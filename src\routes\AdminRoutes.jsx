import { Route, Routes, Navigate } from 'react-router-dom';
import { useState } from 'react';

import { DesignSpaceProvider } from '@contexts/DesignSpaceContext';

import SettingsIndex from '@dashboard/Setting';
import EventsIndex from '@dashboard/Events';
import DesignIndex from '@dashboard/DesignSpace';
import CardsIndex from '@dashboard/Cards';
import AllCardsIndex from '@dashboard/All_Cards';
import OrginalBackagesIndex from '../pages/Dashboard/Backages/OrginalBackagesIndex';

import ManagersDataTable from '../pages/Dashboard/Users/<USER>';
import SoldBackagesIndex from '../pages/Dashboard/Backages/SoldBackagesIndex';
import PackagesHistory from '../pages/Dashboard/Backages/PackageHestory';
import BillingHistory from '../pages/Dashboard/Billing';
import Templates from '@dashboard/Templates';
import Companies from '@pages/Admin/Companies';
import Layout from '@pages/Layout';
import SalesManagementDashboard from '@dashboard/permissions_group';
import AdminDashboard from '@pages/Admin/Dashboard';


function AdminRoutes() {
    const [userType] = useState(localStorage.getItem("user_type"));

    console.log('🔍 AdminRoutes - checking access for user_type:', userType);
    
    if (!userType || userType !== 'admin') {
        console.log('❌ AdminRoutes - Access denied. user_type is not admin:', userType);
        return <Navigate to='/login' replace />;
    }
    
    console.log('✅ AdminRoutes - Access granted for admin user');

    return (
        <Routes>
            {/* Redirect from /admin to /admin/dashboard */}
            <Route path='/' element={<Navigate to='/admin/dashboard' replace />} />
            
            {/* Admin Dashboard */}
            <Route path='/dashboard' element={<Layout><AdminDashboard /></Layout>} />
            
            {/* Admin Settings */}
            <Route path='/settings' element={<Layout><SettingsIndex /></Layout>} />
            
            {/* Admin Events */}
            <Route path='/events' element={<Layout><EventsIndex /></Layout>} />
            
            {/* Admin Cards */}
            <Route path='/c_types' element={<Layout><CardsIndex /></Layout>} />
            <Route path='/cards' element={<Layout><AllCardsIndex /></Layout>} />
            
            {/* Admin Packages */}
            <Route path='/Packages' element={<Layout><OrginalBackagesIndex /></Layout>} />
            <Route path='/sold_P' element={<Layout><SoldBackagesIndex /></Layout>} />
            
            {/* Admin Managers */}
            <Route path="/managers" element={<Layout><ManagersDataTable /></Layout>} />
            
            {/* Admin Packages History */}
            <Route path="/:userId/packages-history" element={<Layout><PackagesHistory /></Layout>} />
            
            {/* Admin Companies */}
            <Route path='/companies' element={<Layout><Companies /></Layout>} />
            
            {/* Admin Permissions Groups */}
            <Route path='/permissions_groups' element={<Layout><SalesManagementDashboard /></Layout>} />
            
            {/* Admin Billing */}
            <Route path='/billing' element={<Layout><BillingHistory /></Layout>} />
            
            {/* Admin Design Space */}
            <Route path='/design-space/:id?' element={
                <Layout>
                    <DesignSpaceProvider>
                        <DesignIndex />
                    </DesignSpaceProvider>
                </Layout>
            } />
            
            {/* Admin Template Design */}
            <Route path='/template-design' element={
                <Layout>
                    <DesignSpaceProvider>
                        <Templates />
                    </DesignSpaceProvider>
                </Layout>
            } />
            
            {/* Catch all - redirect to dashboard */}
            <Route path="*" element={<Navigate to='/admin/dashboard' replace />} />
        </Routes>
    );
}

export default AdminRoutes;
