import { Route, Routes } from 'react-router-dom';

import { DesignSpaceProvider } from '@contexts/DesignSpaceContext';

import SettingsIndex from '@dashboard/Setting';
import MembersIndex from '@dashboard/Users';
import MyBackagesIndex from '../pages/Dashboard/Backages/MyBackagesIndex';
import OrginalBackagesIndex from '../pages/Dashboard/Backages/OrginalBackagesIndex';
import AllCardsIndex from '@dashboard/All_Cards';
import GroupMembersPage from '../pages/Dashboard/Users/<USER>/GroupMembersPage';
import BillingHistory from '../pages/Dashboard/Billing';

import EventsIndex from '@dashboard/Events';
import DesignIndex from '@dashboard/DesignSpace';
import PermissionsGroupIndex from '@dashboard/permissions_group';
import Templates from '@dashboard/Templates';
import Dashboard from '@dashboard';

import Companies from '@pages/Admin/Companies';

import Layout from '@pages/Layout';
import { MembersDataTableProvider } from '@contexts/MembersDataTableContext';

function CompanyRoutes() {
    return (
        <Routes>
            <Route path='/manager/dashboard' element={<Layout><Dashboard /></Layout>} />
            <Route path='/manager/settings' element={<Layout><SettingsIndex /></Layout>} />
            <Route path='/users/:type?' element={<Layout><MembersIndex /></Layout>} />
            <Route path='/manager/users/groups' element={<Layout><></></Layout>} />
            <Route path="/members/group" element={ 
                <Layout>
                    <MembersDataTableProvider>
                        <GroupMembersPage />
                    </MembersDataTableProvider>
                </Layout> 
            } />
            <Route path='/manager/events' element={<Layout><EventsIndex /></Layout>} />
            <Route path='/manager/permissions_group' element={<Layout><PermissionsGroupIndex/></Layout>} />
            <Route path='/manager/companies' element={<Layout><Companies /></Layout>} />
            <Route path='/manager/Packages' element={<Layout><OrginalBackagesIndex /></Layout>} />
            <Route path='/manager/my_cards' element={<Layout><MyBackagesIndex /></Layout>} />
            <Route path='/manager/cards' element={<Layout><AllCardsIndex /></Layout>} />
            <Route path='/manager/billing' element={<Layout><BillingHistory /></Layout>} />
            <Route path='/manager/design-space/:id?' element={
                <DesignSpaceProvider>
                    <DesignIndex />
                </DesignSpaceProvider>
            } />
            <Route path='/manager/template-design' element={
                <Layout>
                    <DesignSpaceProvider>
                        <Templates />
                    </DesignSpaceProvider>
                </Layout>
            } />
        </Routes>
    );
}

export default CompanyRoutes;
