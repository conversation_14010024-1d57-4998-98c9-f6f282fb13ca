
import React, { useEffect, useState } from 'react'

import { usersTableConfig, defaultTableConfig } from '@constants';
import { useDataTableContext } from '@contexts/DataTableContext';
import { useGlobalContext } from '@contexts/GlobalContext';
import { useQueryParams } from '@utils/helper';
import AddMemberDialog from '@dashboard/Users/<USER>/AddMemberDialog';
import Container from '@components/Container';

import { DataTable } from 'primereact/datatable';
import { Tooltip } from 'primereact/tooltip';
import { Column } from 'primereact/column';
import { FiEdit } from "react-icons/fi";


const statusStyles = {
    printed: "bg-[#22C55E] ",
    unprinted: "bg-[#64748B] ",
    onProgress: "bg-[#D97706] ",
}

export default function UsersDataTable() {
    const { totalRecords, lazyParams, setLazyParams, data, dataHandler, getDataTable } = useDataTableContext();
    const {  dialogHandler, openDialog } = useGlobalContext();

    const [selectedMember, setSelectedMember] = useState();
    const [actionType, setActionType] = useState("create") // create or update

    const editMember = (data) => {
        setActionType("update")
        setSelectedMember(data);
        dialogHandler("addMember")
    }


    useEffect(() => {
        setLazyParams({
            ...defaultTableConfig,
            ...usersTableConfig,
        })
    }, [])

    // Data Table Body Template
    const actionBodyTemplate = (rowData) => {
        return (
            <div className="d-inline-block text-nowrap">
                {/* Edit */}
                <Tooltip target={`.update-button-${rowData.id}`} showDelay={100} className="fs-8" />
                <button
                    className={`btn btn-sm btn-icon update-button-${rowData.id} me-3`}
                    data-pr-position="bottom"
                    data-pr-tooltip="Update"
                    onClick={() => editMember(rowData)}>
                    <FiEdit />
                </button>

                {/* Delete  */}
                {/* <Tooltip target={`.delete-button-${rowData.id}`} showDelay={100} className="fs-8" />
                <button
                    className={`btn btn-sm btn-icon  delete-button-${rowData.id}`}
                    data-pr-position="bottom"
                    data-pr-tooltip="Delete"
                    onClick={() => confirmDeleteMember(rowData)} >
                    <TfiTrash />
                </button> */}
            </div>
        );
    }

    const profileBodyTemplate = (rowData) => {
        return rowData?.image ?
            <img loading="lazy" src={rowData?.image} width={100} alt="profile" />
            :
            "";
    }

    const statusBodyTemplate = (rowData) => {
        const status = rowData?.print_status || "unprinted";
        return (
            <span className={`text-[white] rounded-[6px] font-bold text-sm py-2 px-3 capitalize ${statusStyles?.[status]}`}>
                {status}
            </span>
        )
    }

    return (
        <Container>
            <div className="w-full mt-8 ">
                <div className='table-responsive text-nowrap'>
                    <DataTable
                        lazy filterDisplay="row"
                        responsiveLayout="stack"
                        breakpoint="960px"
                        dataKey="id"
                        paginator
                        className="table w-full border "
                        value={data}
                        first={lazyParams?.first}
                        rows={lazyParams?.rows}
                        rowsPerPageOptions={[5, 25, 50, 100]}
                        totalRecords={totalRecords}
                        onPage={dataHandler}
                        onSort={dataHandler}
                        sortField={lazyParams?.sortField}
                        sortOrder={lazyParams?.sortOrder}
                        onFilter={dataHandler}
                        filters={lazyParams?.filters}
                        loading={getDataTable?.isLoading}
                        paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} products"
                    >
                        <Column body={profileBodyTemplate} header="Profile Image" className='text-center' />
                        <Column field="name" header="Name" className='text-center' filter sortable />
                        <Column field="user_type" header="User type" className='text-center' filter sortable showFilterMenu={false} />
                        <Column field="position" header="Position" className='text-center' filter sortable showFilterMenu={false} />
                        <Column field="department" header="Department" className='text-center' filter sortable showFilterMenu={false} />
                        <Column field="status" body={statusBodyTemplate} header="Status" className='text-center' showFilterMenu={false} filter sortable />
                        <Column body={actionBodyTemplate} exportable={false} style={{ minWidth: '8rem' }}></Column>
                    </DataTable>
                </div>

                {openDialog?.addMember ? (
                    <AddMemberDialog
                        data={selectedMember}
                        actionType={actionType}
                        onSuccess={() => {
                            // Refresh data table
                            setLazyParams(prev => ({ ...prev, ...usersTableConfig }));
                        }}
                    />
                ) : <></>}

            </div>
        </Container>
    )
}


