/* Card Carousel Styles */
:root {
  --main-color: #00c3ac;
  --main-color-hover: #02aa96;
  --gray: #dcdcdc;
}

.card-carousel {
  width: 100%;
  position: relative;
}

.carousel-container {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  background: transparent;
}

.carousel-track {
  display: flex;
  gap: 24px;
  transition: transform 0.3s ease;
  will-change: transform;
}

.carousel-item {
  flex-shrink: 0;
  display: flex;
  justify-content: center;
}

.carousel-nav-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  /* Prevent blur during animations */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  will-change: transform;
}

.carousel-nav-btn:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.carousel-nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.carousel-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.carousel-indicator:hover {
  transform: scale(1.2);
}

.carousel-indicator.active {
  transform: scale(1.3);
}

/* Card Preview Styles */
.card-preview-container {
   background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  /* Prevent blur during animations */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  will-change: transform; 
}

.card-preview-container:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.card-preview-container.selected {
  border: 2px solid var(--main-color);
  box-shadow: 0 8px 32px rgba(0, 195, 172, 0.2);
}

.card-visual {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  margin-bottom: 16px;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .card-carousel {
    padding: 0 10px;
  }

  .carousel-track {
    gap: 20px;
  }

  .carousel-nav-btn {
    width: 40px;
    height: 40px;
  }

  .card-preview-container {
    padding: 16px;
    border-radius: 12px;
    min-height: 260px;
    max-height: 300px;
  }

  .card-visual {
    margin-bottom: 12px;
  }

  /* Touch-friendly indicators */
  .carousel-indicator {
    width: 12px;
    height: 12px;
    margin: 0 4px;
  }

  /* Swipe hint animation */
  .swipe-hint {
    animation: swipeHint 2s ease-in-out infinite;
  }

  @keyframes swipeHint {
    0%, 100% { transform: translateX(0); }
    50% { transform: translateX(10px); }
  }
}

@media (max-width: 480px) {
  .card-carousel {
    padding: 0 5px;
  }

  .carousel-track {
    gap: 16px;
  }

  .card-preview-container {
    padding: 12px;
    min-height: 240px;
    max-height: 280px;
  }

  .carousel-nav-btn {
    width: 36px;
    height: 36px;
  }
}

/* Tablet Styles */
@media (min-width: 769px) and (max-width: 1024px) {
  .carousel-track {
    gap: 22px;
  }

  .card-preview-container {
    padding: 18px;
    min-height: 280px;
    max-height: 320px;
  }
}

/* Desktop Styles */
@media (min-width: 1025px) {
  .carousel-track {
    gap: 24px;
  }

  .card-preview-container {
    padding: 20px;
    min-height: 300px;
    max-height: 360px;
  }

  .carousel-nav-btn {
    width: 52px;
    height: 52px;
  }
}

/* Loading Animation */
.carousel-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid var(--main-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Accessibility */
.carousel-nav-btn:focus,
.carousel-indicator:focus,
.card-preview-container:focus {
  outline: 2px solid var(--main-color);
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .carousel-track,
  .card-preview-container,
  .carousel-nav-btn,
  .carousel-indicator {
    transition: none;
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card-preview-container {
    border: 2px solid #000;
  }

  .carousel-nav-btn {
    border: 2px solid #000;
  }

  .carousel-indicator {
    border: 1px solid #000;
  }
}
