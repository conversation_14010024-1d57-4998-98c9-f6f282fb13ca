import { useState, useEffect } from 'react';
import { Dropdown } from 'primereact/dropdown';
import axiosInstance from '../../../../config/Axios';

import { useDesignSpace } from '@contexts/DesignSpaceContext';
import { fieldsOptions } from '@constants/DesignSpaceConfig';
import { measureText } from './TextSettings';

function FieldsDropdown() {
    const { addElement, getActiveTextStyle } = useDesignSpace();
    const [selectedField, setSelectedField] = useState("");
    const [dynamicFieldsOptions, setDynamicFieldsOptions] = useState(fieldsOptions);
    const [isLoadingFields, setIsLoadingFields] = useState(false);

    // جلب أسماء الـ custom fields المخصصة من الـ API
    const fetchCustomFieldNames = async () => {
        setIsLoadingFields(true);
        try {
            const response = await axiosInstance.get('/custom-field-names');
            if (response.data.success) {
                const customFieldNames = response.data.data;
                
                // إنشاء خريطة للأسماء المخصصة
                const customNamesMap = {};
                customFieldNames.forEach(field => {
                    customNamesMap[field.field_key] = field.field_name;
                });

                // تحديث القائمة مع الأسماء المخصصة
                const updatedFieldsOptions = fieldsOptions.map(field => {
                    if (field.value.startsWith('custom_field_')) {
                        const customName = customNamesMap[field.value];
                        return {
                            ...field,
                            label: customName || field.value // استخدم الاسم المخصص إذا وجد، وإلا استخدم المفتاح الأصلي
                        };
                    }
                    return field;
                });

                setDynamicFieldsOptions(updatedFieldsOptions);
            }
        } catch (error) {
            console.error('Error fetching custom field names:', error);
            // في حالة الخطأ، استخدم القائمة الافتراضية
            setDynamicFieldsOptions(fieldsOptions);
        } finally {
            setIsLoadingFields(false);
        }
    };

    // جلب الأسماء عند تحميل المكون
    useEffect(() => {
        fetchCustomFieldNames();
    }, []);

    // الاستماع لتحديث الـ custom field names
    useEffect(() => {
        const handleCustomFieldsUpdate = () => {
            fetchCustomFieldNames();
        };

        // إضافة مستمع للحدث
        window.addEventListener('customFieldsUpdated', handleCustomFieldsUpdate);

        // تنظيف المستمع عند إلغاء تحميل المكون
        return () => {
            window.removeEventListener('customFieldsUpdated', handleCustomFieldsUpdate);
        };
    }, []);

    const onChangeHandler = (val) => {
        const activeStyle = getActiveTextStyle && getActiveTextStyle();
        let customProps = {};
        if (activeStyle) {
            const pureStyle = JSON.parse(JSON.stringify(activeStyle));
            delete pureStyle.value;
            delete pureStyle.id;
            customProps = { ...pureStyle };
        }

        // البحث عن الاسم المخصص للـ custom field
        let displayText = val;
        if (val.startsWith('custom_field_')) {
            const customField = dynamicFieldsOptions.find(field => field.value === val);
            // استخدم الاسم المخصص إذا كان موجوداً، وإلا استخدم المفتاح الأصلي
            if (customField) {
                displayText = customField.label; // سيكون إما الاسم المخصص أو المفتاح الأصلي
            }
        }

        // قياس أبعاد النص بناءً على style
        const { width, height } = measureText(
            displayText,
            customProps.fontSize || 16,
            customProps.fontFamily || 'Arial, sans-serif',
            customProps.fontWeight || 'normal',
            customProps.fontStyle || 'normal',
            customProps.lineHeight || 1.2
        );
        customProps.width = Math.max(width, 40);
        customProps.height = Math.max(height, 24);
        
        // إضافة المفتاح الأصلي كـ metadata للعنصر
        customProps.originalFieldKey = val;
        addElement("text", displayText, customProps);
        setSelectedField("");
    }
 
    return (
        <>
            <Dropdown
                defaultValue={dynamicFieldsOptions[0]?.dimension}
                className='rounded-[6px] me-3 text-[black] w-full'
                optionLabel="label"
                optionValue="value"
                value={selectedField}
                options={dynamicFieldsOptions}
                onChange={(e) => { setSelectedField(e.value); onChangeHandler(e.value); }}
                placeholder={isLoadingFields ? "Loading fields..." : "select field ..."}
                disabled={isLoadingFields}
            />
        </>
    )
}

export default FieldsDropdown