import PropTypes from "prop-types";
import { useContext, createContext, useEffect, useState, useRef, useMemo, useCallback } from "react";
import { useGetManagersDataTable } from "../quires/UsersDatatable";

const ManagerDataTableContext = createContext({});

export const DataTableProvider = ({ children }) => {
    const getDataTable = useGetManagersDataTable();
    const didMountRef = useRef(false);
    const loadLazyTimeout = useRef(null);

    const [reload, setReload] = useState(false);
    const [loading, setLoading] = useState(false);
    const [totalRecords, setTotalRecordsRaw] = useState(0);
    const [data, setDataRaw] = useState([]);
    const [lazyManagersParams, setLazyManagersParamsRaw] = useState({
        url: "datatable/managers",
        first: 0,
        rows: 10,
        page: 0,
        sortField: 'id',
        sortOrder: 1,
        filters: {},
    });

    // Keep a ref for latest params
    const lazyManagersParamsRef = useRef(lazyManagersParams);
    useEffect(() => {
        lazyManagersParamsRef.current = lazyManagersParams;
    }, [lazyManagersParams]);

    // Prevent unnecessary updates for params
    const setLazyManagersParams = useCallback((updater) => {
        setLazyManagersParamsRaw(prev => {
            const next = typeof updater === 'function' ? updater(prev) : updater;
            if (JSON.stringify(prev) === JSON.stringify(next)) {
                console.log('[setLazyManagersParams] No change in params');
                return prev;
            }
            console.log('[setLazyManagersParams] Params changed:', next);
            return next;
        });
    }, []);

    // Prevent unnecessary updates for data
    const setData = useCallback((next) => {
        setDataRaw(prev => {
            if (JSON.stringify(prev) === JSON.stringify(next)) return prev;
            return next;
        });
    }, []);

    // Prevent unnecessary updates for totalRecords
    const setTotalRecords = useCallback((next) => {
        setTotalRecordsRaw(prev => {
            if (prev === next) return prev;
            return next;
        });
    }, []);

    // Update params with shallow compare
    const updateLazyManagersParams = useCallback((event) => {
        setLazyManagersParams(prev => {
            const next = { ...prev, ...event };
            if (JSON.stringify(prev) === JSON.stringify(next)) return prev;
            return next;
        });
    }, [setLazyManagersParams]);

    // loadLazyData now always uses the latest params from ref
    const loadLazyData = useCallback(() => {
        setLoading(true);
        if (loadLazyTimeout.current) {
            clearTimeout(loadLazyTimeout.current);
        }
        loadLazyTimeout.current = setTimeout(async () => {
            try {
                const params = lazyManagersParamsRef.current;
                console.log("📡 Loading managers data with params:", params);
                
                if (!params.url) {
                    console.warn("⚠️ No URL provided, skipping data load");
                    setLoading(false);
                    return;
                }
                
                const response = await getDataTable.mutateAsync(params);
                console.log("📡 Managers data response:", response);
                
                if (response && response.data) {
                    setTotalRecords(response.pagination?.total || 0);
                    setData(response.data);
                } else {
                    console.warn("⚠️ Invalid response format:", response);
                    setData([]);
                    setTotalRecords(0);
                }
            } catch (err) {
                console.error("❌ Error loading managers data:", err);
                if (err.response?.status >= 500) {
                    console.log("🔄 Retrying after server error...");
                    setTimeout(() => {
                        loadLazyData();
                    }, 1000);
                } else {
                    setData([]);
                    setTotalRecords(0);
                }
            } finally {
                setLoading(false);
            }
        }, 200);
    }, [getDataTable, setTotalRecords, setData]);

    useEffect(() => {
        if (didMountRef.current) {
            loadLazyData();
        } else {
            didMountRef.current = true;
            console.log("🚀 First mount, loading initial data");
            loadLazyData();
        }
    }, [lazyManagersParams]);

    useEffect(() => {
        if (reload) {
            console.log("🔄 Reload triggered");
            loadLazyData();
            setReload(false);
        }
    }, [reload, loadLazyData]);

    useEffect(() => {
        return () => {
            setData([]);
            setReload(false);
            setTotalRecords(0);
        };
    }, [setData, setTotalRecords]);

    const safeSetReload = useCallback((v) => setReload(v), []);
    const safeSetLoading = useCallback((v) => setLoading(v), []);
    const safeSetTotalRecords = useCallback((v) => setTotalRecordsRaw(v), []);
    const safeSetData = useCallback((v) => setDataRaw(v), []);

    // Memoize context value to prevent unnecessary renders
    const contextValue = useMemo(() => ({
        totalRecords,
        setTotalRecords: safeSetTotalRecords,
        lazyManagersParams,
        setLazyManagersParams,
        data,
        setData: safeSetData,
        loading,
        setLoading: safeSetLoading,
        setReload: safeSetReload,
        getDataTable,
        dataHandler: updateLazyManagersParams,
        onPage: updateLazyManagersParams,
        onSort: updateLazyManagersParams,
        onFilter: updateLazyManagersParams
    }), [totalRecords, lazyManagersParams, setLazyManagersParams, data, loading, getDataTable, updateLazyManagersParams]);

    return (
        <ManagerDataTableContext.Provider value={contextValue}>
            {children}
        </ManagerDataTableContext.Provider>
    );
};

DataTableProvider.propTypes = {
    children: PropTypes.node.isRequired,
};

export const useDataTableContext = () => useContext(ManagerDataTableContext);
