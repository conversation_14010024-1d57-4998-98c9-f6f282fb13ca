import React, { useState } from 'react';
import { cn } from '../../utils/cn';
import { Avatar, AvatarImage, AvatarFallback } from './avatar';

const AvatarGroup = React.forwardRef(({ className, children, max = 5, ...props }, ref) => {
  const avatars = React.Children.toArray(children);
  const totalAvatars = avatars.length;
  const displayAvatars = avatars.slice(0, max);
  const remainingCount = totalAvatars - max;

  return (
    <div
      ref={ref}
      className={cn("flex -space-x-2", className)}
      {...props}
    >
      {displayAvatars.map((avatar, index) => (
        <div key={index} className="relative">
          {React.cloneElement(avatar, {
            className: cn(
              "border-2 border-white",
              avatar.props.className
            )
          })}
        </div>
      ))}
      {remainingCount > 0 && (
        <div className="relative">
          <Avatar className="border-2 border-white bg-gray-300">
            <AvatarFallback className="text-xs font-medium text-gray-700">
              +{remainingCount}
            </AvatarFallback>
          </Avatar>
        </div>
      )}
    </div>
  );
});
AvatarGroup.displayName = "AvatarGroup";

const AvatarGroupTooltip = React.forwardRef(({ className, children, ...props }, ref) => {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <div
      ref={ref}
      className="relative"
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
      {...props}
    >
      {children}
      {isVisible && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded shadow-lg z-50 whitespace-nowrap">
          {children}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
        </div>
      )}
    </div>
  );
});
AvatarGroupTooltip.displayName = "AvatarGroupTooltip";

export { AvatarGroup, AvatarGroupTooltip }; 