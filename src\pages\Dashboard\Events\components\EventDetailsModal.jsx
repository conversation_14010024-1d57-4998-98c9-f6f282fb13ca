import React from 'react';
import { Dialog } from 'primereact/dialog';
import { Button } from 'primereact/button';
import { Tag } from 'primereact/tag';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { motion } from 'framer-motion';
import { FiCalendar, FiMapPin, FiUsers, FiClock, FiEdit, FiUserPlus } from 'react-icons/fi';
import { useLayout } from '@contexts/LayoutContext';
import { eventStatuses, cardStatuses } from '@data/mockEventsData';

const EventDetailsModal = ({ visible, onHide, event, onEdit, onAssignCards }) => {
    const { isMobile } = useLayout();

    if (!event) return null;

    const eventStatus = eventStatuses.find(s => s.value === event.status);
    
    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    const formatTime = (timeString) => {
        return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
    };

    // Template for card assignment table
    const memberNameTemplate = (rowData) => (
        <div className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
                <span className="text-white text-sm font-medium">
                    {rowData.memberName?.charAt(0)?.toUpperCase()}
                </span>
            </div>
            <div>
                <div className="font-medium">{rowData.memberName}</div>
                <div className="text-sm text-gray-500">{rowData.memberEmail}</div>
            </div>
        </div>
    );

    const cardTypeTemplate = (rowData) => (
        <div className="flex items-center gap-2">
            <div 
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: '#4A90E2' }} // Default color, in real app get from card type
            />
            <span>{rowData.cardType}</span>
        </div>
    );

    const statusTemplate = (rowData) => {
        const status = cardStatuses.find(s => s.value === rowData.status);
        return (
            <Tag 
                value={status?.label} 
                style={{ backgroundColor: status?.color }}
                className="text-white px-2 py-1 rounded-full text-xs"
            />
        );
    };

    const validityTemplate = (rowData) => (
        <div className="text-sm">
            <div>{new Date(rowData.validFrom).toLocaleDateString()}</div>
            <div className="text-gray-500">to {new Date(rowData.validUntil).toLocaleDateString()}</div>
        </div>
    );

    const renderHeader = () => (
        <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-gray-800">Event Details</h2>
            <div className="flex gap-2">
                <Button
                    icon={<FiEdit size={16} />}
                    label={isMobile ? "" : "Edit"}
                    className="p-button-outlined p-button-sm"
                    onClick={() => {
                        onEdit(event);
                        onHide();
                    }}
                />
                <Button
                    icon={<FiUserPlus size={16} />}
                    label={isMobile ? "" : "Assign Cards"}
                    className="main-btn p-button-sm"
                    onClick={() => {
                        onAssignCards(event);
                        onHide();
                    }}
                />
            </div>
        </div>
    );

    return (
        <Dialog
            header={renderHeader()}
            visible={visible}
            style={isMobile ? { width: "95vw", height: "90vh" } : { width: "80vw", minWidth: '800px' }}
            breakpoints={isMobile ? {} : { '960px': '90vw', '641px': '95vw' }}
            modal
            onHide={onHide}
            maximizable={false}
            resizable={false}
            contentStyle={{ maxHeight: isMobile ? 'calc(90vh - 120px)' : 'auto', overflow: 'auto' }}
        >
            <div className="space-y-6">
                {/* Event Overview */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="bg-gray-50 p-4 rounded-lg"
                >
                    <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                            <h3 className="text-2xl font-bold text-gray-900 mb-2">{event.name}</h3>
                            <p className="text-gray-600 leading-relaxed">{event.description}</p>
                        </div>
                        <Tag 
                            value={eventStatus?.label} 
                            style={{ backgroundColor: eventStatus?.color }}
                            className="text-white px-3 py-1 rounded-full text-sm font-medium ml-4"
                        />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div className="flex items-center gap-3 p-3 bg-white rounded-lg">
                            <div className="p-2 bg-blue-100 rounded-lg">
                                <FiCalendar className="text-blue-600" size={20} />
                            </div>
                            <div>
                                <div className="text-sm text-gray-500">Start Date</div>
                                <div className="font-medium">{formatDate(event.startDate)}</div>
                                <div className="text-sm text-gray-600">{formatTime(event.startTime)}</div>
                            </div>
                        </div>

                        <div className="flex items-center gap-3 p-3 bg-white rounded-lg">
                            <div className="p-2 bg-green-100 rounded-lg">
                                <FiCalendar className="text-green-600" size={20} />
                            </div>
                            <div>
                                <div className="text-sm text-gray-500">End Date</div>
                                <div className="font-medium">{formatDate(event.endDate)}</div>
                                <div className="text-sm text-gray-600">{formatTime(event.endTime)}</div>
                            </div>
                        </div>

                        <div className="flex items-center gap-3 p-3 bg-white rounded-lg">
                            <div className="p-2 bg-purple-100 rounded-lg">
                                <FiMapPin className="text-purple-600" size={20} />
                            </div>
                            <div>
                                <div className="text-sm text-gray-500">Location</div>
                                <div className="font-medium">{event.location}</div>
                            </div>
                        </div>

                        <div className="flex items-center gap-3 p-3 bg-white rounded-lg">
                            <div className="p-2 bg-orange-100 rounded-lg">
                                <FiUsers className="text-orange-600" size={20} />
                            </div>
                            <div>
                                <div className="text-sm text-gray-500">Attendees</div>
                                <div className="font-medium">{event.currentAttendees}/{event.maxAttendees}</div>
                                <div className="w-full h-2 bg-gray-200 rounded-full mt-1">
                                    <div 
                                        className="h-full bg-orange-500 rounded-full transition-all duration-300"
                                        style={{ width: `${Math.min((event.currentAttendees / event.maxAttendees) * 100, 100)}%` }}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </motion.div>

                {/* Event Metadata */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 }}
                    className="grid grid-cols-1 md:grid-cols-3 gap-4"
                >
                    <div className="bg-white p-4 rounded-lg border border-gray-200">
                        <div className="flex items-center gap-2 mb-2">
                            <FiClock className="text-gray-500" size={16} />
                            <span className="text-sm font-medium text-gray-700">Duration</span>
                        </div>
                        <div className="text-lg font-semibold text-gray-900">{event.duration}</div>
                    </div>

                    <div className="bg-white p-4 rounded-lg border border-gray-200">
                        <div className="text-sm font-medium text-gray-700 mb-2">Created By</div>
                        <div className="text-lg font-semibold text-gray-900">{event.createdBy}</div>
                        <div className="text-sm text-gray-500">
                            {new Date(event.createdAt).toLocaleDateString()}
                        </div>
                    </div>

                    <div className="bg-white p-4 rounded-lg border border-gray-200">
                        <div className="text-sm font-medium text-gray-700 mb-2">Temporary Cards</div>
                        <div className="text-lg font-semibold text-gray-900">
                            {event.temporaryCards?.length || 0}
                        </div>
                        <div className="text-sm text-gray-500">cards assigned</div>
                    </div>
                </motion.div>

                {/* Temporary Card Assignments */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.2 }}
                    className="bg-white rounded-lg border border-gray-200"
                >
                    <div className="p-4 border-b border-gray-200">
                        <div className="flex items-center justify-between">
                            <h4 className="text-lg font-semibold text-gray-900">Temporary Card Assignments</h4>
                            <Button
                                label="Assign More Cards"
                                icon={<FiUserPlus size={14} />}
                                className="main-btn p-button-sm"
                                onClick={() => {
                                    onAssignCards(event);
                                    onHide();
                                }}
                            />
                        </div>
                    </div>

                    {event.temporaryCards && event.temporaryCards.length > 0 ? (
                        <DataTable
                            value={event.temporaryCards}
                            className="border-t-0"
                            emptyMessage="No temporary cards assigned"
                            responsiveLayout="stack"
                            breakpoint="960px"
                        >
                            <Column body={memberNameTemplate} header="Member" />
                            <Column body={cardTypeTemplate} header="Card Type" />
                            <Column body={statusTemplate} header="Status" />
                            <Column body={validityTemplate} header="Valid Period" />
                        </DataTable>
                    ) : (
                        <div className="p-8 text-center">
                            <div className="text-gray-400 mb-2">
                                <FiUserPlus size={48} className="mx-auto" />
                            </div>
                            <h5 className="text-lg font-medium text-gray-600 mb-2">No Cards Assigned</h5>
                            <p className="text-gray-500 mb-4">
                                No temporary cards have been assigned to this event yet.
                            </p>
                            <Button
                                label="Assign Cards"
                                icon={<FiUserPlus size={16} />}
                                className="main-btn"
                                onClick={() => {
                                    onAssignCards(event);
                                    onHide();
                                }}
                            />
                        </div>
                    )}
                </motion.div>
            </div>
        </Dialog>
    );
};

export default EventDetailsModal;
