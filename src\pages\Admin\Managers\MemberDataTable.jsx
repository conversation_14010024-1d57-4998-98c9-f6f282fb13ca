
import React, { useEffect, useState } from 'react'
import { isEmpty } from 'lodash';
import parse from 'html-react-parser';

import TemplateImageHeader from './TemplateFilter';
import AssignGroupDialog from '../Groups/AssignGroupDialog';
import AddMemberDialog from './AddMemberDialog';

import { usersTableConfig, defaultTableConfig } from '@constants';
import { useDeleteUserMutation } from '@quires/user';
import { useDataTableContext } from '@contexts/DataTableContext';
import { useGlobalContext } from '@contexts/GlobalContext';
import { useQueryParams } from '@utils/helper';

import { DataTable } from 'primereact/datatable';
import { Tooltip } from 'primereact/tooltip';
import { Column } from 'primereact/column';
 
import { TfiTrash } from "react-icons/tfi";
import { FiEdit } from "react-icons/fi";
import Element from '../../DesignSpace/components/Element';
 
const statusStyles = {
    printed: "bg-[#22C55E] ",
    unprinted: "bg-[#64748B] ",
    onProgress: "bg-[#D97706] ",
}

function MemberDataTable() {
    const { totalRecords, lazyParams, setLazyParams, data, dataHandler, loading } = useDataTableContext();
    const { dialogHandler, openDialog, selectedMembers, setSelectedMembers } = useGlobalContext();
    const queryParams = useQueryParams();
    const groupID = queryParams.get("group-id");
    const designID = queryParams.get("design-id");
    const deleteRow = useDeleteUserMutation();

    const [selectedMember, setSelectedMember] = useState();
    const [actionType, setActionType] = useState("create") // create or update

    useEffect(() => {
        setLazyParams({
            ...defaultTableConfig,
            ...usersTableConfig,
            groupID: groupID,
            designID: designID
        })
    }, [])

    //Dialog Handler
    const createMember = () => {
        setActionType("create")
        setSelectedMember({});
        dialogHandler("addMember");
    }

    const createGroup = () => {
        setSelectedMember({});
        dialogHandler("createGroup");
    }

    // const editMember = (data) => {
    //     setActionType("update")
    //     setSelectedMember(data);
    //     dialogHandler("addMember")
    // }

    const editMember = (data) => {
        setActionType("update");
    
        const updatedData = { ...data };
        delete updatedData.role;
        delete updatedData.group_permission; 
        delete updatedData.design;
        setSelectedMember(updatedData);
        dialogHandler("addMember");
    }

    const deleteRowHandler = async (rowData) => {
        await deleteRow.mutateAsync({
            id: rowData?.id,
        }, {
            onSuccess: () => {
                setLazyParams(prev => ({ ...prev, ...usersTableConfig }))
            }
        })
    }

    // Data Table Body Template
    
    const actionBodyTemplate = (rowData) => {
        const currentUserId = localStorage.getItem('user_id'); 


        return (
            <>
                <div className="d-inline-block text-nowrap">
                    {/* Edit */}
                    <Tooltip target={`.update-button-${rowData.id}`} showDelay={100} className="fs-8" />
                    <button
                        className={`btn btn-sm btn-icon update-button-${rowData.id} me-3`}
                        data-pr-position="bottom"
                        data-pr-tooltip="Update"
                        onClick={() => editMember(rowData)}>
                        <FiEdit />
                    </button>

                    {/* Delete */}
                    {rowData.id.toString() !== currentUserId && ( 
                        <>
                            <Tooltip target={`.delete-button-${rowData.id}`} showDelay={100} className="fs-8" />
                            <button
                                className={`btn btn-sm btn-icon delete-button-${rowData.id}`}
                                data-pr-position="bottom"
                                data-pr-tooltip="Delete"
                                onClick={() => deleteRowHandler(rowData)} >
                                <TfiTrash />
                            </button>
                        </>
                    )}
                </div>
            </>
        );

    }
    
    
    

    const selectAllHandler = (rowsData) => {
        setSelectedMembers((prev) => ({ ...prev, data: rowsData }));
    }
  

    const imageBodyTemplate = (rowData) => {
        
        if (rowData?.tempate_image_html) {
            return <div className="border border-black rounded-md ">{parse(rowData?.tempate_image_html)}</div>
           
            
        }
        return
        if (rowData?.design) {
            const scaleFactor = 1
            const elements = JSON.parse(rowData?.design?.init_template)
            return (
                <div style={{ width: `${240 * scaleFactor}px`, height: `${416 * scaleFactor}px`, borderRadius: "5px", border: "1px solid black", position: "relative" }}>
                    {
                        elements.map(el => {
                            return (
                                <div
                                    key={el.id}
                                    style={{
                                        background: "transparent",
                                        position: "absolute",
                                        top: el.y * scaleFactor,
                                        left: el.x * scaleFactor,
                                        width: el.width * scaleFactor,
                                        height: el.height * scaleFactor,
                                    }}
                                >
                                    <Element el={el} scaleFactor={scaleFactor} userData={rowData} />
                                </div>
                            )
                        })}
                </div >
            )
        }

    }

    const profileBodyTemplate = (rowData) => {
        return rowData?.image ?
            <img loading="lazy" src={rowData?.image} width={100} alt="profile" />
            :
            "";
    }

    const statusBodyTemplate = (rowData) => {
        const status = rowData?.print_status || "unprinted";
        return (
            <span className={`text-[white] rounded-[6px] font-bold text-sm py-2 px-3 capitalize ${statusStyles?.[status]}`}>
                {status}
            </span>
        )
    }



    
    const Header = () => {
        const activeBtn = isEmpty(selectedMembers.data);
        const userRole = localStorage.getItem('user_role'); 
        return (

            
            <div className="w-full flex justify-between">
                < TemplateImageHeader />
                <div>
                    {
                        selectedMembers.action === "update" ?
                            <button
                                className={`${activeBtn ? "gray-btn " : "main-btn"} text-md me-2 shadow-md`}
                                disabled={activeBtn} onClick={() => dialogHandler("updateGroup")}>
                                Update Group
                            </button>
                            :
                            <button
                                className={`${activeBtn ? "gray-btn " : "main-btn"} text-md me-2 shadow-md`}
                                disabled={activeBtn} onClick={() => createGroup()}>
                                Create Group
                            </button>
                    }

                    {userRole !== "user" && (
                        <button className="main-btn text-md shadow-md" onClick={() => createMember()}>
                            Add Member
                        </button>
                    )}
                </div>
            </div>
        )
    }
    
    
    

    return (
        <>
            <div className="w-full mt-8 ">
                <div className='table-responsive text-nowrap'>
                    <DataTable
                        selection={selectedMembers.data}
                        onSelectionChange={(e) => selectAllHandler(e.value)}
                        lazy filterDisplay="row"
                        header={Header}
                        responsiveLayout="stack"
                        breakpoint="960px"
                        dataKey="id"
                        paginator
                        className="table w-full border "
                        value={data}
                        first={lazyParams?.first}
                        rows={lazyParams?.rows}
                        rowsPerPageOptions={[5, 25, 50, 100]}
                        totalRecords={totalRecords}
                        onPage={dataHandler}
                        onSort={dataHandler}
                        sortField={lazyParams?.sortField}
                        sortOrder={lazyParams?.sortOrder}
                        onFilter={dataHandler}
                        filters={lazyParams?.filters}
                        loading={loading}
                        paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} products"
                    >
                        <Column selectionMode="multiple" headerStyle={{ width: '3rem' }} exportable={false}></Column>
                        <Column body={profileBodyTemplate} header="Profile Image" className='text-center' />
                        <Column body={imageBodyTemplate} header="Template Image" className='text-center' />
                        <Column field="name" header="Name" className='text-center' filter sortable />
                        <Column field="email" header="Email" className='text-center' filter sortable showFilterMenu={false} />
                        
                        <Column field="role" header="Role" className='text-center' filter sortable body={(rowData) => rowData.role ? rowData.role : 'user'} />  
                        <Column field="type" header="Type" className='text-center' filter sortable showFilterMenu={false} />
                        <Column field="position" header="Position" className='text-center' filter sortable showFilterMenu={false} />
                        <Column field="department" header="Department" className='text-center' filter sortable showFilterMenu={false} />
                        <Column field="status" body={statusBodyTemplate} header="Status" className='text-center' showFilterMenu={false} filter sortable />
                        <Column body={actionBodyTemplate} exportable={false} style={{ minWidth: '8rem' }}></Column>
                    </DataTable>
                </div>
                {openDialog?.addMember ? (
                    <AddMemberDialog
                        data={selectedMember}
                        actionType={actionType}
                        onSuccess={() => {
                            // Refresh data table
                            setLazyParams(prev => ({ ...prev, ...usersTableConfig }));
                        }}
                    />
                ) : <></>}
                {openDialog?.createGroup ? <AssignGroupDialog data={selectedMembers.data} /> : <></>}
                {openDialog?.updateGroup ? <AssignGroupDialog /> : <></>}
            </div>
        </>
    )
}

export default MemberDataTable
