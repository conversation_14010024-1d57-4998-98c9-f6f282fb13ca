// API Routes for Email Verification in Profile Settings
// This file contains the routes that need to be added to the Laravel backend

/*
Add these routes to your Laravel routes/api.php file:

// Email Verification for Profile Update
Route::post('/users/send-email-verification-for-profile', [UserController::class, 'sendEmailVerificationForProfile']);
Route::post('/users/verify-email-for-profile', [UserController::class, 'verifyEmailForProfile']);
Route::post('/users/resend-email-verification-for-profile', [UserController::class, 'resendEmailVerificationForProfile']);

These routes should be added after the existing user routes in your Laravel application.
*/

export const EMAIL_VERIFICATION_ROUTES = {
    SEND_VERIFICATION: '/users/send-email-verification-for-profile',
    VERIFY_EMAIL: '/users/verify-email-for-profile',
    RESEND_VERIFICATION: '/users/resend-email-verification-for-profile'
};

export const EMAIL_AVAILABILITY_ROUTE = '/users/check-email-availability';

export const USER_ROUTES = {
    UPDATE: '/users',
    CREATE: '/users',
    UPLOAD_IMAGE: '/users/upload-image'
};

export const VALIDATION_MESSAGES = {
    NAME_REQUIRED: 'Name is required',
    NAME_MIN: 'Name must be at least 3 characters long',
    NAME_MAX: 'Name must not exceed 32 characters',
    
    EMAIL_REQUIRED: 'Email is required',
    EMAIL_INVALID: 'Please enter a valid email address',
    EMAIL_MAX: 'Email must not exceed 40 characters',
    EMAIL_UNIQUE: 'This email address is already taken',
    
    PASSWORD_REQUIRED: 'Current password is required',
    
    PHONE_REQUIRED: 'Phone number is required',
    PHONE_INVALID: 'Invalid phone number format for selected country',
    
    POSITION_REQUIRED: 'Position is required',
    POSITION_MIN: 'Position must be at least 2 characters long',
    POSITION_MAX: 'Position must not exceed 50 characters',
    POSITION_REGEX: 'Position cannot contain only numbers and must contain at least one letter',
    
    DEPARTMENT_REQUIRED: 'Department is required',
    DEPARTMENT_MIN: 'Department must be at least 2 characters long',
    DEPARTMENT_MAX: 'Department must not exceed 50 characters',
    DEPARTMENT_REGEX: 'Department cannot contain only numbers and must contain at least one letter',
    
    TYPE_REQUIRED: 'Type is required',
    TYPE_MIN: 'Type must be at least 2 characters long',
    TYPE_MAX: 'Type must not exceed 50 characters',
    TYPE_REGEX: 'Type cannot contain only numbers and must contain at least one letter',
    
    CUSTOM_FIELD_MIN: 'Custom Field must be at least 3 characters long',
    CUSTOM_FIELD_MAX: 'Custom Field must not exceed 32 characters'
};

