import React, { useState, useEffect, useRef } from 'react';
import { Dialog } from 'primereact/dialog';
import { Button } from 'primereact/button';
import { motion } from 'framer-motion';
import { FiMapPin, FiExternalLink, FiCopy, FiCheck } from 'react-icons/fi';
import { Wrapper } from '@googlemaps/react-wrapper';

const LocationModal = ({ visible, onHide, event, isMobile }) => {
    const [mapLoaded, setMapLoaded] = useState(false);
    const [mapError, setMapError] = useState(false);
    const [copied, setCopied] = useState(false);
    const mapRef = useRef(null);
    const googleMapRef = useRef(null);

    // Mock coordinates for demo - in real app, these would come from geocoding the location
    const getLocationCoordinates = (locationName) => {
        const mockCoordinates = {
            'Grand Convention Center': { lat: 40.7589, lng: -73.9851 },
            'Innovation Hub': { lat: 40.7505, lng: -73.9934 },
            'Training Center Room A': { lat: 40.7614, lng: -73.9776 },
            'Luxury Hotel Ballroom': { lat: 40.7549, lng: -73.9840 },
            'Main Auditorium': { lat: 40.7580, lng: -73.9855 }
        };
        
        return mockCoordinates[locationName] || { lat: 40.7589, lng: -73.9851 };
    };

    const coordinates = event?.location ? getLocationCoordinates(event.location) : null;

    useEffect(() => {
        if (visible && coordinates && window.google && mapRef.current) {
            initializeMap();
        }
    }, [visible, coordinates]);

    const initializeMap = () => {
        try {
            const map = new window.google.maps.Map(mapRef.current, {
                center: coordinates,
                zoom: 15,
                mapTypeId: 'roadmap',
                styles: [
                    {
                        featureType: 'poi',
                        elementType: 'labels',
                        stylers: [{ visibility: 'on' }]
                    }
                ]
            });

            // Add marker for the event location
            const marker = new window.google.maps.Marker({
                position: coordinates,
                map: map,
                title: event?.location,
                icon: {
                    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z" fill="#00c3ac"/>
                        </svg>
                    `),
                    scaledSize: new window.google.maps.Size(32, 32),
                    anchor: new window.google.maps.Point(16, 32)
                }
            });

            // Add info window
            const infoWindow = new window.google.maps.InfoWindow({
                content: `
                    <div style="padding: 8px; max-width: 200px;">
                        <h3 style="margin: 0 0 8px 0; font-size: 14px; font-weight: bold;">${event?.name}</h3>
                        <p style="margin: 0; font-size: 12px; color: #666;">${event?.location}</p>
                        <p style="margin: 4px 0 0 0; font-size: 11px; color: #888;">
                            ${new Date(event?.startDate).toLocaleDateString()} at ${event?.startTime}
                        </p>
                    </div>
                `
            });

            marker.addListener('click', () => {
                infoWindow.open(map, marker);
            });

            googleMapRef.current = map;
            setMapLoaded(true);
            setMapError(false);
        } catch (error) {
            console.error('Error initializing map:', error);
            setMapError(true);
        }
    };

    const handleCopyLocation = async () => {
        if (event?.location) {
            try {
                await navigator.clipboard.writeText(event.location);
                setCopied(true);
                setTimeout(() => setCopied(false), 2000);
            } catch (error) {
                console.error('Failed to copy location:', error);
            }
        }
    };

    const handleOpenInMaps = () => {
        if (coordinates) {
            const url = `https://www.google.com/maps/search/?api=1&query=${coordinates.lat},${coordinates.lng}`;
            window.open(url, '_blank');
        }
    };

    const renderMap = () => {
        // For demo purposes, we'll show a placeholder if Google Maps API is not available
        if (!window.google) {
            return (
                <div className="w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                    <div className="text-center">
                        <FiMapPin size={32} className="mx-auto mb-2 text-gray-400" />
                        <p className="text-gray-600">Google Maps API not configured</p>
                        <p className="text-sm text-gray-500">Location: {event?.location}</p>
                    </div>
                </div>
            );
        }

        return (
            <div className="relative">
                <div 
                    ref={mapRef} 
                    className="w-full h-64 rounded-lg border border-gray-200"
                    style={{ minHeight: '256px' }}
                />
                {!mapLoaded && !mapError && (
                    <div className="absolute inset-0 bg-gray-100 rounded-lg flex items-center justify-center">
                        <div className="text-center">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                            <p className="text-gray-600">Loading map...</p>
                        </div>
                    </div>
                )}
                {mapError && (
                    <div className="absolute inset-0 bg-red-50 rounded-lg flex items-center justify-center">
                        <div className="text-center">
                            <FiMapPin size={32} className="mx-auto mb-2 text-red-400" />
                            <p className="text-red-600">Failed to load map</p>
                            <p className="text-sm text-gray-500">Location: {event?.location}</p>
                        </div>
                    </div>
                )}
            </div>
        );
    };

    if (!event) return null;

    return (
        <Dialog
            header={
                <div className="flex items-center gap-2">
                    <FiMapPin className="text-blue-600" size={20} />
                    <span className="text-lg font-semibold">Event Location</span>
                </div>
            }
            visible={visible}
            style={{
                width: isMobile ? '95vw' : '70vw',
                maxWidth: isMobile ? '95vw' : '800px',
                maxHeight: '90vh'
            }}
            breakpoints={{
                '960px': '85vw',
                '641px': '95vw'
            }}
            onHide={onHide}
            className="location-modal"
            contentStyle={{
                maxHeight: isMobile ? 'calc(90vh - 60px)' : 'auto',
                overflow: 'auto',
                padding: isMobile ? '15px' : '20px'
            }}
            modal
        >
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
            >
                {/* Event Information */}
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <div className="text-sm text-blue-800">
                        <p><strong>Event:</strong> {event.name}</p>
                        <p><strong>Location:</strong> {event.location}</p>
                        <p><strong>Date:</strong> {new Date(event.startDate).toLocaleDateString()}</p>
                        <p><strong>Time:</strong> {event.startTime} - {event.endTime}</p>
                    </div>
                </div>

                {/* Map */}
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <h3 className="text-lg font-medium text-gray-900">Map View</h3>
                        <div className="flex gap-2">
                            <Button
                                icon={copied ? <FiCheck size={14} /> : <FiCopy size={14} />}
                                label={isMobile ? "" : (copied ? "Copied!" : "Copy")}
                                className="p-button-sm p-button-outlined"
                                style={{
                                    backgroundColor: copied ? '#22c55e' : 'white',
                                    color: copied ? 'white' : 'black',
                                    border: `1px solid ${copied ? '#22c55e' : '#d1d5db'}`,
                                    padding: isMobile ? '8px' : '6px 12px',
                                    borderRadius: '6px',
                                    transition: 'all 0.2s ease'
                                }}
                                onClick={handleCopyLocation}
                                tooltip="Copy Location"
                                tooltipOptions={{ position: 'top' }}
                            />
                            <Button
                                icon={<FiExternalLink size={14} />}
                                label={isMobile ? "" : "Open in Maps"}
                                className="p-button-sm p-button-outlined"
                                style={{
                                    backgroundColor: 'white',
                                    color: 'black',
                                    border: '1px solid #d1d5db',
                                    padding: isMobile ? '8px' : '6px 12px',
                                    borderRadius: '6px',
                                    transition: 'all 0.2s ease'
                                }}
                                onMouseEnter={(e) => {
                                    if (!isMobile) {
                                        e.target.style.transform = 'translateY(-1px)';
                                        e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
                                    }
                                }}
                                onMouseLeave={(e) => {
                                    if (!isMobile) {
                                        e.target.style.transform = 'translateY(0)';
                                        e.target.style.boxShadow = 'none';
                                    }
                                }}
                                onClick={handleOpenInMaps}
                                tooltip="Open in Google Maps"
                                tooltipOptions={{ position: 'top' }}
                            />
                        </div>
                    </div>
                    
                    {renderMap()}
                </div>

                {/* Additional Information */}
                <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-2">Event Details</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                        <div>
                            <p><strong>Duration:</strong> {event.duration}</p>
                            <p><strong>Status:</strong> <span className="capitalize">{event.status}</span></p>
                        </div>
                        <div>
                            <p><strong>Max Attendees:</strong> {event.maxAttendees}</p>
                            <p><strong>Current Attendees:</strong> {event.currentAttendees}</p>
                        </div>
                    </div>
                </div>
            </motion.div>
        </Dialog>
    );
};

// Wrapper component to handle Google Maps API loading
const LocationModalWrapper = (props) => {
    const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
    
    if (!apiKey) {
        return <LocationModal {...props} />;
    }

    return (
        <Wrapper apiKey={apiKey} libraries={['places']}>
            <LocationModal {...props} />
        </Wrapper>
    );
};

export default LocationModalWrapper;
