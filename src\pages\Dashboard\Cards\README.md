# Cards Carousel Implementation

This document describes the redesigned Cards page that transforms the traditional table layout into an interactive carousel-style interface for showcasing card types.

## Overview

The Cards page has been completely redesigned to provide a more visually engaging way to browse and manage card types. The new implementation features:

- **Visual Card Previews**: Each card type is displayed as a visual preview using actual dimensions
- **Responsive Carousel**: Smooth horizontal navigation with touch/swipe support
- **Consistent Design**: Matches the application's teal color scheme and design patterns
- **Full Functionality**: Preserves all existing features (create, edit, delete, search)
- **Accessibility**: WCAG compliant with keyboard navigation and screen reader support

## Components

### 1. CardCarousel (`components/CardCarousel.jsx`)

The main carousel component that handles:
- Responsive layout (3 cards on desktop, 2 on tablet, 1 on mobile)
- Navigation arrows and dot indicators
- Touch/swipe gestures for mobile
- Keyboard navigation (Arrow keys, Home, End)
- Accessibility features

**Props:**
- `cards`: Array of card objects
- `onCardSelect`: Callback when a card is selected
- `selectedCard`: Currently selected card object
- `itemsPerView`: Object defining items per viewport size

### 2. CardPreview (`components/CardPreview.jsx`)

Individual card preview component that:
- Renders visual representation using actual card dimensions
- Shows card information (name, type, colors, dimensions)
- Applies connection-type specific gradients and icons
- Maintains proper aspect ratios
- Supports selection states

**Props:**
- `card`: Card object with settings and metadata
- `isSelected`: Boolean indicating selection state
- `onClick`: Click handler function
- `className`: Additional CSS classes

### 3. Main Cards Page (`index.jsx`)

Enhanced main page featuring:
- Search functionality with real-time filtering
- Create new card type button
- Selected card action panel
- Empty state handling
- Loading states
- Mobile-responsive design

## Features

### Visual Accuracy
- Cards are rendered using their actual `width` and `height` properties
- Aspect ratios are maintained across different screen sizes
- Connection types have distinct visual styles (gradients and icons)

### Responsive Design
- **Desktop**: Shows 3 cards per view
- **Tablet**: Shows 2 cards per view  
- **Mobile**: Shows 1 card per view with swipe gestures

### Navigation
- **Arrow Buttons**: Previous/Next navigation
- **Dot Indicators**: Direct slide navigation
- **Keyboard**: Arrow keys, Home, End keys
- **Touch**: Swipe gestures on mobile devices

### Accessibility
- ARIA labels and roles for screen readers
- Keyboard navigation support
- Focus management
- High contrast mode support
- Reduced motion support

### Performance Optimizations
- Memoized components to prevent unnecessary re-renders
- Optimized filtering with useMemo
- Callback memoization for event handlers
- GPU acceleration for smooth animations

## Styling

The carousel uses the application's established design system:
- **Primary Color**: `#00c3ac` (teal)
- **Hover Color**: `#02aa96` (darker teal)
- **Gray Color**: `#dcdcdc`
- **Consistent with**: `main-btn`, `gray-btn` classes

### CSS Files
- `CardCarousel.css`: Main carousel styles with responsive breakpoints
- Follows existing mobile-responsive patterns from the application

## Usage Example

```jsx
import CardCarousel from './components/CardCarousel';

const CardsPage = () => {
  const [selectedCard, setSelectedCard] = useState(null);
  const { data: cards } = useGetCardsTypes();

  return (
    <CardCarousel
      cards={cards}
      onCardSelect={setSelectedCard}
      selectedCard={selectedCard}
      itemsPerView={{
        desktop: 3,
        tablet: 2,
        mobile: 1
      }}
    />
  );
};
```

## Mobile Experience

The carousel is optimized for mobile devices with:
- Touch-friendly navigation controls
- Swipe gestures for navigation
- Responsive card sizing
- Mobile-specific hints and indicators
- Optimized touch targets (minimum 48px)

## Browser Support

- Modern browsers with CSS Grid and Flexbox support
- Touch events for mobile devices
- Keyboard navigation
- Screen reader compatibility

## Testing

Basic test suite included in `__tests__/CardCarousel.test.jsx` covering:
- Component rendering
- Navigation functionality
- Accessibility features
- Mobile responsiveness
- Error handling

## Future Enhancements

Potential improvements for future versions:
- Infinite scroll/loop navigation
- Card preview animations
- Drag-to-reorder functionality
- Virtual scrolling for large datasets
- Advanced filtering options
- Card comparison mode

## Migration Notes

The new carousel implementation:
- ✅ Preserves all existing functionality
- ✅ Maintains the same API endpoints
- ✅ Uses the same data structure
- ✅ Keeps all user workflows intact
- ✅ Follows established design patterns

No breaking changes were introduced during the migration from table to carousel layout.
