import { useState, useEffect, useRef } from 'react';
import { InputText } from 'primereact/inputtext';
import { But<PERSON> } from 'primereact/button';
import { Toast } from 'primereact/toast';
import { Dialog } from 'primereact/dialog';
import { ProgressSpinner } from 'primereact/progressspinner';
import axiosInstance from '../../config/Axios';

const TwoFactorVerification = ({ onSuccess, visible = true, onClose }) => {
    const [verificationCode, setVerificationCode] = useState('');
    const [loading, setLoading] = useState(false);
    const [timeLeft, setTimeLeft] = useState(30);
    const [canResend, setCanResend] = useState(false);
    const [attempts, setAttempts] = useState(0);
    const [isLocked, setIsLocked] = useState(false);
    const [lockTimeRemaining, setLockTimeRemaining] = useState(0);
    const toast = useRef(null);
    const inputRef = useRef(null);
    
    // Constants
    const maxAttempts = 5;
    const timeRemaining = timeLeft;

    useEffect(() => {
        // Focus on input when component mounts
        if (inputRef.current) {
            inputRef.current.focus();
        }

        // Timer for code refresh
        const timer = setInterval(() => {
            setTimeLeft((prev) => {
                if (prev <= 1) {
                    setCanResend(true);
                    return 30;
                }
                return prev - 1;
            });
        }, 1000);

        return () => clearInterval(timer);
    }, []);

    useEffect(() => {
        // Auto-submit when 6 digits are entered
        if (verificationCode.length === 6 && !loading && !isLocked) {
            handleVerify();
        }
    }, [verificationCode]);

    const handleVerify = async () => {
        if (isLocked || loading) return;

        setLoading(true);
        try {
            // Get temp token for authentication
            const tempToken = localStorage.getItem('temp_token');
            const tempUserId = localStorage.getItem('temp_user_id');
            
            console.log('🔍 Verifying 2FA code:', {
                code: verificationCode,
                hasTempToken: !!tempToken,
                tempUserId: tempUserId
            });

            const response = await axiosInstance.post('/two-factor/verify-login', {
                code: verificationCode,
                token: tempToken,
                user_id: tempUserId
            }, {
                headers: tempToken ? {
                    'Authorization': `Bearer ${tempToken}`
                } : {}
            });

            console.log('✅ 2FA Verification response:', response.data);

            if (response.data.success) {
                console.log('🎉 2FA Verification successful!');
                showMessage('success', 'Verification Successful', 'Welcome back! Redirecting...');
                
                // Store verification status
                localStorage.setItem('two_factor_verified', 'true');
                localStorage.setItem('two_factor_verified_at', new Date().toISOString());
                
                // Call onSuccess callback if provided
                if (onSuccess) {
                    onSuccess();
                } else {
                    // Get user role and redirect accordingly
                    const userRole = localStorage.getItem('user_role');
                    const userType = localStorage.getItem('user_type');
                    
                    let redirectUrl = '/manager/dashboard'; // Default for managers
                    
                    if (userRole === 'admin' || userType === 'admin') {
                        redirectUrl = '/admin/dashboard';
                    } else if (userRole === 'manager' || userType === 'manager') {
                        redirectUrl = '/manager/dashboard';
                    }
                    
                    // Redirect to appropriate dashboard after 1 second
                    setTimeout(() => {
                        window.location.href = redirectUrl;
                    }, 1000);
                }
            }
        } catch (error) {
            console.error('❌ 2FA Verification error:', error);
            console.error('❌ Error response:', error.response?.data);
            console.error('❌ Error status:', error.response?.status);
            
            const newAttempts = attempts + 1;
            setAttempts(newAttempts);
            
            // Get error message from server
            const errorMessage = error.response?.data?.message || 'Invalid verification code';
            
            if (newAttempts >= 3) {
                setIsLocked(true);
                showMessage('error', 'Account Locked', 'Too many failed attempts. Please try again in 5 minutes.');
                
                // Unlock after 5 minutes
                setTimeout(() => {
                    setIsLocked(false);
                    setAttempts(0);
                    setVerificationCode('');
                }, 5 * 60 * 1000);
            } else {
                console.log('🔍 Verification failed with message:', errorMessage);
                showMessage('error', 'Verification Failed', `${errorMessage}. ${3 - newAttempts} attempts remaining.`);
                setVerificationCode('');
            }
        } finally {
            setLoading(false);
        }
    };

    const handleResendCode = async () => {
        if (!canResend || loading) return;

        setLoading(true);
        try {
            // Get temp token for authentication
            const tempToken = localStorage.getItem('temp_token');
            const tempUserId = localStorage.getItem('temp_user_id');
            
            console.log('🔄 Resending 2FA code for user:', tempUserId);

            await axiosInstance.post('/two-factor/resend-code', {
                token: tempToken,
                user_id: tempUserId
            }, {
                headers: tempToken ? {
                    'Authorization': `Bearer ${tempToken}`
                } : {}
            });
            
            showMessage('success', 'Code Sent', 'A new verification code has been sent to your authenticator app.');
            setCanResend(false);
            setTimeLeft(30);
        } catch (error) {
            console.error('Resend error:', error);
            showMessage('error', 'Resend Failed', 'Failed to send new code. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const showMessage = (severity, summary, detail) => {
        toast.current?.show({ severity, summary, detail, life: 3000 });
    };

    const handleInputChange = (e) => {
        const value = e.target.value.replace(/\D/g, '').slice(0, 6);
        setVerificationCode(value);
    };

    const handleKeyPress = (e) => {
        if (e.key === 'Enter' && verificationCode.length === 6 && !loading && !isLocked) {
            handleVerify();
        }
    };

    // Render as modal if onClose prop is provided
    if (onClose) {
        return (
            <Dialog
                visible={visible}
                onHide={onClose}
                modal
                closable={false}
                draggable={false}
                resizable={false}
                style={{ 
                    width: '95vw', 
                    maxWidth: '1100px', 
                    height: '85vh'
                }}
                className="twofa-modal"
                maskStyle={{ backgroundColor: 'rgba(0, 0, 0, 0.8)', backdropFilter: 'blur(4px)' }}

                header={
                    <div className="flex items-center justify-between px-6 py-4 border-b border-gray-100">
                        <div className="flex items-center gap-3">
                            <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-sm">
                                <i className="pi pi-shield text-white text-lg"></i>
                            </div>
                            <div>
                                <h2 className="text-xl font-semibold text-gray-900 m-0">Two-Factor Authentication</h2>
                                <p className="text-sm text-gray-500 m-0">Verify your identity to continue</p>
                            </div>
                        </div>
                        <div className="flex items-center gap-3">
                            <div className="flex items-center gap-2 text-sm text-gray-400">
                                <i className="pi pi-lock text-xs"></i>
                                <span>Secure</span>
                            </div>
                            <button
                                onClick={onClose}
                                className="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-500 hover:text-gray-700 transition-all duration-200 flex items-center justify-center shadow-sm border border-gray-200"
                            >
                                <i className="pi pi-times text-sm"></i>
                            </button>
                        </div>
                    </div>
                }
                footer={null}
                contentStyle={{ 
                    padding: 0, 
                    background: 'linear-gradient(135deg, #0f172a 0%, #581c87 50%, #0f172a 100%)',
                    borderRadius: '0 0 16px 16px',
                    overflow: 'hidden'
                }}
                headerStyle={{
                    background: '#ffffff',
                    borderRadius: '16px 16px 0 0',
                    border: 'none',
                    padding: 0,
                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
                }}
            >
                <div className="w-full h-full bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
                    {/* Background Pattern */}
                    <div className="absolute inset-0 opacity-50">
                        <div className="w-full h-full" style={{
                            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
                        }}></div>
                    </div>
                    
                    {/* Animated Background Elements */}
                    <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
                        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-500/20 to-purple-600/20 rounded-full blur-3xl animate-pulse"></div>
                        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-purple-500/20 to-pink-600/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
                        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-purple-400/10 rounded-full blur-3xl animate-pulse delay-500"></div>
                    </div>

                    <Toast ref={toast} />

                    <div className="relative z-10 h-full flex items-center justify-center p-6 overflow-y-auto">
                        <div className="w-full max-w-4xl">
                            {/* Main Content */}
                            <div className="flex lg:flex-row flex-col gap-6 items-start justify-center pt-4">
                                {/* Center Column - Verification Form */}
                                <div className="lg:w-3/5 max-w-xl bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-2xl">
                                    <div className="space-y-6">
                                        {/* Security Status */}
                                        <div className="bg-gradient-to-r from-emerald-500/20 to-blue-500/20 border border-emerald-400/30 rounded-xl p-4">
                                            <div className="flex items-center gap-3">
                                                <div className="w-10 h-10 bg-emerald-500/20 rounded-full flex items-center justify-center">
                                                    <i className="pi pi-check-circle text-emerald-400"></i>
                                                </div>
                                                <div>
                                                    <h3 className="text-base font-semibold text-white">Account Protected</h3>
                                                    <p className="text-emerald-300 text-sm">Your account is secured with 2FA</p>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Code Input */}
                                        <div className="space-y-4">
                                            <div className="text-center">
                                                <label className="text-base font-medium text-white block mb-3">
                                                    Verification Code
                                                </label>
                                                <div className="relative">
                                                    <InputText
                                                        ref={inputRef}
                                                        value={verificationCode}
                                                        onChange={handleInputChange}
                                                        onKeyPress={handleKeyPress}
                                                        placeholder="000000"
                                                        maxLength={6}
                                                        className={`w-full text-center text-3xl font-mono tracking-widest border-2 rounded-xl p-4 transition-all duration-300 bg-white/10 backdrop-blur-sm border-white/20 text-white placeholder-gray-400 ${
                                                            isLocked 
                                                                ? 'border-red-400/50 bg-red-500/10' 
                                                                : 'border-white/30 focus:border-blue-400 focus:ring-4 focus:ring-blue-400/20'
                                                        }`}
                                                        keyfilter="int"
                                                        disabled={isLocked || loading}
                                                    />
                                                    {loading && (
                                                        <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                                                            <ProgressSpinner style={{ width: '20px', height: '20px' }} strokeWidth="3" />
                                                        </div>
                                                    )}
                                                </div>
                                            </div>

                                            {/* Timer */}
                                            <div className="text-center">
                                                <div className="flex items-center justify-center gap-2 text-gray-300 mb-2">
                                                    <i className="pi pi-clock"></i>
                                                    <span>Code refreshes in {timeLeft}s</span>
                                                </div>
                                                
                                                {/* Progress Bar */}
                                                <div className="w-full bg-white/10 rounded-full h-1.5 overflow-hidden">
                                                    <div 
                                                        className="bg-gradient-to-r from-blue-500 to-purple-500 h-1.5 rounded-full transition-all duration-1000 ease-out"
                                                        style={{ width: `${((30 - timeLeft) / 30) * 100}%` }}
                                                    ></div>
                                                </div>
                                            </div>

                                            {/* Attempts Counter */}
                                            {attempts > 0 && (
                                                <div className="text-center">
                                                    <div className="inline-flex items-center gap-2 px-3 py-1 bg-red-500/20 border border-red-400/30 rounded-full">
                                                        <i className="pi pi-exclamation-triangle text-red-400 text-sm"></i>
                                                        <span className="text-red-300 text-sm font-medium">
                                                            {attempts}/3 attempts used
                                                        </span>
                                                    </div>
                                                </div>
                                            )}
                                        </div>

                                        {/* Action Buttons */}
                                        <div className="space-y-3">
                                            <Button
                                                label={loading ? 'Verifying...' : 'Verify & Continue'}
                                                className={`w-full py-3 text-base font-semibold rounded-xl transition-all duration-300 ${
                                                    isLocked 
                                                        ? 'bg-gray-500/50 cursor-not-allowed' 
                                                        : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 hover:scale-105 active:scale-95'
                                                } border-0 shadow-xl`}
                                                onClick={handleVerify}
                                                loading={loading}
                                                disabled={verificationCode.length !== 6 || isLocked}
                                            />
                                            
                                            <Button
                                                label="Use Different Account"
                                                icon="pi pi-arrow-left"
                                                className="w-full py-3 text-sm text-gray-300 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg transition-all duration-300"
                                                onClick={onClose}
                                            />
                                        </div>

                                        {/* Locked Message */}
                                        {isLocked && (
                                            <div className="bg-red-500/20 border border-red-400/30 rounded-xl p-4">
                                                <div className="flex items-center gap-3">
                                                    <i className="pi pi-lock text-red-400 text-xl"></i>
                                                    <div className="text-red-300">
                                                        <strong className="block text-base mb-1">Temporarily Locked</strong>
                                                        <p className="text-sm">Too many failed attempts. Wait 5 minutes.</p>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Side Column - Instructions */}
                                <div className="lg:w-2/5 space-y-4">
                                    {/* Instructions */}
                                    <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-4 border border-white/20 shadow-2xl">
                                        <div className="flex items-start gap-3 mb-4">
                                            <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                                <i className="pi pi-info-circle text-blue-400 text-sm"></i>
                                            </div>
                                            <div>
                                                <h3 className="text-base font-bold text-white mb-1">Setup Guide</h3>
                                                <p className="text-gray-300 text-sm">Complete verification steps</p>
                                            </div>
                                        </div>

                                        <div className="space-y-2">
                                            <div className="flex items-center gap-3 p-2 bg-white/5 rounded-lg">
                                                <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-xs">1</div>
                                                <span className="text-white text-sm">Open authenticator app</span>
                                            </div>
                                            <div className="flex items-center gap-3 p-2 bg-white/5 rounded-lg">
                                                <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-xs">2</div>
                                                <span className="text-white text-sm">Find InkNull account</span>
                                            </div>
                                            <div className="flex items-center gap-3 p-2 bg-white/5 rounded-lg">
                                                <div className="w-6 h-6 bg-pink-500 rounded-full flex items-center justify-center text-white font-bold text-xs">3</div>
                                                <span className="text-white text-sm">Enter 6-digit code</span>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Security Features */}
                                    <div className="bg-gradient-to-r from-emerald-500/20 to-blue-500/20 rounded-2xl p-4 border border-emerald-400/30 shadow-2xl">
                                        <div className="flex items-center gap-3 mb-3">
                                            <i className="pi pi-shield text-emerald-400"></i>
                                            <h3 className="text-sm font-bold text-white">Security Features</h3>
                                        </div>
                                        <div className="grid grid-cols-2 gap-2 text-xs">
                                            <div className="flex items-center gap-1 text-emerald-300">
                                                <i className="pi pi-check-circle text-xs"></i>
                                                <span>30s codes</span>
                                            </div>
                                            <div className="flex items-center gap-1 text-emerald-300">
                                                <i className="pi pi-check-circle text-xs"></i>
                                                <span>3 attempts</span>
                                            </div>
                                            <div className="flex items-center gap-1 text-emerald-300">
                                                <i className="pi pi-check-circle text-xs"></i>
                                                <span>Auto-lock</span>
                                            </div>
                                            <div className="flex items-center gap-1 text-emerald-300">
                                                <i className="pi pi-check-circle text-xs"></i>
                                                <span>Encrypted</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </Dialog>
        );
    }

    // Render as full page if no onClose prop
    return (
        <div className="min-h-screen w-full bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-50">
                <div className="w-full h-full" style={{
                    backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
                }}></div>
            </div>
            
            {/* Animated Background Elements */}
            <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
                <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-500/20 to-purple-600/20 rounded-full blur-3xl animate-pulse"></div>
                <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-purple-500/20 to-pink-600/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-purple-400/10 rounded-full blur-3xl animate-pulse delay-500"></div>
            </div>

            <Toast ref={toast} />
            
            <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
                <div className="w-full max-w-6xl">
                    {/* Header Section */}
                    <div className="text-center mb-12">
                        <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 rounded-full mb-8 shadow-2xl relative overflow-hidden">
                            <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>
                            <i className="pi pi-shield text-white text-4xl relative z-10"></i>
                            <div className="absolute inset-0 bg-gradient-to-br from-transparent to-black/20"></div>
                        </div>
                        <h1 className="text-5xl font-bold text-white mb-4 tracking-tight">
                            Two-Factor Authentication
                        </h1>
                        <p className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed">
                            Enter the 6-digit verification code from your authenticator app to securely access your account
                        </p>
                    </div>

                    {/* Main Content */}
                    <div className="flex lg:flex-row flex-col gap-8 items-start justify-center">
                        {/* Center Column - Verification Form */}
                        <div className="lg:w-2/3 max-w-2xl bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl">
                            <div className="space-y-8">
                                {/* Security Status */}
                                <div className="bg-gradient-to-r from-emerald-500/20 to-blue-500/20 border border-emerald-400/30 rounded-2xl p-6">
                                    <div className="flex items-center gap-4">
                                        <div className="w-12 h-12 bg-emerald-500/20 rounded-full flex items-center justify-center">
                                            <i className="pi pi-check-circle text-emerald-400 text-xl"></i>
                                        </div>
                                        <div>
                                            <h3 className="text-lg font-semibold text-white mb-1">Account Protected</h3>
                                            <p className="text-emerald-300 text-sm">Your account is secured with advanced 2FA</p>
                                        </div>
                                    </div>
                                </div>

                                {/* Code Input */}
                        <div className="space-y-6">
                            <div className="text-center">
                                        <label className="text-lg font-medium text-white block mb-4">
                                    Verification Code
                                </label>
                                <div className="relative">
                                    <InputText
                                        ref={inputRef}
                                        value={verificationCode}
                                        onChange={handleInputChange}
                                        onKeyPress={handleKeyPress}
                                        placeholder="000000"
                                        maxLength={6}
                                                className={`w-full text-center text-4xl font-mono tracking-widest border-2 rounded-2xl p-6 transition-all duration-300 bg-white/10 backdrop-blur-sm border-white/20 text-white placeholder-gray-400 ${
                                            isLocked 
                                                ? 'border-red-400/50 bg-red-500/10' 
                                                        : 'border-white/30 focus:border-blue-400 focus:ring-4 focus:ring-blue-400/20'
                                        }`}
                                        keyfilter="int"
                                        disabled={isLocked || loading}
                                    />
                                    {loading && (
                                                <div className="absolute right-6 top-1/2 transform -translate-y-1/2">
                                                    <ProgressSpinner style={{ width: '24px', height: '24px' }} strokeWidth="3" />
                                        </div>
                                    )}
                                </div>
                            </div>

                                    {/* Timer */}
                                    <div className="text-center">
                                        <div className="flex items-center justify-center gap-3 text-gray-300 mb-3">
                                            <i className="pi pi-clock text-lg"></i>
                                            <span className="text-lg">Code refreshes in {timeLeft}s</span>
                                        </div>
                                        
                                        {/* Progress Bar */}
                                        <div className="w-full bg-white/10 rounded-full h-2 overflow-hidden">
                                            <div 
                                                className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-1000 ease-out"
                                                style={{ width: `${((30 - timeLeft) / 30) * 100}%` }}
                                            ></div>
                                        </div>
                                    </div>

                                    {/* Attempts Counter */}
                            {attempts > 0 && (
                                <div className="text-center">
                                            <div className="inline-flex items-center gap-3 px-4 py-2 bg-red-500/20 border border-red-400/30 rounded-full">
                                        <i className="pi pi-exclamation-triangle text-red-400"></i>
                                                <span className="text-red-300 font-medium">
                                            {attempts}/3 attempts used
                                        </span>
                                    </div>
                                </div>
                            )}
                                </div>

                                {/* Action Buttons */}
                                <div className="space-y-4">
                                <Button
                                    label={loading ? 'Verifying...' : 'Verify & Continue'}
                                        className={`w-full py-4 text-xl font-semibold rounded-2xl transition-all duration-300 ${
                                        isLocked 
                                            ? 'bg-gray-500/50 cursor-not-allowed' 
                                                : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 hover:scale-105 active:scale-95'
                                        } border-0 shadow-2xl`}
                                    onClick={handleVerify}
                                    loading={loading}
                                    disabled={verificationCode.length !== 6 || isLocked}
                                />
                                
                                <Button
                                    label={`Resend Code ${canResend ? '' : `(${timeLeft}s)`}`}
                                        className="w-full py-3 text-gray-300 bg-white/10 hover:bg-white/20 border border-white/20 rounded-2xl transition-all duration-300"
                                    onClick={handleResendCode}
                                    disabled={!canResend || loading}
                                    />
                                </div>

                                {/* Locked Message */}
                                {isLocked && (
                                    <div className="bg-red-500/20 border border-red-400/30 rounded-2xl p-6">
                                        <div className="flex items-center gap-4">
                                            <i className="pi pi-lock text-red-400 text-2xl"></i>
                                            <div className="text-red-300">
                                                <strong className="block text-lg mb-1">Account Temporarily Locked</strong>
                                                <p>Too many failed attempts. Please wait 5 minutes before trying again.</p>
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Side Column - All Extra Info */}
                        <div className="lg:w-1/3 space-y-8">
                            {/* Instructions */}
                            <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl">
                                <div className="flex items-start gap-4 mb-6">
                                    <div className="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i className="pi pi-info-circle text-blue-400 text-xl"></i>
                                    </div>
                                    <div>
                                        <h3 className="text-2xl font-bold text-white mb-2">Quick Setup Guide</h3>
                                        <p className="text-gray-300 text-lg leading-relaxed">
                                            Follow these simple steps to complete your verification
                                        </p>
                                    </div>
                            </div>

                                <div className="space-y-4">
                                    <div className="flex items-center gap-4 p-4 bg-white/5 rounded-xl">
                                        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-sm">1</div>
                                        <span className="text-white">Open your authenticator app (Google Authenticator, Authy, etc.)</span>
                                    </div>
                                    <div className="flex items-center gap-4 p-4 bg-white/5 rounded-xl">
                                        <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-sm">2</div>
                                        <span className="text-white">Find the InkNull account in your app</span>
                                    </div>
                                    <div className="flex items-center gap-4 p-4 bg-white/5 rounded-xl">
                                        <div className="w-8 h-8 bg-pink-500 rounded-full flex items-center justify-center text-white font-bold text-sm">3</div>
                                        <span className="text-white">Enter the 6-digit code shown in your app</span>
                                    </div>
                                </div>
                            </div>

                            {/* Troubleshooting */}
                            <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl">
                                <div className="flex items-start gap-4 mb-6">
                                    <div className="w-12 h-12 bg-amber-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i className="pi pi-lightbulb text-amber-400 text-xl"></i>
                                    </div>
                                    <div>
                                        <h3 className="text-2xl font-bold text-white mb-2">Need Help?</h3>
                                        <p className="text-gray-300 text-lg leading-relaxed">
                                            Common solutions for verification issues
                                        </p>
                                    </div>
                            </div>

                                <div className="space-y-4">
                                    <div className="p-4 bg-amber-500/10 border border-amber-400/20 rounded-xl">
                                        <h4 className="text-amber-300 font-semibold mb-2">Device Time Sync</h4>
                                        <p className="text-gray-300 text-sm">Make sure your device time is synchronized with the internet</p>
                                    </div>
                                    <div className="p-4 bg-amber-500/10 border border-amber-400/20 rounded-xl">
                                        <h4 className="text-amber-300 font-semibold mb-2">App Refresh</h4>
                                        <p className="text-gray-300 text-sm">Try refreshing your authenticator app or restarting it</p>
                                    </div>
                                    <div className="p-4 bg-amber-500/10 border border-amber-400/20 rounded-xl">
                                        <h4 className="text-amber-300 font-semibold mb-2">Code Timing</h4>
                                        <p className="text-gray-300 text-sm">Codes change every 30 seconds, make sure to enter it quickly</p>
                                    </div>
                                </div>
                            </div>

                            {/* Security Features */}
                            <div className="bg-gradient-to-r from-emerald-500/20 to-blue-500/20 rounded-3xl p-8 border border-emerald-400/30 shadow-2xl">
                                <div className="flex items-center gap-4 mb-4">
                                    <i className="pi pi-shield text-emerald-400 text-2xl"></i>
                                    <h3 className="text-xl font-bold text-white">Security Features</h3>
                                </div>
                                <div className="grid grid-cols-1 gap-4 text-sm">
                                    <div className="flex items-center gap-2 text-emerald-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>30-second codes</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-emerald-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>3 attempt limit</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-emerald-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>Auto-lock protection</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-emerald-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>Secure encryption</span>
                                    </div>
                                </div>
                            </div>

                            {/* Additional Security Info */}
                            <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl">
                                <div className="flex items-start gap-4 mb-6">
                                    <div className="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i className="pi pi-shield-check text-purple-400 text-xl"></i>
                                    </div>
                                    <div>
                                        <h3 className="text-xl font-bold text-white mb-2">Why 2FA?</h3>
                                        <p className="text-gray-300 text-sm leading-relaxed">
                                            Two-factor authentication adds an extra layer of security to your account, protecting you even if your password is compromised.
                                        </p>
                                    </div>
                            </div>

                                <div className="space-y-3">
                                    <div className="flex items-center gap-3 text-purple-300">
                                        <i className="pi pi-lock text-sm"></i>
                                        <span className="text-sm">Protects against password theft</span>
                                    </div>
                                    <div className="flex items-center gap-3 text-purple-300">
                                        <i className="pi pi-user-shield text-sm"></i>
                                        <span className="text-sm">Secures sensitive data access</span>
                                        </div>
                                    <div className="flex items-center gap-3 text-purple-300">
                                        <i className="pi pi-shield text-sm"></i>
                                        <span className="text-sm">Industry standard security</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Footer */}
                    <div className="text-center mt-12">
                        <p className="text-gray-400 text-lg">
                            Having trouble? <a href="/support" className="text-blue-400 hover:text-blue-300 font-medium transition-colors">Contact Support</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default TwoFactorVerification; 