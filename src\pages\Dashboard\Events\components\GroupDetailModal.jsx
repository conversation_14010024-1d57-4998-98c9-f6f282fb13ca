import React, { useState } from 'react';
import { Dialog } from 'primereact/dialog';
import { But<PERSON> } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { Dropdown } from 'primereact/dropdown';
import { ConfirmDialog } from 'primereact/confirmdialog';
import { motion } from 'framer-motion';
import { FiInfo, FiEdit, FiTrash2, FiShield, FiCreditCard, FiUsers, FiPlus, FiUserPlus, FiX, FiMapPin } from 'react-icons/fi';
import { mockEventCardTypes, getEventMembersByGroupId } from '@data/mockEventGroupsData';
import AddCardModal from './AddCardModal';
import LocationModal from './LocationModal';

const GroupDetailModal = ({ visible, onHide, group, event, onUpdateGroup, onDeleteGroup, isMobile }) => {
    const [loading, setLoading] = useState(false);
    const [editMode, setEditMode] = useState(false);
    const [editFormData, setEditFormData] = useState({
        title: '',
        description: '',
        access_level: '',
        status: ''
    });
    const [groupMembers, setGroupMembers] = useState([]);
    const [groupCards, setGroupCards] = useState([]);
    const [showAddMemberModal, setShowAddMemberModal] = useState(false);
    const [showAddCardModal, setShowAddCardModal] = useState(false);
    const [showLocationModal, setShowLocationModal] = useState(false);

    // Confirmation dialog states
    const [confirmDialog, setConfirmDialog] = useState({
        visible: false,
        message: '',
        header: '',
        action: null
    });

    // Initialize form data when group changes
    React.useEffect(() => {
        if (group) {
            setEditFormData({
                title: group.title || '',
                description: group.description || '',
                access_level: group.access_level || '',
                status: group.status || 'active'
            });
            
            // Load group members (mock data for now)
            const members = getEventMembersByGroupId(group.id);
            setGroupMembers(members);
            
            // Mock group cards data
            setGroupCards([
                {
                    id: 'card-1',
                    name: 'VIP Access Card',
                    type: group.card_type_name,
                    status: 'active',
                    assignedAt: '2024-07-01T10:30:00Z'
                }
            ]);
        }
    }, [group]);

    if (!group) return null;

    const handleSaveChanges = async () => {
        try {
            setLoading(true);
            
            const updatedGroup = {
                ...group,
                ...editFormData
            };

            // Call API to update group
            // await axiosInstance.put(`/event-groups/${group.id}`, editFormData);
            
            onUpdateGroup(updatedGroup);
            setEditMode(false);
        } catch (error) {
            console.error('Error updating group:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleDeleteGroup = () => {
        setConfirmDialog({
            visible: true,
            message: `Are you sure you want to delete the group "${group.title}"? This action cannot be undone.`,
            header: 'Delete Group Confirmation',
            action: async () => {
                try {
                    setLoading(true);
                    // await axiosInstance.delete(`/event-groups/${group.id}`);
                    onDeleteGroup(group.id);
                } catch (error) {
                    console.error('Error deleting group:', error);
                } finally {
                    setLoading(false);
                }
            }
        });
    };

    const handleRemoveMember = (memberId) => {
        setConfirmDialog({
            visible: true,
            message: 'Are you sure you want to remove this member from the group?',
            header: 'Remove Member Confirmation',
            action: () => {
                setGroupMembers(prev => prev.filter(member => member.id !== memberId));
            }
        });
    };

    const handleRemoveCard = (cardId) => {
        setConfirmDialog({
            visible: true,
            message: 'Are you sure you want to remove this card from the group?',
            header: 'Remove Card Confirmation',
            action: () => {
                setGroupCards(prev => prev.filter(card => card.id !== cardId));
            }
        });
    };

    const handleConfirmAccept = () => {
        if (confirmDialog.action) {
            confirmDialog.action();
        }
        setConfirmDialog({ visible: false, message: '', header: '', action: null });
    };

    const handleConfirmReject = () => {
        setConfirmDialog({ visible: false, message: '', header: '', action: null });
    };

    // Handle creating new event member and adding to group
    const handleCreateEventMember = async (memberData) => {
        try {
            setLoading(true);

            const payload = {
                event_id: event.id,
                group_id: group.id,
                name: memberData.name,
                email: memberData.email || '',
                phone: memberData.phone || '',
                department: memberData.department,
                role: memberData.role,
                access_level: memberData.accessLevel,
                event_context: true
            };

            // Call event-specific API to create new member and add to group
            // const response = await axiosInstance.post(`/event-groups/${group.id}/members/create`, payload);
            // const newMember = response.data.member || response.data;

            // For now, create a mock member
            const newMember = {
                id: Date.now().toString(),
                name: memberData.name,
                email: memberData.email || '',
                phone: memberData.phone || '',
                department: memberData.department,
                role: memberData.role,
                access_level: memberData.accessLevel,
                addedAt: new Date().toISOString()
            };

            // Add the new member to the local state
            setGroupMembers(prev => [...prev, newMember]);
            setShowAddMemberModal(false);

        } catch (error) {
            console.error('Error creating event member:', error);
        } finally {
            setLoading(false);
        }
    };

    // Handle adding card to group
    const handleAddCard = (cardData) => {
        setGroupCards(prev => [...prev, cardData]);
    };

    return (
        <Dialog
            header={
                <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-2">
                        <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
                            <span className="text-white font-medium text-sm">
                                {group.title?.charAt(0)?.toUpperCase()}
                            </span>
                        </div>
                        <span className="text-lg font-semibold">Group Details - {group.title}</span>
                    </div>
                    <div className="flex gap-2">
                        {!editMode ? (
                            <Button
                                icon={<FiEdit size={14} />}
                                label={isMobile ? "" : "Edit"}
                                className="p-button-sm p-button-outlined"
                                style={{
                                    backgroundColor: 'white',
                                    color: 'black',
                                    border: '1px solid #d1d5db',
                                    padding: isMobile ? '8px' : '6px 12px',
                                    borderRadius: '6px',
                                    transition: 'all 0.2s ease'
                                }}
                                onClick={() => setEditMode(true)}
                                tooltip="Edit Group"
                                tooltipOptions={{ position: 'bottom' }}
                            />
                        ) : (
                            <>
                                <Button
                                    label="Cancel"
                                    className="p-button-sm p-button-outlined"
                                    style={{
                                        backgroundColor: 'white',
                                        color: 'black',
                                        border: '1px solid #d1d5db',
                                        padding: '6px 12px',
                                        borderRadius: '6px'
                                    }}
                                    onClick={() => {
                                        setEditMode(false);
                                        setEditFormData({
                                            title: group.title || '',
                                            description: group.description || '',
                                            access_level: group.access_level || '',
                                            status: group.status || 'active'
                                        });
                                    }}
                                />
                                <Button
                                    label="Save"
                                    className="p-button-sm"
                                    style={{
                                        backgroundColor: '#00c3ac',
                                        color: 'white',
                                        border: '1px solid #00c3ac',
                                        padding: '6px 12px',
                                        borderRadius: '6px'
                                    }}
                                    onClick={handleSaveChanges}
                                    loading={loading}
                                />
                            </>
                        )}
                        <Button
                            icon={<FiTrash2 size={14} />}
                            label={isMobile ? "" : "Delete"}
                            className="p-button-sm p-button-danger p-button-outlined"
                            style={{
                                backgroundColor: 'white',
                                color: '#dc2626',
                                border: '1px solid #dc2626',
                                padding: isMobile ? '8px' : '6px 12px',
                                borderRadius: '6px'
                            }}
                            onClick={handleDeleteGroup}
                            tooltip="Delete Group"
                            tooltipOptions={{ position: 'bottom' }}
                        />
                    </div>
                </div>
            }
            visible={visible}
            style={{
                width: isMobile ? '95vw' : '80vw',
                maxWidth: isMobile ? '95vw' : '1200px',
                height: isMobile ? '90vh' : 'auto',
                maxHeight: '90vh'
            }}
            breakpoints={{
                '960px': '95vw',
                '641px': '95vw'
            }}
            onHide={onHide}
            className="group-detail-modal"
            contentStyle={{
                height: isMobile ? 'calc(90vh - 80px)' : 'auto',
                overflow: 'auto',
                padding: isMobile ? '15px' : '20px'
            }}
        >
            <div className="space-y-6">
                {/* Group Information Section */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="bg-blue-50 p-4 rounded-lg border border-blue-200"
                >
                    <div className="flex items-center gap-2 mb-3">
                        <FiInfo className="text-blue-600" size={16} />
                        <span className="font-medium text-blue-900">Group Information</span>
                    </div>
                    
                    {editMode ? (
                        <div className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Group Name
                                </label>
                                <InputText
                                    value={editFormData.title}
                                    onChange={(e) => setEditFormData(prev => ({ ...prev, title: e.target.value }))}
                                    className="w-full"
                                    placeholder="Enter group name"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Description
                                </label>
                                <InputTextarea
                                    value={editFormData.description}
                                    onChange={(e) => setEditFormData(prev => ({ ...prev, description: e.target.value }))}
                                    className="w-full"
                                    rows={3}
                                    placeholder="Enter group description"
                                />
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Access Level
                                    </label>
                                    <Dropdown
                                        value={editFormData.access_level}
                                        options={[
                                            { label: 'VIP', value: 'vip' },
                                            { label: 'Standard', value: 'standard' },
                                            { label: 'Staff', value: 'staff' },
                                            { label: 'Media', value: 'media' },
                                            { label: 'Volunteer', value: 'volunteer' },
                                            { label: 'Security', value: 'security' },
                                            { label: 'Facilitator', value: 'facilitator' },
                                            { label: 'Instructor', value: 'instructor' }
                                        ]}
                                        onChange={(e) => setEditFormData(prev => ({ ...prev, access_level: e.value }))}
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Select access level"
                                        className="w-full"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Status
                                    </label>
                                    <Dropdown
                                        value={editFormData.status}
                                        options={[
                                            { label: 'Active', value: 'active' },
                                            { label: 'Inactive', value: 'inactive' },
                                            { label: 'Pending', value: 'pending' }
                                        ]}
                                        onChange={(e) => setEditFormData(prev => ({ ...prev, status: e.value }))}
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Select status"
                                        className="w-full"
                                    />
                                </div>
                            </div>
                        </div>
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
                            <div>
                                <p><strong>Group Name:</strong> {group.title}</p>
                                <p><strong>Description:</strong> {group.description}</p>
                                <p><strong>Card Type:</strong> {group.card_type_name}</p>
                                {event?.location && (
                                    <p>
                                        <strong>Location:</strong>
                                        <button
                                            onClick={() => setShowLocationModal(true)}
                                            className="ml-2 text-blue-600 hover:text-blue-800 underline cursor-pointer inline-flex items-center gap-1 transition-colors"
                                            title="View location on map"
                                        >
                                            <FiMapPin size={12} />
                                            {event.location}
                                        </button>
                                    </p>
                                )}
                            </div>
                            <div>
                                <p><strong>Access Level:</strong> {group.access_level}</p>
                                <p><strong>Status:</strong> {group.status}</p>
                                <p><strong>Created:</strong> {new Date(group.created_at).toLocaleDateString()}</p>
                                <p><strong>Members:</strong> {groupMembers.length}</p>
                            </div>
                        </div>
                    )}
                </motion.div>

                {/* Access Privileges Section */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 }}
                    className="bg-green-50 p-4 rounded-lg border border-green-200"
                >
                    <div className="flex items-center gap-2 mb-3">
                        <FiShield className="text-green-600" size={16} />
                        <span className="font-medium text-green-900">Access Privileges</span>
                    </div>
                    <div className="text-sm text-green-800">
                        <p><strong>Access Level:</strong> {group.access_level}</p>
                        <p><strong>Card Type:</strong> {group.card_type_name}</p>
                        <div className="mt-2">
                            <strong>Permissions:</strong>
                            <div className="flex flex-wrap gap-1 mt-1">
                                {mockEventCardTypes
                                    .find(cardType => cardType.name === group.card_type_name)
                                    ?.permissions?.map((permission, index) => (
                                        <span
                                            key={index}
                                            className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full"
                                        >
                                            {permission.replace('_', ' ')}
                                        </span>
                                    )) || <span className="text-gray-500">No permissions defined</span>
                                }
                            </div>
                        </div>
                    </div>
                </motion.div>

                {/* Assigned Cards Section */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.2 }}
                    className="bg-purple-50 p-4 rounded-lg border border-purple-200"
                >
                    <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-2">
                            <FiCreditCard className="text-purple-600" size={16} />
                            <span className="font-medium text-purple-900">Assigned Cards ({groupCards.length})</span>
                        </div>
                        <Button
                            icon={<FiPlus size={14} />}
                            label={isMobile ? "" : "Add Card"}
                            className="p-button-sm p-button-outlined"
                            style={{
                                backgroundColor: 'white',
                                color: 'black',
                                border: '1px solid #d1d5db',
                                padding: isMobile ? '8px' : '6px 12px',
                                borderRadius: '6px',
                                transition: 'all 0.2s ease'
                            }}
                            onMouseEnter={(e) => {
                                if (!isMobile) {
                                    e.target.style.transform = 'translateY(-1px)';
                                    e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
                                }
                            }}
                            onMouseLeave={(e) => {
                                if (!isMobile) {
                                    e.target.style.transform = 'translateY(0)';
                                    e.target.style.boxShadow = 'none';
                                }
                            }}
                            onClick={() => setShowAddCardModal(true)}
                            tooltip="Add Card to Group"
                            tooltipOptions={{ position: 'top' }}
                        />
                    </div>

                    {groupCards.length === 0 ? (
                        <div className="text-center py-4 text-purple-600">
                            <FiCreditCard size={32} className="mx-auto mb-2 opacity-50" />
                            <p>No cards assigned to this group</p>
                        </div>
                    ) : (
                        <div className="space-y-2">
                            {groupCards.map((card) => (
                                <div key={card.id} className="flex items-center justify-between p-3 bg-white rounded-lg border">
                                    <div>
                                        <div className="font-medium">{card.name}</div>
                                        <div className="text-sm text-gray-500">Type: {card.type} • Status: {card.status}</div>
                                        <div className="text-xs text-gray-400">
                                            Assigned: {new Date(card.assignedAt).toLocaleDateString()}
                                        </div>
                                    </div>
                                    <Button
                                        icon={<FiX size={14} />}
                                        className="p-button-sm p-button-outlined p-button-danger"
                                        style={{
                                            backgroundColor: 'white',
                                            color: '#dc2626',
                                            border: '1px solid #dc2626',
                                            padding: '6px',
                                            borderRadius: '6px'
                                        }}
                                        onClick={() => handleRemoveCard(card.id)}
                                        tooltip="Remove Card"
                                        tooltipOptions={{ position: 'top' }}
                                    />
                                </div>
                            ))}
                        </div>
                    )}
                </motion.div>

                {/* Members Section */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.3 }}
                    className="bg-orange-50 p-4 rounded-lg border border-orange-200"
                >
                    <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-2">
                            <FiUsers className="text-orange-600" size={16} />
                            <span className="font-medium text-orange-900">Members ({groupMembers.length})</span>
                        </div>
                        <Button
                            icon={<FiUserPlus size={14} />}
                            label={isMobile ? "" : "Add Member"}
                            className="p-button-sm p-button-outlined"
                            style={{
                                backgroundColor: 'white',
                                color: 'black',
                                border: '1px solid #d1d5db',
                                padding: isMobile ? '8px' : '6px 12px',
                                borderRadius: '6px',
                                transition: 'all 0.2s ease'
                            }}
                            onMouseEnter={(e) => {
                                if (!isMobile) {
                                    e.target.style.transform = 'translateY(-1px)';
                                    e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
                                }
                            }}
                            onMouseLeave={(e) => {
                                if (!isMobile) {
                                    e.target.style.transform = 'translateY(0)';
                                    e.target.style.boxShadow = 'none';
                                }
                            }}
                            onClick={() => setShowAddMemberModal(true)}
                            tooltip="Add Member to Group"
                            tooltipOptions={{ position: 'top' }}
                        />
                    </div>

                    {groupMembers.length === 0 ? (
                        <div className="text-center py-4 text-orange-600">
                            <FiUsers size={32} className="mx-auto mb-2 opacity-50" />
                            <p>No members in this group</p>
                        </div>
                    ) : (
                        <div className="space-y-2">
                            {groupMembers.map((member) => (
                                <div key={member.id} className="flex items-center justify-between p-3 bg-white rounded-lg border">
                                    <div className="flex items-center gap-3">
                                        <div className="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center">
                                            <span className="text-gray-600 font-medium text-sm">
                                                {member.name?.charAt(0)?.toUpperCase()}
                                            </span>
                                        </div>
                                        <div>
                                            <div className="font-medium">{member.name}</div>
                                            <div className="text-sm text-gray-500">{member.email}</div>
                                            <div className="text-xs text-gray-400">
                                                {member.department} • {member.role}
                                            </div>
                                        </div>
                                    </div>
                                    <Button
                                        icon={<FiX size={14} />}
                                        className="p-button-sm p-button-outlined p-button-danger"
                                        style={{
                                            backgroundColor: 'white',
                                            color: '#dc2626',
                                            border: '1px solid #dc2626',
                                            padding: '6px',
                                            borderRadius: '6px'
                                        }}
                                        onClick={() => handleRemoveMember(member.id)}
                                        tooltip="Remove Member"
                                        tooltipOptions={{ position: 'top' }}
                                    />
                                </div>
                            ))}
                        </div>
                    )}
                </motion.div>
            </div>

            {/* Confirmation Dialog */}
            <ConfirmDialog
                visible={confirmDialog.visible}
                onHide={handleConfirmReject}
                message={confirmDialog.message}
                header={confirmDialog.header}
                icon="pi pi-exclamation-triangle"
                accept={handleConfirmAccept}
                reject={handleConfirmReject}
                acceptLabel="Yes"
                rejectLabel="Cancel"
                acceptClassName="p-button-danger"
            />

            {/* Add Member Modal */}
            <Dialog
                header={`Add Member to ${group?.title || 'Group'}`}
                visible={showAddMemberModal}
                style={{
                    width: isMobile ? '95vw' : '50vw',
                    maxWidth: isMobile ? '95vw' : '600px'
                }}
                breakpoints={{
                    '960px': '80vw',
                    '641px': '95vw'
                }}
                onHide={() => setShowAddMemberModal(false)}
                className="create-member-modal"
                modal
            >
                <CreateMemberForm
                    loading={loading}
                    onSubmit={handleCreateEventMember}
                    onCancel={() => setShowAddMemberModal(false)}
                    isMobile={isMobile}
                />
            </Dialog>

            {/* Add Card Modal */}
            <AddCardModal
                visible={showAddCardModal}
                onHide={() => setShowAddCardModal(false)}
                group={group}
                event={event}
                onAddCard={handleAddCard}
                isMobile={isMobile}
            />

            {/* Location Modal */}
            <LocationModal
                visible={showLocationModal}
                onHide={() => setShowLocationModal(false)}
                event={event}
                isMobile={isMobile}
            />
        </Dialog>
    );
};

// CreateMemberForm component - extracted from AssociatedGroupsModal for consistency
const CreateMemberForm = ({ loading, onSubmit, onCancel, isMobile }) => {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        phone: '',
        department: '',
        role: '',
        accessLevel: ''
    });
    const [errors, setErrors] = useState({});

    const accessLevels = [
        { label: 'VIP', value: 'vip' },
        { label: 'Standard', value: 'standard' },
        { label: 'Staff', value: 'staff' },
        { label: 'Media', value: 'media' },
        { label: 'Volunteer', value: 'volunteer' },
        { label: 'Security', value: 'security' },
        { label: 'Facilitator', value: 'facilitator' },
        { label: 'Instructor', value: 'instructor' }
    ];

    const handleInputChange = (field, value) => {
        setFormData(prev => ({ ...prev, [field]: value }));
    };

    const validateForm = () => {
        const newErrors = {};

        if (!formData.name.trim()) {
            newErrors.name = 'Name is required';
        }

        // Email is optional - only validate format if provided
        if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
            newErrors.email = 'Email format is invalid';
        }

        if (!formData.department.trim()) {
            newErrors.department = 'Department is required';
        }

        if (!formData.role.trim()) {
            newErrors.role = 'Role is required';
        }

        if (!formData.accessLevel) {
            newErrors.accessLevel = 'Access level is required';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = () => {
        if (validateForm()) {
            onSubmit(formData);
        }
    };

    return (
        <div className="space-y-4">
            <div className="text-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Create New Event Member</h3>
                <p className="text-sm text-gray-600">Add a new member specifically for this event</p>
            </div>

            <div className="grid grid-cols-1 gap-4">
                {/* Name */}
                <div className="field">
                    <label htmlFor="memberName" className="block text-sm font-medium text-gray-700 mb-1">
                        Full Name <span className="text-red-500">*</span>
                    </label>
                    <InputText
                        id="memberName"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        placeholder="Enter full name"
                        className={`w-full ${errors.name ? 'p-invalid' : ''}`}
                        disabled={loading}
                    />
                    {errors.name && <small className="p-error">{errors.name}</small>}
                </div>

                {/* Email */}
                <div className="field">
                    <label htmlFor="memberEmail" className="block text-sm font-medium text-gray-700 mb-1">
                        Email Address
                    </label>
                    <InputText
                        id="memberEmail"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        placeholder="Enter email address (optional)"
                        type="email"
                        className={`w-full ${errors.email ? 'p-invalid' : ''}`}
                        disabled={loading}
                    />
                    {errors.email && <small className="p-error">{errors.email}</small>}
                </div>

                {/* Phone */}
                <div className="field">
                    <label htmlFor="memberPhone" className="block text-sm font-medium text-gray-700 mb-1">
                        Phone Number
                    </label>
                    <InputText
                        id="memberPhone"
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        placeholder="Enter phone number (optional)"
                        type="tel"
                        className="w-full"
                        disabled={loading}
                    />
                </div>

                {/* Department */}
                <div className="field">
                    <label htmlFor="memberDepartment" className="block text-sm font-medium text-gray-700 mb-1">
                        Department <span className="text-red-500">*</span>
                    </label>
                    <InputText
                        id="memberDepartment"
                        value={formData.department}
                        onChange={(e) => handleInputChange('department', e.target.value)}
                        placeholder="Enter department"
                        className={`w-full ${errors.department ? 'p-invalid' : ''}`}
                        disabled={loading}
                    />
                    {errors.department && <small className="p-error">{errors.department}</small>}
                </div>

                {/* Role */}
                <div className="field">
                    <label htmlFor="memberRole" className="block text-sm font-medium text-gray-700 mb-1">
                        Role <span className="text-red-500">*</span>
                    </label>
                    <InputText
                        id="memberRole"
                        value={formData.role}
                        onChange={(e) => handleInputChange('role', e.target.value)}
                        placeholder="Enter role/position"
                        className={`w-full ${errors.role ? 'p-invalid' : ''}`}
                        disabled={loading}
                    />
                    {errors.role && <small className="p-error">{errors.role}</small>}
                </div>

                {/* Access Level */}
                <div className="field">
                    <label htmlFor="memberAccessLevel" className="block text-sm font-medium text-gray-700 mb-1">
                        Access Level <span className="text-red-500">*</span>
                    </label>
                    <Dropdown
                        id="memberAccessLevel"
                        value={formData.accessLevel}
                        options={accessLevels}
                        onChange={(e) => handleInputChange('accessLevel', e.value)}
                        optionLabel="label"
                        optionValue="value"
                        placeholder="Select access level"
                        className={`w-full ${errors.accessLevel ? 'p-invalid' : ''}`}
                        disabled={loading}
                    />
                    {errors.accessLevel && <small className="p-error">{errors.accessLevel}</small>}
                </div>
            </div>

            <div className="flex justify-end gap-2 pt-4">
                <Button
                    label="Cancel"
                    className="p-button-outlined"
                    style={{
                        backgroundColor: 'white',
                        color: 'black',
                        border: '1px solid #d1d5db',
                        padding: '10px 16px',
                        borderRadius: '6px',
                        minHeight: '44px'
                    }}
                    onClick={onCancel}
                    disabled={loading}
                />
                <Button
                    label="Create Member"
                    style={{
                        backgroundColor: '#00c3ac',
                        color: 'white',
                        border: '1px solid #00c3ac',
                        padding: '10px 16px',
                        borderRadius: '6px',
                        minHeight: '44px'
                    }}
                    onClick={handleSubmit}
                    disabled={loading}
                    loading={loading}
                />
            </div>
        </div>
    );
};

export default GroupDetailModal;
