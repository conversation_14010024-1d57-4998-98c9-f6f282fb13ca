import { useState, useEffect } from "react";
import { Dialog } from "primereact/dialog";
import { InputText } from "primereact/inputtext";
import { Toast } from 'primereact/toast';
import { useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import CardTypeCarousel from './components/CardTypeCarousel';
import BarcodeScanner from './components/BarcodeScanner';
import './CreateCardForm.css';

const CreateCardForm = ({
  fetchCards,
  isModalOpen,
  setIsModalOpen,
  editData,
  isEditMode,
  resetEditMode
}) => {
  const [formData, setFormData] = useState({
    name: "",
    number: "",
    type: null,
  });
  const [cardTypes, setCardTypes] = useState([]);
  const [selectedCardType, setSelectedCardType] = useState(null);
  const [loading, setLoading] = useState(false);
  const [showScanner, setShowScanner] = useState(false);
  const toast = useRef(null);
  const backendUrl = import.meta.env.VITE_BACKEND_URL;
  const token = localStorage.getItem("token");

  useEffect(() => {
    if (isEditMode && editData) {
      setFormData({
        name: editData.name,
        number: editData.number,
        type: editData.card_type?.id || null
      });
      // Find and set the selected card type for the carousel
      const cardType = cardTypes.find(ct => ct.id === editData.card_type?.id);
      setSelectedCardType(cardType || null);
    } else {
      setFormData({
        name: "",
        number: "",
        type: null,
      });
      setSelectedCardType(null);
    }
  }, [editData, isEditMode, cardTypes]);

  useEffect(() => {
    const fetchCardTypes = async () => {
      try {
        const response = await fetch(`${backendUrl}/card-types`, {
          headers: {
            Authorization: `Bearer ${token}`,
            Accept: "application/json",
          },
        });

        if (!response.ok) throw new Error("Failed to fetch card types.");

        const data = await response.json();
        const types = data.data || data;
        
        const formattedTypes = Array.isArray(types) ? types.map((type) => ({
          id: type.id,
          name: type.name,
          type_of_connection: type.type_of_connection,
          setting: type.setting
        })) : [];

        setCardTypes(formattedTypes);
      } catch (error) {
        console.error("Error fetching card types:", error);
        setCardTypes([]);
      }
    };

    if (isModalOpen) {
      fetchCardTypes();
    }
  }, [isModalOpen]);

  const handleChange = (name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCardTypeSelect = (cardType) => {
    setSelectedCardType(cardType);
    setFormData(prev => ({
      ...prev,
      type: cardType.id
    }));
  };

  const handleBarcodeScanned = (scannedData) => {
    setFormData(prev => ({
      ...prev,
      number: scannedData
    }));
    setShowScanner(false);
    toast.current.show({
      severity: 'success',
      summary: 'Barcode Scanned',
      detail: `Card number populated: ${scannedData}`,
      life: 3000
    });
  };

  const extractErrorMessage = (data) => {
    if (data.details) {
      const firstErrorField = Object.keys(data.details)[0];
      if (firstErrorField && Array.isArray(data.details[firstErrorField])) {
        return data.details[firstErrorField][0];
      }
    }
    return data.message || null;
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.name || !formData.number || !formData.type || !selectedCardType) {
      toast.current.show({
        severity: 'error',
        summary: 'Validation Error',
        detail: 'All fields are required. Please select a card type and fill in all details.',
        life: 3000
      });
      return;
    }

    let requestUrl, method, successMessage;
    const requestData = {
      name: formData.name,
      number: formData.number,
      type: formData.type,
    };

    if (isEditMode) {
      requestUrl = `${backendUrl}/new_update_cards/${editData.id}`;
      method = "PUT";
      successMessage = "Card updated successfully";
    } else {
      requestUrl = `${backendUrl}/create_cards`;
      method = "POST";
      successMessage = "The card has been created successfully.";
    }

    try {
      setLoading(true);
      const response = await fetch(requestUrl, {
        method: method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(requestData),
      });

      const data = await response.json();

      if (response.ok) {
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: successMessage,
          life: 3000
        });
        setFormData({ name: "", number: "", type: null });
        setSelectedCardType(null);
        setIsModalOpen(false);
        if (fetchCards) fetchCards();
        if (isEditMode && resetEditMode) resetEditMode();
      } else {
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: extractErrorMessage(data) || (isEditMode ? 'Failed to update card' : 'Failed to create card'),
          life: 3000
        });
      }
    } catch (error) {
      console.error("Error:", error);
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'An error occurred',
        life: 3000
      });
    } finally {
      setLoading(false);
    }
  };

  const onHide = () => {
    setFormData({ name: "", number: "", type: null });
    setSelectedCardType(null);
    setShowScanner(false);
    setIsModalOpen(false);
    if (isEditMode && resetEditMode) resetEditMode();
  };

  return (
    <>
      <Toast ref={toast} />
      <Dialog
        header={
          <div className="flex items-center gap-3">
            <div className="w-1 h-8 bg-gradient-to-b from-[#00c3ac] to-[#02aa96] rounded-full"></div>
            <h2 className="text-xl font-semibold text-gray-900">
              {isEditMode ? "Edit Card" : "Create New Card"}
            </h2>
          </div>
        }
        visible={isModalOpen}
        onHide={onHide}
        style={{
          width: "90vw",
          maxWidth: "700px",
          borderRadius: "16px",
          overflow: "hidden"
        }}
        modal
        className="modern-card-form-dialog"
        contentClassName="p-0"
      >
        <div className="p-6">
          <AnimatePresence>
            <motion.form
              onSubmit={handleSubmit}
              className="space-y-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              {/* Card Type Selection - Now at the top */}
              <div className="space-y-3">
                <label className="block text-lg font-semibold text-gray-800 mb-4">
                  Select Card Type
                </label>
                <CardTypeCarousel
                  cardTypes={cardTypes}
                  onCardTypeSelect={handleCardTypeSelect}
                  selectedCardType={selectedCardType}
                  itemsPerView={{
                    desktop: 3,
                    tablet: 2,
                    mobile: 1
                  }}
                />
                {cardTypes.length === 0 && (
                  <div className="text-center py-4">
                    <p className="text-red-500 text-sm">No card types available</p>
                  </div>
                )}
              </div>

              {/* Divider */}
              <div className="border-t border-gray-200 my-6"></div>

              {/* Card Details Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Card Details</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                      Card Name
                    </label>
                    <InputText
                      id="name"
                      value={formData.name}
                      onChange={(e) => handleChange("name", e.target.value)}
                      placeholder="Enter card name"
                      required
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#00c3ac] focus:border-transparent transition-all duration-200"
                    />
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="number" className="block text-sm font-medium text-gray-700">
                      Card Number
                    </label>
                    <div className="relative">
                      <InputText
                        id="number"
                        value={formData.number}
                        onChange={(e) => handleChange("number", e.target.value)}
                        placeholder="Enter card number or scan barcode"
                        required
                        className="w-full p-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#00c3ac] focus:border-transparent transition-all duration-200"
                      />
                      <button
                        type="button"
                        onClick={() => setShowScanner(true)}
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 p-2 text-gray-500 hover:text-[#00c3ac] transition-colors duration-200"
                        title="Scan Barcode"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          strokeWidth={1.5}
                          stroke="currentColor"
                          className="w-5 h-5"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M3.75 4.875c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5A1.125 1.125 0 013.75 9.375v-4.5zM3.75 14.625c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5a1.125 1.125 0 01-1.125-1.125v-4.5zM13.5 4.875c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5A1.125 1.125 0 0113.5 9.375v-4.5z"
                          />
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M6.75 6.75h.75v.75h-.75v-.75zM6.75 16.5h.75v.75h-.75v-.75zM16.5 6.75h.75v.75h-.75v-.75zM13.5 13.5h4.5v4.5h-4.5v-4.5z"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end gap-3 pt-6 border-t border-gray-200">
                <motion.button
                  type="button"
                  onClick={onHide}
                  disabled={loading}
                  className="px-6 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Cancel
                </motion.button>
                <motion.button
                  type="submit"
                  disabled={loading || cardTypes.length === 0 || !selectedCardType}
                  className="px-6 py-3 bg-[#00c3ac] hover:bg-[#02aa96] text-white rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {loading ? (
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      {isEditMode ? "Updating..." : "Creating..."}
                    </div>
                  ) : (
                    isEditMode ? "Update Card" : "Create Card"
                  )}
                </motion.button>
              </div>
            </motion.form>
          </AnimatePresence>
        </div>
      </Dialog>

      {/* Barcode Scanner Modal */}
      {showScanner && (
        <BarcodeScanner
          isOpen={showScanner}
          onClose={() => setShowScanner(false)}
          onBarcodeScanned={handleBarcodeScanned}
        />
      )}
    </>
  );
};

export default CreateCardForm;