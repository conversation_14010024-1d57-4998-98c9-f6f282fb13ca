import React, { forwardRef } from "react";

// مكون الصورة
const AvatarImage = forwardRef(({ src, alt, className = "", ...props }, ref) => (
  <img
    ref={ref}
    src={src}
    alt={alt}
    className={`h-full w-full object-cover ${className}`}
    onError={e => {
      e.target.style.display = 'none';
      if (e.target.nextSibling) {
        e.target.nextSibling.style.display = 'flex';
      }
    }}
    {...props}
  />
));

// مكون fallback
const AvatarFallback = forwardRef(({ children, className = "", ...props }, ref) => (
  <div
    ref={ref}
    className={`flex h-full w-full items-center justify-center rounded-full bg-gray-200 text-gray-700 font-medium text-sm ${className}`}
    style={{ display: 'none' }}
    {...props}
  >
    {children}
  </div>
));

// مكون الغلاف
const Avatar = forwardRef(({ children, className = "", ...props }, ref) => (
  <div
    ref={ref}
    className={`relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full ${className}`}
    {...props}
  >
    {children}
  </div>
));

export { Avatar, AvatarImage, AvatarFallback }; 