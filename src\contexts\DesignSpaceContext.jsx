import { createContext, useContext, useRef, useState, useEffect, useCallback } from "react";
import { isEmpty } from "lodash";
import PropTypes from "prop-types";

import { textElementConfig } from "@constants";
import { typesOptions } from "@constants/DesignSpaceConfig";
import { v4 as uuidv4 } from 'uuid';

const DesignSpaceContext = createContext({});

export const DesignSpaceProvider = (props) => {
    const [selectedIds, setSelectedIds] = useState([]);
    const [selectedElement, setSelectedElement] = useState([]);
    const [elements, setElements] = useState([]);
    const [isMultiSelectActive, setIsMultiSelectActive] = useState(false);
    const [cardType, setCardType] = useState(typesOptions[0].dimension);
    const [userSelectedCardType, setUserSelectedCardType] = useState(false); // تتبع ما إذا كان المستخدم قد اختار فعلاً

    // History for undo/redo functionality
    const [history, setHistory] = useState([]);
    const [currentHistoryIndex, setCurrentHistoryIndex] = useState(-1);

    // Layers management
    const [layers, setLayers] = useState([]);

    // Locked elements
    const [lockedElements, setLockedElements] = useState([]);

    // Grouped elements
    const [groups, setGroups] = useState({});

    // Zoom level
    const [zoomLevel, setZoomLevel] = useState(100);

    // Canvas background
    const [canvasBackground, setCanvasBackground] = useState("#ffffff");
    const [canvasBackgroundStyle, setCanvasBackgroundStyle] = useState(null);

    // إدارة التصميمات النصية المحفوظة
    const [savedTextStyles, setSavedTextStyles] = useState(() => {
        // جلب من localStorage إذا كان موجوداً
        if (typeof window !== 'undefined') {
            const saved = window.localStorage.getItem('savedTextStyles');
            return saved ? JSON.parse(saved) : [];
        }
        return [];
    });
    const [activeTextStyleId, setActiveTextStyleId] = useState(() => {
        if (typeof window !== 'undefined') {
            return window.localStorage.getItem('activeTextStyleId') || null;
        }
        return null;
    });

    // حفظ التصميم في localStorage عند التغيير
    useEffect(() => {
        if (typeof window !== 'undefined') {
            window.localStorage.setItem('savedTextStyles', JSON.stringify(savedTextStyles));
        }
    }, [savedTextStyles]);
    useEffect(() => {
        if (typeof window !== 'undefined') {
            if (activeTextStyleId)
                window.localStorage.setItem('activeTextStyleId', activeTextStyleId);
            else
                window.localStorage.removeItem('activeTextStyleId');
        }
    }, [activeTextStyleId]);

    // دالة لحفظ تصميم نصي جديد
    const saveTextStyle = (styleObj) => {
        const id = `style_${Date.now()}`;
        setSavedTextStyles(prev => [...prev, { id, ...styleObj }]);
        setActiveTextStyleId(id);
    };
    // جلب جميع التصميمات
    const getSavedTextStyles = () => savedTextStyles;
    // تفعيل تصميم
    const setActiveTextStyle = (id) => setActiveTextStyleId(id);
    // جلب التصميم النشط
    const getActiveTextStyle = () => savedTextStyles.find(s => s.id === activeTextStyleId) || null;
    // حذف تصميم
    const deleteTextStyle = (id) => {
        setSavedTextStyles(prev => prev.filter(s => s.id !== id));
        if (activeTextStyleId === id) setActiveTextStyleId(null);
    };

    const designSpaceRef = useRef();

    // Update history when elements change
    useEffect(() => {
        if (elements.length > 0) {
            // Only add to history if it's a new state (not during undo/redo)
            if (currentHistoryIndex === history.length - 1 || history.length === 0) {
                const newHistory = [...history.slice(0, currentHistoryIndex + 1), [...elements]];
                setHistory(newHistory);
                setCurrentHistoryIndex(newHistory.length - 1);
            }
        }
    }, [elements]);

    // Update layers when elements change
    useEffect(() => {
        setLayers(elements.map(el => ({ id: el.id, type: el.type, locked: lockedElements.includes(el.id) })));
    }, [elements, lockedElements]);

    const updateElement = (id, updates, keepSelection = true) => {
        // Store current selection
        const currentSelection = [...selectedIds];

        // Update the element
        setElements((prev) =>
            prev.map((el) => (el.id === id ? { ...el, ...updates } : el))
        );

        // Restore selection if needed
        if (keepSelection && currentSelection.includes(id)) {
            // Use a small delay to ensure the update completes first
            setTimeout(() => {
                setSelectedIds(currentSelection);
            }, 10);
        }
    };

    const defaultTextStyle = {
        fontFamily: 'Arial, sans-serif',
        fontSize: 16,
        fontWeight: 'normal',
        color: '#000000',
        backgroundColor: 'transparent',
        textAlign: 'left',
        lineHeight: 1.5,
        letterSpacing: 0,
        textDecoration: 'none',
        textTransform: 'none',
        fontStyle: 'normal',
        opacity: 1,
        textShadow: undefined,
        WebkitTextStroke: undefined,
        transform: undefined
    };

    const addElement = (type, value = "", customProps = {}) => {
        // تجاهل أي id قادم من customProps
        let newElement = {
            id: `el_${uuidv4()}`,
            type,
            x: customProps.x || 50,
            y: customProps.y || 50,
            value: isEmpty(value) ? (type === "img" ? "https://www.gravatar.com/avatar/?d=mp" : "") : value,
            width: customProps.width !== undefined ? customProps.width : 150,
            height: customProps.height !== undefined ? customProps.height : (150 * 9 / 16),
            borderRadius: 0,
            style: (type === 'text' && !customProps.style) ? { ...defaultTextStyle } : (customProps.style || {}),
            attributes: customProps.attributes || {},
            ...(type === "text" || type === "label" ? textElementConfig : {}),
            ...Object.fromEntries(Object.entries(customProps).filter(([key]) => key !== 'id')) // إزالة id من customProps
        };

        // Special handling for different element types
        if (type === 'shape') {
            newElement.backgroundColor = newElement.backgroundColor || "#4338ca";
        } else if (type === 'line') {
            newElement.strokeColor = newElement.strokeColor || "#4338ca";
            newElement.strokeWidth = newElement.strokeWidth || 2;
            // Lines are typically wider than tall
            if (!customProps.width || !customProps.height) {
                newElement.width = 150;
                newElement.height = 2;
            }
        } else if (type === 'frame') {
            newElement.borderColor = newElement.borderColor || "#4338ca";
            newElement.borderWidth = newElement.borderWidth || 2;
        } else if (type === 'text') {
            // Check if this is a field variable (custom field or other dynamic field)
            if (value && typeof value === 'string' && (value.startsWith('custom_field_') || ['name', 'type', 'position', 'department'].includes(value))) {
                // Mark this element as a dynamic field that should be replaced with user data
                newElement.isDynamicField = true;
                newElement.fieldKey = value; // Store the field key for later replacement
                newElement.className = "user-data"; // Add class for special handling
                // Set a placeholder value that will be replaced with actual data
                newElement.value = `${value}`;
            } else if (customProps.originalFieldKey) {
                // This is a custom field with a custom name, but we need to store the original key
                newElement.isDynamicField = true;
                newElement.fieldKey = customProps.originalFieldKey; // Store the original field key
                newElement.className = "user-data"; // Add class for special handling
                // The value is already set to the display text (custom name)
            }
        } else if (type === 'img' && value && !isEmpty(value)) {
            // Load image with original dimensions
            const img = new Image();
            img.onload = () => {
                // Get design space dimensions
                const designSpace = designSpaceRef.current;
                const designSpaceRect = designSpace ? designSpace.getBoundingClientRect() : null;
                
                let finalWidth = img.naturalWidth;
                let finalHeight = img.naturalHeight;
                
                // If design space is available, scale down large images
                if (designSpaceRect && cardType) {
                    const maxWidth = cardType.width * 0.8; // 80% of design space width
                    const maxHeight = cardType.height * 0.8; // 80% of design space height
                    
                    // Check if image is too large
                    if (img.naturalWidth > maxWidth || img.naturalHeight > maxHeight) {
                        // Calculate scale factor to fit within design space
                        const scaleX = maxWidth / img.naturalWidth;
                        const scaleY = maxHeight / img.naturalHeight;
                        const scale = Math.min(scaleX, scaleY);
                        
                        finalWidth = Math.round(img.naturalWidth * scale);
                        finalHeight = Math.round(img.naturalHeight * scale);
                    }
                }
                
                // Update the element with appropriate dimensions
                setElements(prevElements => 
                    prevElements.map(el => 
                        el.id === newElement.id 
                            ? { ...el, width: finalWidth, height: finalHeight }
                            : el
                    )
                );
            };
            img.onerror = () => {
                console.warn('Failed to load image for dimension detection:', value);
            };
            img.src = value;
        }

        setElements([...elements, newElement]);
        setSelectedIds([newElement.id]); // Select the newly added element
        return newElement.id; // Return the ID of the new element
    };

    // Undo function with improved handling
    const undo = () => {
        if (currentHistoryIndex > 0) {
            try {
                // First clear selection to avoid any conflicts
                setSelectedIds([]);
                // Then update the history index
                const newIndex = currentHistoryIndex - 1;
                setCurrentHistoryIndex(newIndex);
                // Finally update the elements from history
                setElements([...history[newIndex]]);
                console.log("Undo successful");
            } catch (error) {
                console.error("Error during undo:", error);
            }
        } else {
            console.log("Nothing to undo");
        }
    };

    // Redo function with improved handling
    const redo = () => {
        if (currentHistoryIndex < history.length - 1) {
            try {
                // First clear selection to avoid any conflicts
                setSelectedIds([]);
                // Then update the history index
                const newIndex = currentHistoryIndex + 1;
                setCurrentHistoryIndex(newIndex);
                // Finally update the elements from history
                setElements([...history[newIndex]]);
                console.log("Redo successful");
            } catch (error) {
                console.error("Error during redo:", error);
            }
        } else {
            console.log("Nothing to redo");
        }
    };

    // Lock/unlock elements
    const toggleLock = (ids) => {
        if (!Array.isArray(ids)) ids = [ids];

        setLockedElements(prev => {
            const newLockedElements = [...prev];
            ids.forEach(id => {
                const index = newLockedElements.indexOf(id);
                if (index === -1) {
                    newLockedElements.push(id);
                } else {
                    newLockedElements.splice(index, 1);
                }
            });
            return newLockedElements;
        });
    };

    // Group elements
    const groupElements = (ids) => {
        if (ids.length < 2) return;

        const groupId = `group_${uuidv4()}`;
        setGroups(prev => ({
            ...prev,
            [groupId]: ids
        }));

        // Select the group instead of individual elements
        setSelectedIds([groupId]);
    };

    // Ungroup elements
    const ungroupElements = (groupId) => {
        if (!groups[groupId]) return;

        const elementIds = groups[groupId];
        setGroups(prev => {
            const newGroups = {...prev};
            delete newGroups[groupId];
            return newGroups;
        });

        // Select the elements that were in the group
        setSelectedIds(elementIds);
    };

    // Change zoom level
    const zoom = (level) => {
        // Ensure zoom level is between 50 and 200
        const boundedLevel = Math.max(50, Math.min(200, level));
        setZoomLevel(boundedLevel);

        // Apply zoom using multiple approaches to ensure it works
        setTimeout(() => {
            // Try multiple selectors to find the design space
            const selectors = [
                '.design-space',
                '#design-space-content',
                '[data-design-space]',
                '.design-space-container .design-space'
            ];

            let targetElement = null;

            for (const selector of selectors) {
                targetElement = document.querySelector(selector);
                if (targetElement) {
                    console.log('Found design space element with selector:', selector);
                    break;
                }
            }

            if (targetElement) {
                // Add zooming class for CSS transitions
                targetElement.classList.add('zooming');

                // Remove any existing transition
                targetElement.style.transition = 'none';

                // Apply zoom immediately
                targetElement.style.transform = `scale(${boundedLevel / 100})`;
                targetElement.style.transformOrigin = 'center center';
                targetElement.style.willChange = 'transform';

                // Force a reflow to ensure the transform is applied
                targetElement.offsetHeight;

                // Add transition back for smooth future changes
                setTimeout(() => {
                    targetElement.style.transition = 'transform 0.3s ease';
                    // Remove zooming class after transition
                    setTimeout(() => {
                        targetElement.classList.remove('zooming');
                    }, 300);
                }, 10);

                console.log('✅ Zoom applied successfully to:', targetElement.className || targetElement.id, 'Level:', boundedLevel + '%');
            } else {
                console.error('❌ Design space element not found for zoom. Available elements:',
                    document.querySelectorAll('[class*="design"], [id*="design"]'));
            }
        }, 10); // Small delay to ensure DOM is ready
    };

    // Bring element to front
    const bringToFront = (id) => {
        setElements(prev => {
            const element = prev.find(el => el.id === id);
            if (!element) return prev;

            return [...prev.filter(el => el.id !== id), element];
        });
    };

    // Send element to back
    const sendToBack = (id) => {
        setElements(prev => {
            const element = prev.find(el => el.id === id);
            if (!element) return prev;

            return [element, ...prev.filter(el => el.id !== id)];
        });
    };

    // Function to update elements while preserving background
    const updateElements = (newElements) => {
        setElements(newElements);
        // Ensure background is preserved
        const designSpaceContent = document.getElementById('design-space-content');
        if (designSpaceContent) {
            const currentBackground = designSpaceContent.style.backgroundColor;
            const currentBackgroundStyle = {
                backgroundSize: designSpaceContent.style.backgroundSize,
                backgroundPosition: designSpaceContent.style.backgroundPosition,
                backgroundRepeat: designSpaceContent.style.backgroundRepeat
            };

            // Store background information
            setCanvasBackground(currentBackground || '#ffffff');
            setCanvasBackgroundStyle(currentBackgroundStyle);
        }
    };

    // Function to set canvas background with style support
    const setCanvasBackgroundWithStyle = (background, style = null) => {
        setCanvasBackground(background);
        setCanvasBackgroundStyle(style);
        // Dispatch a custom event to notify other components about the background change
        try {
            const event = new CustomEvent('canvasBackgroundChanged', {
                detail: {
                    background: background,
                    backgroundStyle: style
                }
            });
            window.dispatchEvent(event);
        } catch (error) {
            console.error("Error dispatching canvasBackgroundChanged event:", error);
        }
    };

    const [groupId, setGroupId] = useState(undefined);

    // Add logging wrapper for setGroupId
    const setGroupIdWithLogging = useCallback((value) => {
        console.log('setGroupId called with:', value, 'previous value was:', groupId);
        console.log('setGroupId type:', typeof value, 'is null:', value === null, 'is undefined:', value === undefined);
        setGroupId(value);
        console.log('setGroupId completed, new value should be:', value);
    }, [groupId]);

    // دالة لتحديث cardType وتتبع الاختيار اليدوي
    const setCardTypeWithUserSelection = useCallback((value, isUserSelection = false) => {
        console.log('setCardTypeWithUserSelection called with:', value, 'isUserSelection:', isUserSelection);
        setCardType(value);
        // Set userSelectedCardType to true if it's a user selection OR if we have a valid cardType with id
        if (isUserSelection || (value && value.id)) {
            setUserSelectedCardType(true);
        }
    }, []);

    return (
        <DesignSpaceContext.Provider value={{
            isMultiSelectActive, setIsMultiSelectActive,
            selectedElement, setSelectedElement,
            selectedIds, setSelectedIds,
            elements, setElements: updateElements,
            cardType, setCardType: setCardTypeWithUserSelection, userSelectedCardType,
            history, currentHistoryIndex,
            layers,
            lockedElements,
            groups,
            zoomLevel,
            canvasBackground,
            canvasBackgroundStyle,
            setCanvasBackgroundWithStyle,
            groupId,
            setGroupId: setGroupIdWithLogging,

            updateElement,
            designSpaceRef,
            addElement,
            undo,
            redo,
            toggleLock,
            groupElements,
            ungroupElements,
            zoom,
            bringToFront,
            sendToBack,
            savedTextStyles,
            saveTextStyle,
            getSavedTextStyles,
            activeTextStyleId,
            setActiveTextStyle,
            getActiveTextStyle,
            deleteTextStyle
        }}>
            {props.children}
        </DesignSpaceContext.Provider>
    )
}

export const useDesignSpace = () => {
    return useContext(DesignSpaceContext)
}

DesignSpaceProvider.propTypes = {
    children: PropTypes.node
};