import { VA<PERSON><PERSON>ATION_MESSAGES } from '../routes/api-routes';
import { userAPI } from './api';

// Validation functions that match backend rules
export const validateName = (name) => {
    if (!name || name.trim() === '') {
        return VALIDATION_MESSAGES.NAME_REQUIRED;
    }
    if (name.length < 3) {
        return VALIDATION_MESSAGES.NAME_MIN;
    }
    if (name.length > 32) {
        return VALIDATION_MESSAGES.NAME_MAX;
    }
    return null;
};

export const validateEmail = (email) => {
    if (!email || email.trim() === '') {
        return VALIDATION_MESSAGES.EMAIL_REQUIRED;
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        return VALIDATION_MESSAGES.EMAIL_INVALID;
    }
    if (email.length > 40) {
        return VALIDATION_MESSAGES.EMAIL_MAX;
    }
    return null;
};

// Email validation with availability check
export const validateEmailWithAvailability = async (email, userId = null) => {
    // First check basic email format
    const formatError = validateEmail(email);
    if (formatError) {
        return formatError;
    }

    // Then check availability
    try {
        const result = await userAPI.checkEmailAvailability(email, userId);
        if (!result.success) {
            return 'Failed to check email availability';
        }
        if (!result.available) {
            return 'This email address is already taken by another user';
        }
        return null;
    } catch (error) {
        return 'Failed to check email availability';
    }
};

// Debounced email validation for real-time checking
let emailCheckTimeout = null;
export const validateEmailWithDebounce = (email, userId = null, callback) => {
    // Clear previous timeout
    if (emailCheckTimeout) {
        clearTimeout(emailCheckTimeout);
    }

    // Check basic format immediately
    const formatError = validateEmail(email);
    if (formatError) {
        callback(formatError);
        return;
    }

    // Debounce availability check
    emailCheckTimeout = setTimeout(async () => {
        try {
            const result = await userAPI.checkEmailAvailability(email, userId);
            if (!result.success) {
                callback('Failed to check email availability');
                return;
            }
            if (!result.available) {
                callback('This email address is already taken by another user');
                return;
            }
            callback(null);
        } catch (error) {
            callback('Failed to check email availability');
        }
    }, 500); // 500ms delay
};

export const validatePhone = (phone, countryCode) => {
    if (!phone || phone.trim() === '') {
        return VALIDATION_MESSAGES.PHONE_REQUIRED;
    }
    
    if (countryCode && !validatePhoneNumber(phone, countryCode)) {
        return VALIDATION_MESSAGES.PHONE_INVALID;
    }
    
    return null;
};

export const validatePosition = (position) => {
    if (!position || position.trim() === '') {
        return VALIDATION_MESSAGES.POSITION_REQUIRED;
    }
    if (position.length < 2) {
        return VALIDATION_MESSAGES.POSITION_MIN;
    }
    if (position.length > 50) {
        return VALIDATION_MESSAGES.POSITION_MAX;
    }
    if (/^\d+$/.test(position)) {
        return VALIDATION_MESSAGES.POSITION_REGEX;
    }
    if (!/[a-zA-Z\u0600-\u06FF]/.test(position)) {
        return VALIDATION_MESSAGES.POSITION_REGEX;
    }
    return null;
};

export const validateDepartment = (department) => {
    if (!department || department.trim() === '') {
        return VALIDATION_MESSAGES.DEPARTMENT_REQUIRED;
    }
    if (department.length < 2) {
        return VALIDATION_MESSAGES.DEPARTMENT_MIN;
    }
    if (department.length > 50) {
        return VALIDATION_MESSAGES.DEPARTMENT_MAX;
    }
    if (/^\d+$/.test(department)) {
        return VALIDATION_MESSAGES.DEPARTMENT_REGEX;
    }
    if (!/[a-zA-Z\u0600-\u06FF]/.test(department)) {
        return VALIDATION_MESSAGES.DEPARTMENT_REGEX;
    }
    return null;
};

export const validateType = (type) => {
    if (!type || type.trim() === '') {
        return VALIDATION_MESSAGES.TYPE_REQUIRED;
    }
    if (type.length < 2) {
        return VALIDATION_MESSAGES.TYPE_MIN;
    }
    if (type.length > 50) {
        return VALIDATION_MESSAGES.TYPE_MAX;
    }
    if (/^\d+$/.test(type)) {
        return VALIDATION_MESSAGES.TYPE_REGEX;
    }
    if (!/[a-zA-Z\u0600-\u06FF]/.test(type)) {
        return VALIDATION_MESSAGES.TYPE_REGEX;
    }
    return null;
};

export const validateCustomField = (value, fieldName) => {
    if (!value || value.trim() === '') {
        return null; // Custom fields are optional
    }
    if (value.length < 3) {
        return `${fieldName} must be at least 3 characters long`;
    }
    if (value.length > 32) {
        return `${fieldName} must not exceed 32 characters`;
    }
    return null;
};

export const validatePassword = (password) => {
    if (!password || password.trim() === '') {
        return VALIDATION_MESSAGES.PASSWORD_REQUIRED;
    }
    return null;
};

// Phone number validation based on country
export const validatePhoneNumber = (phone, countryCode) => {
    if (!phone) return false;
    
    const cleaned = phone.replace(/\D/g, '');
    
    switch (countryCode) {
        case '+1': // US/Canada: 10 digits
            return cleaned.length === 10;
        case '+44': // UK: 10-11 digits
            return cleaned.length >= 10 && cleaned.length <= 11;
        case '+962': // Jordan: 10 digits (e.g., 0798307711)
            return cleaned.length === 10;
        case '+963': // Syria: 9 digits
        case '+961': // Lebanon: 8 digits
            return cleaned.length >= 8 && cleaned.length <= 9;
        case '+966': // Saudi Arabia: 9 digits
        case '+971': // UAE: 9 digits
        case '+965': // Kuwait: 8 digits
        case '+974': // Qatar: 8 digits
        case '+973': // Bahrain: 8 digits
        case '+968': // Oman: 8 digits
            return cleaned.length >= 8 && cleaned.length <= 9;
        case '+964': // Iraq: 10 digits
            return cleaned.length === 10;
        case '+90': // Turkey: 10 digits
            return cleaned.length === 10;
        default:
            return cleaned.length >= 7 && cleaned.length <= 15;
    }
};

// Format phone number based on country
export const formatPhoneNumber = (value, countryCode) => {
    if (!value) return value;
    
    const cleaned = value.replace(/\D/g, '');
    
    switch (countryCode) {
        case '+1': // US/Canada: (XXX) XXX-XXXX
            if (cleaned.length <= 3) return cleaned;
            if (cleaned.length <= 6) return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3)}`;
            return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6, 10)}`;
        case '+44': // UK: XXXX XXX XXX
            if (cleaned.length <= 4) return cleaned;
            if (cleaned.length <= 7) return `${cleaned.slice(0, 4)} ${cleaned.slice(4)}`;
            return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7, 10)}`;
        case '+962': // Jordan: XX XXXX XXXX (10 digits)
            if (cleaned.length <= 2) return cleaned;
            if (cleaned.length <= 6) return `${cleaned.slice(0, 2)} ${cleaned.slice(2)}`;
            return `${cleaned.slice(0, 2)} ${cleaned.slice(2, 6)} ${cleaned.slice(6, 10)}`;
        case '+966': // Saudi Arabia: XX XXX XXXX
            if (cleaned.length <= 2) return cleaned;
            if (cleaned.length <= 5) return `${cleaned.slice(0, 2)} ${cleaned.slice(2)}`;
            return `${cleaned.slice(0, 2)} ${cleaned.slice(2, 5)} ${cleaned.slice(5, 9)}`;
        case '+90': // Turkey: XXX XXX XXXX
            if (cleaned.length <= 3) return cleaned;
            if (cleaned.length <= 6) return `${cleaned.slice(0, 3)} ${cleaned.slice(3)}`;
            return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6, 10)}`;
        default:
            return cleaned;
    }
};

// Validate all fields at once
export const validateAllFields = (formData) => {
    const errors = {};
    
    errors.name = validateName(formData.name);
    errors.email = validateEmail(formData.email);
    errors.phone = validatePhone(formData.phone, formData.country_code);
    errors.position = validatePosition(formData.position);
    errors.department = validateDepartment(formData.department);
    errors.type = validateType(formData.type);
    errors.current_password = validatePassword(formData.current_password);
    
    // Validate custom fields
    for (let i = 1; i <= 10; i++) {
        const fieldName = `custom_field_${i}`;
        const error = validateCustomField(formData[fieldName], `Custom Field ${i}`);
        if (error) {
            errors[fieldName] = error;
        }
    }
    
    // Remove null values
    Object.keys(errors).forEach(key => {
        if (errors[key] === null) {
            delete errors[key];
        }
    });
    
    return errors;
};
