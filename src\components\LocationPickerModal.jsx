import { useState, useEffect, useRef, useCallback } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, useMapEvents, Popup } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import '../assets/css/location-picker.css';

// Fix for default markers in react-leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Custom marker icon
const customIcon = new L.Icon({
  iconUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyQzIgMTcuNTIgNi40OCAyMiAxMiAyMkMxNy41MiAyMiAyMiAxNy41MiAyMiAxMkMyMiA2LjQ4IDE3LjUyIDIgMTIgMloiIGZpbGw9IiM0MjcwZjAiLz4KPHBhdGggZD0iTTEyIDZDNi40OCA2IDIgMTAuNDggMiAxNkMyIDIxLjUyIDYuNDggMjYgMTIgMjZDMjEuNTIgMjYgMjYgMjEuNTIgMjYgMTZDMjYgMTAuNDggMjEuNTIgNiAxMiA2WiIgZmlsbD0id2hpdGUiLz4KPHBhdGggZD0iTTEyIDEwQzE0LjIwOTEgMTAgMTYgMTEuNzkwOSAxNiAxNEMxNiAxNi4yMDkxIDE0LjIwOTEgMTggMTIgMThDOS43OTA5IDE4IDggMTYuMjA5MSA4IDE0QzggMTEuNzkwOSA5Ljc5MDkgMTAgMTIgMTBaIiBmaWxsPSIjNDI3MGYwIi8+Cjwvc3ZnPgo=',
  iconSize: [32, 32],
  iconAnchor: [16, 32],
  popupAnchor: [0, -32],
});

// Component to handle map clicks
function MapClickHandler({ onLocationSelect }) {
  useMapEvents({
    click: (e) => {
      onLocationSelect(e.latlng);
    },
  });
  return null;
}

// Component to handle location search
function LocationSearch({ onLocationSelect }) {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [searchError, setSearchError] = useState('');
  const searchTimeoutRef = useRef(null);

  const searchLocation = async (query) => {
    if (!query.trim()) {
      setSearchResults([]);
      setSearchError('');
      return;
    }

    setIsSearching(true);
    setSearchError('');

    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&limit=5&addressdetails=1&countrycodes=jo,sa,ae,kw,qa,bh,om,iq,sy,lb,tr,eg,ps,ye,ma,tn,dz,us,uk,fr,de,ca,au,in,pk,bd,my,sg,ph,id,th`
      );
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data && Array.isArray(data)) {
        setSearchResults(data);
        if (data.length === 0) {
          setSearchError('No locations found. Try a different search term.');
        }
      } else {
        setSearchResults([]);
        setSearchError('Invalid response from search service.');
      }
    } catch (error) {
      console.error('Error searching location:', error);
      setSearchError('Failed to search location. Please try again.');
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Debounced search
  const handleSearchInput = (e) => {
    const query = e.target.value;
    setSearchQuery(query);
    
    // Clear previous timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    // Set new timeout for debounced search
    searchTimeoutRef.current = setTimeout(() => {
      if (query.trim().length >= 3) {
        searchLocation(query);
      } else {
        setSearchResults([]);
        setSearchError('');
      }
    }, 500);
  };

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim().length >= 3) {
      searchLocation(searchQuery);
    }
  };

  const selectLocation = (result) => {
    const lat = parseFloat(result.lat);
    const lng = parseFloat(result.lon);
    
    if (isNaN(lat) || isNaN(lng)) {
      setSearchError('Invalid coordinates received from search result.');
      return;
    }
    
    onLocationSelect({ lat, lng }, result.display_name);
    setSearchQuery(result.display_name);
    setSearchResults([]);
    setSearchError('');
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div className="search-section">
      <form onSubmit={handleSearch} className="search-form">
        <div className="search-input-wrapper">
          <svg className="search-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <input
            type="text"
            value={searchQuery}
            onChange={handleSearchInput}
            placeholder="Search for company address..."
            className="search-input"
            minLength={3}
          />
        </div>
        <button
          type="submit"
          disabled={isSearching || searchQuery.trim().length < 3}
          className="search-btn"
        >
          {isSearching ? (
            <>
              <span className="loading-spinner"></span>
              Searching...
            </>
          ) : (
            <>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              Search
            </>
          )}
        </button>
      </form>
      
      {/* Error Message */}
      {searchError && (
        <div className="search-error">
          <svg className="error-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          {searchError}
        </div>
      )}
      
      {/* Search Results */}
      {searchResults.length > 0 && (
        <div className="search-results">
          {searchResults.map((result, index) => (
            <div
              key={`${result.place_id || index}-${result.lat}-${result.lon}`}
              onClick={() => selectLocation(result)}
              className="search-result-item"
            >
              <div className="result-icon">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <div className="result-content">
                <div className="result-title">{result.display_name}</div>
                {result.address && (
                  <div className="result-subtitle">
                    {[
                      result.address.country,
                      result.address.state,
                      result.address.city,
                      result.address.town,
                      result.address.village
                    ].filter(Boolean).join(', ')}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
      
      {/* Search Tips */}
      {searchQuery.trim().length > 0 && searchQuery.trim().length < 3 && (
        <div className="search-tips">
          <svg className="info-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Type at least 3 characters to search
        </div>
      )}
    </div>
  );
}

const LocationPickerModal = ({
  isOpen,
  onClose,
  onLocationSelect,
  initialLocation = null,
  fetchUserLocation = null,
  isSaving = false,
  userId = null,
  mode = "edit"
}) => {
  const [isUpdatingLocation, setIsUpdatingLocation] = useState(false);
  
  // Function to show professional error message
  const showErrorMessage = (errorMessage) => {
    // Create a custom error notification
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, #f44336, #d32f2f);
      color: white;
      padding: 16px;
      border-radius: 8px;
      box-shadow: 0 4px 16px rgba(0,0,0,0.2);
      z-index: 10000;
      max-width: 320px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      font-size: 13px;
      line-height: 1.4;
      animation: slideInRight 0.3s ease-out;
      border-left: 3px solid #c62828;
    `;
    
    notification.innerHTML = `
      <div style="display: flex; align-items: flex-start; gap: 8px;">
        <div style="font-size: 20px; flex-shrink-0;">❌</div>
        <div style="flex: 1;">
          <div style="font-weight: bold; font-size: 14px; margin-bottom: 4px;">Update Failed</div>
          <div style="font-size: 12px; opacity: 0.9; margin-bottom: 6px;">
            ${errorMessage || 'Unknown error occurred'}
          </div>
          <div style="font-size: 11px; opacity: 0.8;">
            Please try again
          </div>
        </div>
        <button onclick="this.parentElement.parentElement.remove()" style="
          background: none;
          border: none;
          color: white;
          font-size: 16px;
          cursor: pointer;
          padding: 0;
          margin-left: 4px;
          opacity: 0.7;
          transition: opacity 0.2s;
          line-height: 1;
        " onmouseover="this.style.opacity='1'" onmouseout="this.style.opacity='0.7'">×</button>
      </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 6 seconds (longer for errors)
    setTimeout(() => {
      if (notification.parentElement) {
        notification.style.animation = 'slideOutRight 0.3s ease-out';
        setTimeout(() => {
          if (notification.parentElement) {
            notification.remove();
          }
        }, 300);
      }
    }, 6000);
  };
  
  const [position, setPosition] = useState(initialLocation?.coordinates || { lat: 31.9539, lng: 35.9106 });
  const [address, setAddress] = useState(initialLocation?.address || '');
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingFromAPI, setIsLoadingFromAPI] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [locationError, setLocationError] = useState('');
  const [apiLocationLoaded, setApiLocationLoaded] = useState(false);
  const [hasInitialized, setHasInitialized] = useState(false);
  const mapRef = useRef(null);

  // Function to update map view manually
  const updateMapView = (coords) => {
    if (mapRef.current && coords) {
      mapRef.current.setView([coords.lat, coords.lng], 15);
    }
  };

  // Fetch location from API
  const fetchLocationFromAPI = useCallback(async () => {
    if (!fetchUserLocation) return;
    
    setIsLoadingFromAPI(true);
    setLocationError('');
    
    try {
      const locationData = await fetchUserLocation();
      
      if (locationData && locationData.latitude && locationData.longitude) {
        const newPosition = { 
          lat: parseFloat(locationData.latitude), 
          lng: parseFloat(locationData.longitude) 
        };
        setPosition(newPosition);
        setAddress(locationData.address || '');
        
        const newLocation = {
          coordinates: newPosition,
          address: locationData.address || '',
          formatted: locationData.address || ''
        };
        setSelectedLocation(newLocation);
        setApiLocationLoaded(true);
        setLocationError('');
        console.log('🌍 Location loaded from API:', newLocation);
        
        // Update map view for API location
        setTimeout(() => {
          updateMapView(newPosition);
        }, 500);
      } else {
        setLocationError('No location data found in your profile.');
      }
    } catch (error) {
      console.error('Error fetching location from API:', error);
      setLocationError('Failed to load location from profile. Please try again.');
    } finally {
      setIsLoadingFromAPI(false);
    }
  }, []);

  // Get current location
  const getCurrentLocation = () => {
    setIsLoading(true);
    setLocationError('');
    
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          const newPosition = { lat: latitude, lng: longitude };
          setPosition(newPosition);
          reverseGeocode(newPosition);
          setIsLoading(false);
          
                  // Update map view for current location
        setTimeout(() => {
          updateMapView(newPosition);
        }, 100);
        },
        (error) => {
          console.error('Error getting location:', error);
          setIsLoading(false);
          switch(error.code) {
            case error.PERMISSION_DENIED:
              setLocationError('Location access denied. Please enable location services.');
              break;
            case error.POSITION_UNAVAILABLE:
              setLocationError('Location information unavailable.');
              break;
            case error.TIMEOUT:
              setLocationError('Location request timed out.');
              break;
            default:
              setLocationError('Failed to get current location.');
          }
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        }
      );
    } else {
      setIsLoading(false);
      setLocationError('Geolocation is not supported by this browser.');
    }
  };

  // Reverse geocoding to get address from coordinates
  const reverseGeocode = async (coords) => {
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${coords.lat}&lon=${coords.lng}&addressdetails=1&zoom=18`
      );
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data && data.display_name) {
        const fullAddress = data.display_name;
        setAddress(fullAddress);
        const newLocation = {
          coordinates: coords,
          address: fullAddress,
          formatted: fullAddress
        };
        setSelectedLocation(newLocation);
        setLocationError('');
        console.log('🌍 Location reverse geocoded:', newLocation);
        
        // Update map view for reverse geocoded location
        setTimeout(() => {
          updateMapView(coords);
        }, 100);
      } else {
        throw new Error('Invalid response from geocoding service');
      }
    } catch (error) {
      console.error('Error reverse geocoding:', error);
      setLocationError('Failed to get address for selected location.');
    }
  };

  // Handle location selection
  const handleLocationSelect = (coords, selectedAddress = null) => {
    setPosition(coords);
    setLocationError('');
    
    if (selectedAddress) {
      setAddress(selectedAddress);
      const newLocation = {
        coordinates: coords,
        address: selectedAddress,
        formatted: selectedAddress
      };
      setSelectedLocation(newLocation);
      console.log('🎯 Location selected from search:', newLocation);
      
              // Update map view for search results
        setTimeout(() => {
          updateMapView(coords);
        }, 100);
    } else {
      reverseGeocode(coords);
    }
  };



  // Initialize with initial location or fetch from API
  useEffect(() => {
    if (isOpen && !hasInitialized) {
      setHasInitialized(true);
      
      if (initialLocation && initialLocation.coordinates) {
        setPosition(initialLocation.coordinates);
        setAddress(initialLocation.address || '');
        setSelectedLocation(initialLocation);
        setLocationError('');
        setApiLocationLoaded(true);
        
        // Update map view for initial location
        setTimeout(() => {
          updateMapView(initialLocation.coordinates);
        }, 100);
      } else if (fetchUserLocation) {
        // Auto-fetch location from API when modal opens
        fetchLocationFromAPI();
      }
    }
  }, [isOpen, initialLocation, hasInitialized]);

  // Handle modal close
  const handleClose = () => {
    setLocationError('');
    setHasInitialized(false);
    onClose();
  };

  // Handle confirm location
  const handleConfirm = async () => {
    if (selectedLocation && !isUpdatingLocation) {
      console.log('🗺️ Modal Location Data to be sent in request:', {
        Address: selectedLocation.address,
        latitude: selectedLocation.coordinates?.lat,
        longitude: selectedLocation.coordinates?.lng,
        formatted: selectedLocation.formatted
      });

      // For registration mode (select), just return the location data without API call
      if (mode === "select") {
        console.log('📍 Registration mode - returning location data directly');
        onLocationSelect(selectedLocation);
        onClose();
        return;
      }

      // For edit mode, proceed with API update
      // Get user ID - prioritize passed userId prop, then try localStorage
      let finalUserId = userId;
      let user = null;
      
      if (!finalUserId) {
        // Try different possible localStorage keys for user data
        const possibleUserKeys = ['user', 'currentUser', 'authUser', 'userData'];
        const possibleTokenKeys = ['token', 'authToken', 'apiToken', 'accessToken'];
        
        for (const key of possibleUserKeys) {
          try {
            const userData = localStorage.getItem(key);
            if (userData) {
              user = JSON.parse(userData);
              if (user && user.id) {
                finalUserId = user.id;
                console.log(`Found user ID in localStorage.${key}:`, finalUserId);
                break;
              }
            }
          } catch (error) {
            console.warn(`Error parsing localStorage.${key}:`, error);
          }
        }
        
        // If no user ID found in localStorage, try sessionStorage
        if (!finalUserId) {
          for (const key of possibleUserKeys) {
            try {
              const userData = sessionStorage.getItem(key);
              if (userData) {
                user = JSON.parse(userData);
                if (user && user.id) {
                  finalUserId = user.id;
                  console.log(`Found user ID in sessionStorage.${key}:`, finalUserId);
                  break;
                }
              }
            } catch (error) {
              console.warn(`Error parsing sessionStorage.${key}:`, error);
            }
          }
        }
        
        // If still no user ID, try direct localStorage keys
        if (!finalUserId) {
          const directKeys = ['user_id', 'userId', 'id'];
          for (const key of directKeys) {
            const directId = localStorage.getItem(key) || sessionStorage.getItem(key);
            if (directId) {
              finalUserId = directId;
              console.log(`Found user ID in direct key ${key}:`, finalUserId);
              break;
            }
          }
        }
        
        // If still no user ID, try to decode it from the token (if it's a JWT)
        if (!finalUserId) {
          const token = localStorage.getItem('token') || localStorage.getItem('authToken');
          if (token) {
            try {
              // Try to decode JWT token to get user ID
              const tokenParts = token.split('.');
              if (tokenParts.length === 3) {
                const payload = JSON.parse(atob(tokenParts[1]));
                if (payload && payload.user_id) {
                  finalUserId = payload.user_id;
                  console.log('Found user ID in JWT token:', finalUserId);
                }
              }
            } catch (error) {
              console.warn('Error decoding JWT token:', error);
            }
          }
        }
      }
      
      if (!finalUserId) {
        console.error('User ID not found in any storage location');
        console.log('Available localStorage keys:', Object.keys(localStorage));
        console.log('Available sessionStorage keys:', Object.keys(sessionStorage));
        
        // Try to get user info from API if we have a token
        const tempToken = localStorage.getItem('token') || localStorage.getItem('authToken');
        if (tempToken) {
          try {
            console.log('Attempting to get user info from API...');
            const userResponse = await fetch('http://*************:8000/api/user', {
              method: 'GET',
              headers: {
                'Authorization': `Bearer ${tempToken}`,
                'Accept': 'application/json'
              }
            });
            
            if (userResponse.ok) {
              const userData = await userResponse.json();
              if (userData && userData.id) {
                finalUserId = userData.id;
                console.log('Found user ID from API:', finalUserId);
              }
            }
          } catch (error) {
            console.warn('Error getting user info from API:', error);
          }
        }
        
        if (!finalUserId) {
          // Last resort: try to get user ID from the fetchUserLocation function
          if (fetchUserLocation) {
            try {
              console.log('Attempting to get user ID from fetchUserLocation...');
              const locationData = await fetchUserLocation();
              // If fetchUserLocation returns user data, extract ID
              if (locationData && locationData.user_id) {
                finalUserId = locationData.user_id;
                console.log('Found user ID from fetchUserLocation:', finalUserId);
              }
            } catch (error) {
              console.warn('Error getting user ID from fetchUserLocation:', error);
            }
          }
          
          if (!finalUserId) {
            alert('User ID not found. Please log in again or refresh the page.');
            return;
          }
        }
      }

      // Get auth token - try multiple possible keys
      let token = null;
      const possibleTokenKeys = ['token', 'authToken', 'apiToken', 'accessToken'];
      
      for (const key of possibleTokenKeys) {
        const storedToken = localStorage.getItem(key);
        if (storedToken) {
          token = storedToken;
          console.log(`Found token in localStorage.${key}`);
          break;
        }
      }
      
      // If no token in localStorage, try sessionStorage
      if (!token) {
        for (const key of possibleTokenKeys) {
          const storedToken = sessionStorage.getItem(key);
          if (storedToken) {
            token = storedToken;
            console.log(`Found token in sessionStorage.${key}`);
            break;
          }
        }
      }
      
      if (!token) {
        console.error('Authentication token not found in any storage location');
        console.log('Available localStorage keys:', Object.keys(localStorage));
        console.log('Available sessionStorage keys:', Object.keys(sessionStorage));
        alert('Authentication token not found. Please log in again or refresh the page.');
        return;
      }

      setIsUpdatingLocation(true);

      try {
        // Prepare location data for API
        const locationData = {
          latitude: selectedLocation.coordinates?.lat?.toString(),
          longitude: selectedLocation.coordinates?.lng?.toString(),
          address: selectedLocation.address
        };

        console.log('📡 Sending location update to API:', locationData);

        // Call the new location API
        const response = await fetch(`http://*************:8000/api/datatable/users/${finalUserId}/location`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify(locationData)
        });

        const result = await response.json();

        if (response.ok) {
          console.log('✅ Location updated successfully:', result.data);
          
          // Call the original onLocationSelect with the updated data
          onLocationSelect(selectedLocation);
          
          // Close the modal
          onClose();
        } else {
          console.error('❌ Failed to update location:', result);
          
          // Show professional error message
          showErrorMessage(result.message || 'Unknown error occurred');
        }
      } catch (error) {
        console.error('❌ Error updating location:', error);
        showErrorMessage('Network error occurred. Please check your connection and try again.');
      } finally {
        setIsUpdatingLocation(false);
      }
    }
  };

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="location-modal-overlay" onClick={handleClose}>
      <div className="location-modal" onClick={(e) => e.stopPropagation()}>
        {/* Modal Header */}
        <div className="modal-header">
          <div className="modal-title">
            <svg className="title-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <h2>{mode === "select" ? "Select Company Address" : "Update Company Address"}</h2>
          </div>
          <button onClick={handleClose} className="close-btn">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Modal Content */}
        <div className="modal-content">
          {/* Loading Indicators */}
          {isLoadingFromAPI && (
            <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-400/30 rounded-xl p-4 mb-6">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
                  <span className="loading-spinner"></span>
                </div>
                <div>
                  <p className="text-blue-300 font-medium text-sm">Loading company address from profile...</p>
                  <p className="text-blue-400 text-xs">Please wait while we fetch your location data</p>
                </div>
              </div>
            </div>
          )}

          {isSaving && (
            <div className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-400/30 rounded-xl p-4 mb-6">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                  <span className="loading-spinner"></span>
                </div>
                <div>
                  <p className="text-green-300 font-medium text-sm">Saving company address...</p>
                  <p className="text-green-400 text-xs">Please wait while we update your location data</p>
                </div>
              </div>
            </div>
          )}

          {isUpdatingLocation && (
            <div className="bg-gradient-to-r from-blue-500/20 to-indigo-500/20 border border-blue-400/30 rounded-xl p-4 mb-6">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
                  <span className="loading-spinner"></span>
                </div>
                <div>
                  <p className="text-blue-300 font-medium text-sm">
                    {mode === "select" ? "Creating company location..." : "Updating company location..."}
                  </p>
                  <p className="text-blue-400 text-xs">
                    {mode === "select" ? "Please wait while we save your location data" : "Please wait while we save your new location data"}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Search Section */}
          <LocationSearch onLocationSelect={handleLocationSelect} />

          {/* Current Location Button */}
          <button
            type="button"
            onClick={getCurrentLocation}
            disabled={isLoading}
            className="current-location-btn"
          >
            {isLoading ? (
              <>
                <span className="loading-spinner"></span>
                Getting location...
              </>
            ) : (
              <>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                Use Current Location
              </>
            )}
          </button>

          {/* Location Error */}
          {locationError && (
            <div className="location-error">
              <svg className="error-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              {locationError}
            </div>
          )}

          {/* Map Container */}
          <div className="map-container">
            <MapContainer
              ref={mapRef}
              center={[position.lat, position.lng]}
              zoom={15}
              className="map"
              style={{ height: '500px', width: '100%' }}
            >
              <TileLayer
                attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
              />
              <Marker position={[position.lat, position.lng]} icon={customIcon}>
                <Popup>
                  <div className="popup-content">
                    <strong>Selected Company Address</strong>
                    <br />
                    <small className="text-gray-600">{address}</small>
                  </div>
                </Popup>
              </Marker>
              <MapClickHandler onLocationSelect={handleLocationSelect} />
            </MapContainer>
          </div>

          {/* Address Display */}
          {address && (
            <div className="address-display">
              <div className="address-header">
                <svg className="address-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <div className="address-label">
                  {apiLocationLoaded ? 'Current Company Address (from profile):' : 'Selected Company Address:'}
                </div>
              </div>
              <div className="address-text">{address}</div>
              <div className="coordinates">
                Coordinates: {Number(position.lat).toFixed(6)}, {Number(position.lng).toFixed(6)}
              </div>
            </div>
          )}

          {/* Instructions */}
          <div className="instructions">
            <svg className="info-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Your company address is loaded automatically from your profile. Click on the map to update it or search for a new address.
          </div>
        </div>

        {/* Modal Footer */}
        <div className="modal-footer">
          <button onClick={handleClose} className="cancel-btn">
            Cancel
          </button>
          <button
            onClick={handleConfirm}
            className="confirm-btn"
            disabled={!selectedLocation || isSaving || isUpdatingLocation}
          >
            {isSaving || isUpdatingLocation ? (
              <>
                <span className="loading-spinner"></span>
                {mode === "select" ? "Creating..." : "Updating..."}
              </>
            ) : (
              mode === "select" ? "Create Company Address" : "Update Company Address"
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default LocationPickerModal;
