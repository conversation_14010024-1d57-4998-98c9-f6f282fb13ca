
import React, { createContext, useContext, useState, useRef } from "react";
import { Toast } from 'primereact/toast';

const Global = createContext({});

export const GlobalProvider = (props) => {
    const toastRef = useRef(null);

    const [disableBtn, setDisableBtn] = useState(false);
     const [userType, setUserType] = useState(localStorage.getItem("user_type") || "manager");

    const [selectedMembers, setSelectedMembers] = useState({
        action: "create",
        groupData:{},
        data: []
    });

    // Global Dialogs Controls
    const [openDialog, setOpenDialog] = useState({
        createDesignTemplate: false,
        createGroup: false,
        updateGroup: false,
        addMember: false,

        companyDialog: false,
    });

    const dialogHandler = (key) => {
        setOpenDialog(prev => ({
            ...prev, [key]: !prev[key]
        }))
    }

    // Global Dialogs Controls 
    const [deleteDialog, setDeleteDialog] = useState({

    });

    const deleteDialogHandler = (key) => {
        setDeleteDialog(prev => ({
            ...prev, [key]: !prev[key]
        }))
    }

    const showToast = (severity, summary, detail, content = null) => {
        if (content == null)
            toastRef.current.show({ severity: severity, summary: summary, detail: detail, life: 3000 });
        else
            toastRef.current.show({ severity: severity, summary: summary, detail: detail, content: content, sticky: true });

    };

    return (
        <Global.Provider value={{
            disableBtn, setDisableBtn,
            userType, setUserType,

            openDialog, setOpenDialog, dialogHandler,
            deleteDialog, deleteDialogHandler,

            selectedMembers, setSelectedMembers,

            showToast
        }}>
            <Toast ref={toastRef} position="bottom-center"></Toast>
            {props.children}
        </Global.Provider>
    )
}

export const useGlobalContext = () => {
    return useContext(Global)
}