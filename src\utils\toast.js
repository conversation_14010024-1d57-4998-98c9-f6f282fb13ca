import { toast } from 'react-toastify';

// Toast configuration
export const toastConfig = {
    position: "top-right",
    autoClose: 5000,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
};

// Success toast
export const showSuccessToast = (message) => {
    toast.success(message, toastConfig);
};

// Error toast
export const showErrorToast = (message) => {
    toast.error(message, toastConfig);
};

// Warning toast
export const showWarningToast = (message) => {
    toast.warning(message, toastConfig);
};

// Info toast
export const showInfoToast = (message) => {
    toast.info(message, toastConfig);
};

// Show validation errors
export const showValidationErrors = (errors) => {
    if (typeof errors === 'object' && errors !== null) {
        Object.values(errors).forEach(error => {
            if (Array.isArray(error)) {
                error.forEach(err => showErrorToast(err));
            } else if (typeof error === 'string') {
                showErrorToast(error);
            }
        });
    } else if (typeof errors === 'string') {
        showErrorToast(errors);
    }
};

// Show API response errors
export const showAPIErrors = (response) => {
    if (response.details && typeof response.details === 'object') {
        // Show validation errors
        Object.values(response.details).forEach(error => {
            if (Array.isArray(error)) {
                error.forEach(err => showErrorToast(err));
            } else if (typeof error === 'string') {
                showErrorToast(error);
            }
        });
    } else if (response.message) {
        showErrorToast(response.message);
    } else if (response.error) {
        showErrorToast(response.error);
    } else {
        showErrorToast('An unexpected error occurred');
    }
};

// Show field-specific error
export const showFieldError = (fieldName, error) => {
    if (error) {
        showErrorToast(`${fieldName}: ${error}`);
    }
};

// Show success message for specific action
export const showActionSuccess = (action) => {
    const messages = {
        'profile_updated': 'Profile updated successfully',
        'email_verified': 'Email verified successfully',
        'verification_sent': 'Verification email sent successfully',
        'image_uploaded': 'Image uploaded successfully',
        'user_created': 'User created successfully',
        'password_changed': 'Password changed successfully'
    };
    
    const message = messages[action] || 'Action completed successfully';
    showSuccessToast(message);
};

// Show loading toast
export const showLoadingToast = (message = 'Loading...') => {
    return toast.loading(message, {
        ...toastConfig,
        autoClose: false,
    });
};

// Update loading toast
export const updateLoadingToast = (toastId, message, type = 'success') => {
    toast.update(toastId, {
        render: message,
        type: type,
        isLoading: false,
        autoClose: 5000,
    });
};

// Dismiss toast
export const dismissToast = (toastId) => {
    toast.dismiss(toastId);
};

// Clear all toasts
export const clearAllToasts = () => {
    toast.dismiss();
};

