import { useState, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

import { getFormErrorMessage } from '@utils/helper'
import { useLogInMutation } from '@quires';
import { useAuth } from '@contexts/AuthContext';

import { classNames } from 'primereact/utils';
import { InputText } from 'primereact/inputtext';
import { Password } from 'primereact/password';
import SideImage from './SideImage';
import Lanyard from './Lanyard';
import TwoFactorVerification from './TwoFactorVerification';

function Login () {
  const { t } = useTranslation("auth");
  const { formState: { errors }, handleSubmit, control } = useForm();
  const [showTwoFactor, setShowTwoFactor] = useState(false);
  const [loginResponse, setLoginResponse] = useState(null);
  const [isMobile, setIsMobile] = useState(false);
  const { login: authLogin } = useAuth();

    // Mobile detection useEffect
    useEffect(() => {
      const handleResize = () => {
        const mobileView = window.innerWidth < 768;
        setIsMobile(mobileView);
      };
  
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }, []);
  
  // Use login mutation with custom options to prevent auto-redirect
  const login = useLogInMutation({
    skipStorageAndRedirect: true,
    onSuccess: (response) => {
      console.log('🎯 Login mutation onSuccess called');
      handleLoginSuccess(response);
    }
  });

  const handleLoginSuccess = (response) => {
    console.log('🚀 handleLoginSuccess called');
    console.log('🔍 Current window.location:', window.location.href);
    
    console.log('🔍 Full response structure:', response);
    console.log('🔍 Raw response.user:', response.user);
    console.log('🔍 Raw response.data:', response.data);
    console.log('🔍 Raw response.data?.user:', response.data?.user);
    
    // Try both response.user and response.data.user structures
    const userData = response.user || response.data?.user;
    const twoFactorEnabled = userData?.two_factor_enabled === true;
    
    console.log('🔍 Checking 2FA condition:', {
      hasUserData: !!userData,
      userEmail: userData?.email,
      twoFactorEnabled: twoFactorEnabled,
      twoFactorValue: userData?.two_factor_enabled
    });
    
    // Only show 2FA modal if user has 2FA enabled
    if (userData && twoFactorEnabled) {
      console.log('🔐 2FA is ENABLED for user:', userData.email, '- setting up modal');
          console.log('👤 User data from response:', {
      id: userData.id,
      name: userData.name,
      email: userData.email,
      role: userData.role,
      user_type: userData.user_type,
      ALL_FIELDS: userData
    });
    
    console.log('🔍 DETAILED USER ANALYSIS:');
    console.log('  - userData.role:', userData.role, '(type:', typeof userData.role, ')');
    console.log('  - userData.user_type:', userData.user_type, '(type:', typeof userData.user_type, ')');
    console.log('  - JSON.stringify(userData):', JSON.stringify(userData, null, 2));
      
      // Make sure no permanent token exists
      localStorage.removeItem('token');
      localStorage.removeItem('user_id');
      localStorage.removeItem('user_name');
      localStorage.removeItem('user_email');
      localStorage.removeItem('user_role');
      localStorage.removeItem('user_type');
      
      // Store basic user data but NOT the token yet
      localStorage.setItem('temp_user_id', userData.id);
      localStorage.setItem('temp_user_name', userData.name);
      localStorage.setItem('temp_user_email', userData.email);
      localStorage.setItem('temp_token', response.token || response.data?.token);
      
      if (userData.user_type) {
        console.log('💾 Storing temp_user_type:', userData.user_type);
        localStorage.setItem('temp_user_type', userData.user_type);
      }
      // IGNORING role field - only using user_type
      
        localStorage.removeItem('two_factor_verified');
        localStorage.removeItem('two_factor_verified_at');
        
      console.log('🎯 About to set showTwoFactor to true');
      setLoginResponse(response);
      setShowTwoFactor(true);
      console.log('✅ showTwoFactor should now be true');
      
    } else if (userData && !twoFactorEnabled) {
      console.log('🔄 2FA is DISABLED for user:', userData.email, '- normal login flow');
      console.log('👤 User data from response (no 2FA):', {
        id: userData.id,
        name: userData.name,
        email: userData.email,
        role: userData.role,
        user_type: userData.user_type,
        ALL_FIELDS: userData
      });
      
      console.log('🔍 DETAILED USER ANALYSIS (no 2FA):');
      console.log('  - userData.role:', userData.role, '(type:', typeof userData.role, ')');
      console.log('  - userData.user_type:', userData.user_type, '(type:', typeof userData.user_type, ')');
      console.log('  - JSON.stringify(userData):', JSON.stringify(userData, null, 2));
      
      // Store user data normally and redirect to dashboard
      console.log('💾 Storing user data in localStorage (no 2FA)');
      localStorage.setItem('user_id', userData.id);
      localStorage.setItem('user_name', userData.name);
      localStorage.setItem('user_email', userData.email);
      localStorage.setItem('token', response.token || response.data?.token);
      
      if (userData.user_type) {
        console.log('💾 Storing user_type:', userData.user_type);
        localStorage.setItem('user_type', userData.user_type);
      }
      // IGNORING role field - only using user_type
      
      // Update AuthContext
      authLogin(userData.role, userData.user_type);
      
      // Force localStorage update
      console.log('🔍 Verification - Final localStorage after storing:', {
        user_role: localStorage.getItem('user_role'),
        user_type: localStorage.getItem('user_type'),
        user_id: localStorage.getItem('user_id'),
        token: !!localStorage.getItem('token')
      });
      
      // Set 2FA as verified for non-2FA accounts
      localStorage.setItem('two_factor_verified', 'true');
      localStorage.setItem('two_factor_verified_at', new Date().toISOString());
      
      console.log('🎯 About to redirect for user with role:', userData.role, 'and type:', userData.user_type);
      console.log('🎯 User data being passed to redirectToDashboard (no 2FA):', userData);
      redirectToDashboard(userData);
      } else {
      console.log('🔄 No userData found - cannot proceed');
      console.log('📍 Response structure issues - check API');
    }
    console.log('📍 handleLoginSuccess finished');
  };

  const onSubmit = async (data) => {
    console.log('🔍 Login form submitted with:', {
      email: data.email,
      password: '***hidden***'
    });
    
    console.log('🚀 Attempting login for email:', data.email);
    console.log('🔍 Expected user type: [Please verify this is a manager account]');
    
    try {
      await login.mutateAsync(data);
    } catch (error) {
      console.error("Login error:", error.response?.data || error.message);
    }
  };

  const redirectToDashboard = (user) => {
    console.log('🔍 redirectToDashboard received user:', user);
    console.log('🔍 Checking all possible role fields in user:', {
      role: user?.role,
      user_role: user?.user_role,
      type: user?.type,
      user_type: user?.user_type,
      position: user?.position,
      level: user?.level
    });
    
    // ONLY use user_type for redirect logic (ignore role completely)
    const userType = user?.user_type || user?.type || localStorage.getItem('user_type');
    
    console.log('🔍 Determining redirect URL based on user_type ONLY:', {
      user: user,
      userType: userType,
      userTypeFromStorage: localStorage.getItem('user_type'),
      IGNORED_role: user?.role
    });
    
    let redirectUrl = '/manager/dashboard'; // Default
        
        if (userType === 'admin') {
          redirectUrl = '/admin/dashboard';
          console.log('🎯 Admin detected (user_type=admin) - redirecting to admin dashboard');
        } else if (userType === 'manager') {
          redirectUrl = '/manager/dashboard';
          console.log('🎯 Manager detected (user_type=manager) - redirecting to manager dashboard');
        } else {
          console.log('🎯 Unknown user_type:', userType, '- using default manager dashboard');
        }
        
        console.log('🚀 Final redirect URL:', redirectUrl);
        window.location.href = redirectUrl;
  };

  const handleTwoFactorSuccess = () => {
    console.log('🎉 2FA Success callback called!');
    
    // Move temp data to permanent storage
    const tempUserId = localStorage.getItem('temp_user_id');
    const tempUserName = localStorage.getItem('temp_user_name');
    const tempUserEmail = localStorage.getItem('temp_user_email');
    const tempToken = localStorage.getItem('temp_token');
    const tempUserType = localStorage.getItem('temp_user_type');
    
    console.log('🔄 Moving temp data to permanent storage:', {
      tempUserId,
      tempUserName,
      tempUserEmail,
      tempUserType,
      hasTempToken: !!tempToken
    });
    
    if (tempToken) {
      localStorage.setItem('user_id', tempUserId);
      localStorage.setItem('user_name', tempUserName);
      localStorage.setItem('user_email', tempUserEmail);
      localStorage.setItem('token', tempToken);
      
      if (tempUserType) {
        console.log('💾 Moving temp_user_type to permanent:', tempUserType);
        localStorage.setItem('user_type', tempUserType);
      }
      // IGNORING temp_user_role - only using user_type
      
      // Update AuthContext
      const userRole = localStorage.getItem('user_role');
      authLogin(userRole, tempUserType);
      
      // Force localStorage update and verify
      console.log('🔍 Verification - Final localStorage after 2FA success:', {
        user_role: localStorage.getItem('user_role'),
        user_type: localStorage.getItem('user_type'),
        user_id: localStorage.getItem('user_id'),
        token: !!localStorage.getItem('token')
      });
      
      // Clear temp data
      localStorage.removeItem('temp_user_id');
      localStorage.removeItem('temp_user_name');
      localStorage.removeItem('temp_user_email');
      localStorage.removeItem('temp_token');
      localStorage.removeItem('temp_user_type');
      
      localStorage.setItem('two_factor_verified', 'true');
      localStorage.setItem('two_factor_verified_at', new Date().toISOString());
      
      console.log('✅ User data moved to permanent storage successfully');
    }
    
    setShowTwoFactor(false);
    console.log('📍 Redirecting to dashboard...');
    const userForRedirect = loginResponse?.data?.user || { user_type: tempUserType };
    console.log('🎯 User data being passed to redirectToDashboard:', userForRedirect);
    redirectToDashboard(userForRedirect);
  };

  const handleTwoFactorClose = () => {
    console.log('❌ 2FA Modal closed - clearing temp data');
    setShowTwoFactor(false);
    setLoginResponse(null);
    
    // Clear temp data
    localStorage.removeItem('temp_user_id');
    localStorage.removeItem('temp_user_name');
    localStorage.removeItem('temp_user_email');
    localStorage.removeItem('temp_token');
    localStorage.removeItem('temp_user_type');
    
    console.log('🧹 Temp data cleared');
  };
  

  
  return (
    <div className='relative w-full h-[100vh] overflow-hidden flex'>
      <div className="absolute top-0 right-0 z-50">
        {!isMobile && <Lanyard position={[0, 0, 20]} gravity={[0, -40, 0]} />}
      </div>
      <SideImage />
      <div className='w-full sm:w-7/12 h-full px-12 flex flex-col justify-center '>
        <h1 className='text-3xl font-bold mb-12'>{ t('login') }</h1>
        <form onSubmit={ (e) => { 
          e.preventDefault(); 
          console.log('Form submitted, calling handleSubmit');
          handleSubmit(onSubmit)(e); 
        }} className="flex flex-col">

          {/* email */ }
          <div className="mb-2 w-full">
            <div className="field">
              <label className="form-label mb-2 text-[#696F79]">{ t('inputs.email') }</label>
              <span className="p-float-label mt-2">
                <Controller name="email" control={ control }
                  rules={ {
                    required: t('messages.required'),
                    pattern: {
                      value: /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
                      message: t('messages.email'),
                    }
                  } }
                  render={ ({ field, fieldState }) => (
                    <InputText
                      id={ field.email }
                      { ...field }
                      ref={ field.ref }
                      placeholder={ t('placeholders.email') }
                      className={ `w-full text-[#696F79] p-3 ${ classNames({ 'p-invalid': fieldState.invalid }) }` } />
                  ) } />
              </span>
              { getFormErrorMessage('email', errors) }
            </div>
          </div>

          {/* password */ }
          <div className="mb-2 form-password-toggle w-full my-4">
            <div className="field ">
              <div className="flex justify-between">
                <label className="form-label mb-2 text-[#696F79]" htmlFor="password"> { t('inputs.password') } </label>
                <Link to="/forget-password">
                  <small className='text-[#696F79]'>{ t('go_to.forget_pass') }</small>
                </Link>
              </div>
              <span className="p-float-label">
                <Controller name="password" control={ control }
                  rules={ { required: t('messages.required') } }
                  render={ ({ field, fieldState }) => (
                    <Password
                      id={ field.password }
                      { ...field }
                      ref={ field.ref }
                      placeholder={ t('placeholders.password') }
                      className={ `text-[#696F79] pass-input w-full ${ classNames({ 'p-invalid': fieldState.invalid }) }` }
                      toggleMask
                      feedback={false} />
                  ) } />
              </span>
              { getFormErrorMessage('password', errors) }
            </div>
          </div>

          <button className="main-btn w-full mt-8 text-md sm:text-xl"> { t('login') } </button>
        </form>

        <p className="mt-3 text-[#696F79] text-sm">
          { t('go_to.register') }
          <Link to="/register">
            <span className="mx-1 capitalize text-[#427bf0]">{ t('register') }</span>
          </Link>
        </p>
      </div>

      {console.log('🔍 Login component render - showTwoFactor:', showTwoFactor)}
      {showTwoFactor && console.log('🎯 About to render TwoFactorVerification')}
      {showTwoFactor && (
        <TwoFactorVerification
          visible={showTwoFactor}
          onSuccess={handleTwoFactorSuccess}
          onClose={handleTwoFactorClose}
        />
      )}
    </div>
  )
}

export default Login