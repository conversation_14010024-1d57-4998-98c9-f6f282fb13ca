import React, { useState, useEffect } from 'react';
import { Dialog } from 'primereact/dialog';
import { But<PERSON> } from 'primereact/button';
import { Dropdown } from 'primereact/dropdown';
import { InputText } from 'primereact/inputtext';
import { motion } from 'framer-motion';
import { FiCreditCard, FiSearch, FiX, FiCheck } from 'react-icons/fi';
import { useFetchCards } from '@quires/useGetCards ';

const AddCardModal = ({ visible, onHide, group, event, onAddCard, isMobile }) => {
    const { data: cardsData, isLoading, isError } = useFetchCards();
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedCard, setSelectedCard] = useState(null);
    const [filteredCards, setFilteredCards] = useState([]);
    const [loading, setLoading] = useState(false);

    // Filter cards based on group type and assignment status
    useEffect(() => {
        if (!cardsData?.data || !group) return;

        const cardData = cardsData.data;
        let filtered = [...cardData];

        // Filter by group's card type - only show cards of the same type
        if (group.card_type_name) {
            filtered = filtered.filter(card => 
                card.card_type?.name === group.card_type_name
            );
        }

        // Apply search filter
        if (searchQuery.trim()) {
            const searchLower = searchQuery.toLowerCase();
            filtered = filtered.filter(card => {
                return (
                    card.name?.toLowerCase().includes(searchLower) ||
                    card.number?.toLowerCase().includes(searchLower) ||
                    card.manager_name?.toLowerCase().includes(searchLower)
                );
            });
        }

        // Sort cards: unassigned first, then assigned (grayed out)
        filtered.sort((a, b) => {
            const aAssigned = a.manager_name && a.manager_name !== 'No Manager' && a.manager_name !== '';
            const bAssigned = b.manager_name && b.manager_name !== 'No Manager' && b.manager_name !== '';
            
            if (aAssigned && !bAssigned) return 1;
            if (!aAssigned && bAssigned) return -1;
            return 0;
        });

        setFilteredCards(filtered);
    }, [cardsData?.data, group, searchQuery]);

    const handleAddCard = async () => {
        if (!selectedCard) return;

        try {
            setLoading(true);
            
            // Call API to assign card to group
            // await axiosInstance.post(`/event-groups/${group.id}/cards`, {
            //     card_id: selectedCard.id
            // });

            // For now, simulate the API call
            const cardAssignment = {
                id: selectedCard.id,
                name: selectedCard.name,
                type: selectedCard.card_type?.name || group.card_type_name,
                status: 'active',
                assignedAt: new Date().toISOString()
            };

            onAddCard(cardAssignment);
            setSelectedCard(null);
            setSearchQuery('');
            onHide();
        } catch (error) {
            console.error('Error adding card to group:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleCancel = () => {
        setSelectedCard(null);
        setSearchQuery('');
        onHide();
    };

    const isCardAssigned = (card) => {
        return card.manager_name && card.manager_name !== 'No Manager' && card.manager_name !== '';
    };

    const isCardSelectable = (card) => {
        return !isCardAssigned(card);
    };

    if (!group) return null;

    return (
        <Dialog
            header={
                <div className="flex items-center gap-2">
                    <FiCreditCard className="text-purple-600" size={20} />
                    <span className="text-lg font-semibold">Add Card to {group.title}</span>
                </div>
            }
            visible={visible}
            style={{
                width: isMobile ? '95vw' : '60vw',
                maxWidth: isMobile ? '95vw' : '700px',
                maxHeight: '90vh'
            }}
            breakpoints={{
                '960px': '80vw',
                '641px': '95vw'
            }}
            onHide={handleCancel}
            className="add-card-modal"
            contentStyle={{
                maxHeight: isMobile ? 'calc(90vh - 60px)' : 'auto',
                overflow: 'auto',
                padding: isMobile ? '15px' : '20px'
            }}
            modal
        >
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
            >
                {/* Group Information */}
                <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                    <div className="text-sm text-purple-800">
                        <p><strong>Group:</strong> {group.title}</p>
                        <p><strong>Card Type:</strong> {group.card_type_name}</p>
                        <p><strong>Event:</strong> {event?.name}</p>
                    </div>
                </div>

                {/* Search */}
                <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">
                        Search Cards
                    </label>
                    <div className="relative">
                        <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                        <InputText
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            placeholder="Search by card name, number, or manager..."
                            className="w-full pl-10"
                        />
                    </div>
                </div>

                {/* Card Selection */}
                <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">
                        Available Cards ({filteredCards.filter(card => isCardSelectable(card)).length} unassigned)
                    </label>
                    
                    {isLoading ? (
                        <div className="text-center py-8">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
                            <p className="text-gray-500 mt-2">Loading cards...</p>
                        </div>
                    ) : isError ? (
                        <div className="text-center py-8 text-red-600">
                            <p>Error loading cards. Please try again.</p>
                        </div>
                    ) : filteredCards.length === 0 ? (
                        <div className="text-center py-8 text-gray-500">
                            <FiCreditCard size={32} className="mx-auto mb-2 opacity-50" />
                            <p>No cards found for this card type</p>
                        </div>
                    ) : (
                        <div className="max-h-64 overflow-y-auto border border-gray-200 rounded-lg">
                            {filteredCards.map((card) => {
                                const assigned = isCardAssigned(card);
                                const selectable = isCardSelectable(card);
                                const isSelected = selectedCard?.id === card.id;
                                
                                return (
                                    <div
                                        key={card.id}
                                        className={`p-3 border-b border-gray-100 last:border-b-0 cursor-pointer transition-colors ${
                                            assigned 
                                                ? 'bg-gray-50 opacity-60 cursor-not-allowed' 
                                                : isSelected 
                                                    ? 'bg-purple-100 border-purple-300' 
                                                    : 'hover:bg-gray-50'
                                        }`}
                                        onClick={() => selectable && setSelectedCard(card)}
                                    >
                                        <div className="flex items-center justify-between">
                                            <div className="flex-1">
                                                <div className="flex items-center gap-2">
                                                    <span className="font-medium">{card.name}</span>
                                                    {isSelected && (
                                                        <FiCheck className="text-purple-600" size={16} />
                                                    )}
                                                </div>
                                                <div className="text-sm text-gray-500">
                                                    Number: {card.number} • Type: {card.card_type?.name}
                                                </div>
                                                {assigned && (
                                                    <div className="text-xs text-red-600 mt-1">
                                                        Assigned to: {card.manager_name}
                                                    </div>
                                                )}
                                            </div>
                                            {!selectable && (
                                                <div className="text-xs bg-red-100 text-red-600 px-2 py-1 rounded">
                                                    Assigned
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    )}
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end gap-2 pt-4">
                    <Button
                        label="Cancel"
                        className="p-button-outlined"
                        style={{
                            backgroundColor: 'white',
                            color: 'black',
                            border: '1px solid #d1d5db',
                            padding: '10px 16px',
                            borderRadius: '6px',
                            minHeight: '44px'
                        }}
                        onClick={handleCancel}
                        disabled={loading}
                    />
                    <Button
                        label="Add Card"
                        style={{
                            backgroundColor: '#00c3ac',
                            color: 'white',
                            border: '1px solid #00c3ac',
                            padding: '10px 16px',
                            borderRadius: '6px',
                            minHeight: '44px'
                        }}
                        onMouseEnter={(e) => {
                            if (!isMobile && !loading && selectedCard) {
                                e.target.style.transform = 'translateY(-1px)';
                                e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
                            }
                        }}
                        onMouseLeave={(e) => {
                            if (!isMobile) {
                                e.target.style.transform = 'translateY(0)';
                                e.target.style.boxShadow = 'none';
                            }
                        }}
                        onClick={handleAddCard}
                        disabled={!selectedCard || loading}
                        loading={loading}
                    />
                </div>
            </motion.div>
        </Dialog>
    );
};

export default AddCardModal;
