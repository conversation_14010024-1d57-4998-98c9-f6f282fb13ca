import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import CardCarousel from '../CardCarousel';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }) => <div {...props}>{children}</div>,
    button: ({ children, ...props }) => <button {...props}>{children}</button>,
  },
  AnimatePresence: ({ children }) => children,
}));

// Mock CardPreview component
jest.mock('../CardPreview', () => {
  return function MockCardPreview({ card, isSelected, onClick }) {
    return (
      <div 
        data-testid={`card-preview-${card.id}`}
        onClick={onClick}
        className={isSelected ? 'selected' : ''}
      >
        {card.name}
      </div>
    );
  };
});

const mockCards = [
  {
    id: 1,
    name: 'Business Card',
    type_of_connection: 'NFC',
    number_of_colors: 4,
    setting: { width: 350, height: 200 }
  },
  {
    id: 2,
    name: 'ID Badge',
    type_of_connection: 'QR',
    number_of_colors: 2,
    setting: { width: 300, height: 450 }
  },
  {
    id: 3,
    name: 'Access Card',
    type_of_connection: 'Bluetooth',
    number_of_colors: 1,
    setting: { width: 400, height: 250 }
  }
];

describe('CardCarousel', () => {
  const defaultProps = {
    cards: mockCards,
    onCardSelect: jest.fn(),
    selectedCard: null,
    itemsPerView: { desktop: 3, tablet: 2, mobile: 1 }
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock window.innerWidth for responsive tests
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    });
  });

  it('renders carousel with cards', () => {
    render(<CardCarousel {...defaultProps} />);
    
    expect(screen.getByText('Business Card')).toBeInTheDocument();
    expect(screen.getByText('ID Badge')).toBeInTheDocument();
    expect(screen.getByText('Access Card')).toBeInTheDocument();
  });

  it('shows navigation arrows', () => {
    render(<CardCarousel {...defaultProps} />);
    
    expect(screen.getByLabelText('Previous cards')).toBeInTheDocument();
    expect(screen.getByLabelText('Next cards')).toBeInTheDocument();
  });

  it('disables previous button on first slide', () => {
    render(<CardCarousel {...defaultProps} />);
    
    const prevButton = screen.getByLabelText('Previous cards');
    expect(prevButton).toBeDisabled();
  });

  it('calls onCardSelect when card is clicked', () => {
    const onCardSelect = jest.fn();
    render(<CardCarousel {...defaultProps} onCardSelect={onCardSelect} />);
    
    fireEvent.click(screen.getByTestId('card-preview-1'));
    expect(onCardSelect).toHaveBeenCalledWith(mockCards[0]);
  });

  it('shows correct card counter', () => {
    render(<CardCarousel {...defaultProps} />);
    
    expect(screen.getByText(/1-3 of 3 card types/)).toBeInTheDocument();
  });

  it('handles empty cards array', () => {
    render(<CardCarousel {...defaultProps} cards={[]} />);
    
    expect(screen.getByText('No card types available')).toBeInTheDocument();
  });

  it('shows mobile swipe hint on mobile', () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 500,
    });

    render(<CardCarousel {...defaultProps} />);
    
    expect(screen.getByText(/Swipe left or right to navigate/)).toBeInTheDocument();
  });

  it('handles keyboard navigation', () => {
    render(<CardCarousel {...defaultProps} />);
    
    const carousel = screen.getByRole('region');
    
    // Test right arrow key
    fireEvent.keyDown(carousel, { key: 'ArrowRight' });
    // Since we're mocking framer-motion, we can't test actual slide changes
    // but we can verify the component doesn't crash
    
    expect(carousel).toBeInTheDocument();
  });

  it('shows slide indicators', () => {
    render(<CardCarousel {...defaultProps} />);
    
    const indicators = screen.getAllByRole('tab');
    expect(indicators).toHaveLength(1); // With 3 cards and 3 items per view, only 1 slide
  });

  it('applies accessibility attributes', () => {
    render(<CardCarousel {...defaultProps} />);
    
    expect(screen.getByRole('region')).toHaveAttribute('aria-label', 'Card types carousel');
    expect(screen.getByRole('tablist')).toHaveAttribute('aria-label', 'Carousel navigation');
  });
});
