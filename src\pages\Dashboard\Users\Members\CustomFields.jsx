import { Controller } from 'react-hook-form'
import { classNames } from 'primereact/utils'
import { InputText } from 'primereact/inputtext';
import { Button } from 'primereact/button';
import _ from 'lodash';
import { useState, useEffect } from 'react';
import axiosInstance from '../../../../config/Axios';

import { getFormErrorMessage } from '@utils/helper'

export default function CustomFields({ numberOfInputs = 10, control, errors, setValue, companyUserType, setCompanyUserType }) {
    const [userCustomFieldNames, setUserCustomFieldNames] = useState({});

    // Fetch user custom field names
    useEffect(() => {
        const fetchUserCustomFieldNames = async () => {
            try {
                const response = await axiosInstance.get('/custom-field-names');
                if (response.data.success) {
                    const fieldNamesMap = {};
                    response.data.data.forEach(field => {
                        fieldNamesMap[field.field_key] = field.field_name;
                    });
                    setUserCustomFieldNames(fieldNamesMap);
                }
            } catch (error) {
                console.error('Error fetching user custom field names:', error);
            }
        };

        fetchUserCustomFieldNames();
    }, []);

    return (
        _.times(numberOfInputs, (index) => {
            return <CustomField 
                control={control} 
                index={index} 
                errors={errors} 
                userCustomFieldNames={userCustomFieldNames}
            />;
        }))
}

function CustomField({ control, index, errors, userCustomFieldNames }) {
    index += 1
    const fieldKey = `custom_field_${index}`;
    const fieldLabel = userCustomFieldNames[fieldKey] || `Custom Field ${index}`;
    const fieldName = `custom_field_${index}`;

    return (
        <div className='w-6/12 mb-3 px-2'>
            <div className="field bg-gradient-to-br from-slate-50 to-gray-50 border border-slate-200 rounded-2xl p-6 shadow-sm">
                <div className="flex items-center justify-between mb-3">
                    <label className="text-sm font-semibold text-slate-700 flex items-center gap-2">
                        <i className="pi pi-cog text-slate-600"></i>
                        {fieldLabel}
                    </label>
                    <div className="flex items-center gap-2">
                        {userCustomFieldNames[fieldKey] ? (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <i className="pi pi-check-circle mr-1"></i>
                                Have Tag
                            </span>
                        ) : (
                            <Button
                                type="button"
                                label="Create Tag"
                                icon="pi pi-tag"
                                className="p-button-sm bg-gradient-to-r from-orange-50 to-amber-50 border border-orange-200 text-orange-700 hover:from-orange-100 hover:to-amber-100 hover:border-orange-300 rounded-lg px-4 py-2 font-medium shadow-sm hover:shadow-md transition-all duration-300"
                                onClick={() => {
                                    // Navigate to settings page with custom-fields tab
                                    window.location.href = '/manager/settings?tab=custom-fields';
                                }}
                                tooltip="Create Tag"
                            />
                        )}
                    </div>
                </div>
                <Controller name={fieldName} control={control}
                    rules={{ 
                        required: false,
                        minLength: {
                            value: 3,
                            message: `${fieldLabel} must be at least 3 characters long`
                        },
                        maxLength: {
                            value: 32,
                            message: `${fieldLabel} cannot exceed 32 characters`
                        }
                    }}
                    render={({ field, fieldState }) => (
                        <InputText
                            id={field.name}
                            {...field}
                            ref={field.ref}
                            className={`w-full p-inputtext-sm p-4 rounded-xl border-2 focus:border-slate-500 focus:ring-2 focus:ring-slate-200 transition-all duration-200 ${fieldState.invalid ? 'border-red-500 bg-red-50' : 'border-slate-300 bg-white'}`}
                            placeholder={`Enter ${fieldLabel.toLowerCase()}`}
                            maxLength={32}
                        />
                    )} />
                {getFormErrorMessage(fieldName, errors)}
            </div>
        </div>
    )
}


