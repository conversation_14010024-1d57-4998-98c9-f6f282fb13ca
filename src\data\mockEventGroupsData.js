// Mock data for Event Groups - Separate from main application groups
// This file contains sample data structures for event-specific groups and members

// Event-specific groups that are independent from main application groups
export const mockEventGroups = [
  {
    id: 'evt-grp-attendees-1',
    title: "Attendees",
    description: "General event attendees",
    event_id: 1,
    card_type_name: "Standard Access",
    status: "active",
    memberCount: 330,
    created_at: "2024-07-01T09:00:00Z",
    event_context: true,
    access_level: "standard",
    is_default_attendees: true
  },
  {
    id: 'evt-grp-1',
    title: "VIP Attendees",
    description: "High-priority guests and speakers for the event",
    event_id: 1,
    card_type_name: "VIP Access",
    status: "active",
    memberCount: 12,
    created_at: "2024-07-01T10:30:00Z",
    event_context: true, // Flag to indicate this is an event-specific group
    access_level: "vip"
  },
  {
    id: 'evt-grp-2',
    title: "Workshop Facilitators",
    description: "Speakers and facilitators for workshop sessions",
    event_id: 1,
    card_type_name: "Facilitator Access",
    status: "active",
    memberCount: 8,
    created_at: "2024-07-02T14:20:00Z",
    event_context: true,
    access_level: "facilitator"
  },
  {
    id: 'evt-grp-3',
    title: "Media Representatives",
    description: "Press and media personnel covering the event",
    event_id: 1,
    card_type_name: "Media Pass",
    status: "active",
    memberCount: 6,
    created_at: "2024-07-03T09:15:00Z",
    event_context: true,
    access_level: "media"
  },
  {
    id: 'evt-grp-4',
    title: "General Attendees",
    description: "Standard event participants",
    event_id: 1,
    card_type_name: "Standard Access",
    status: "active",
    memberCount: 150,
    created_at: "2024-07-04T11:45:00Z",
    event_context: true,
    access_level: "standard"
  },
  {
    id: 'evt-grp-attendees-2',
    title: "Attendees",
    description: "General event attendees",
    event_id: 2,
    card_type_name: "Standard Access",
    status: "active",
    memberCount: 79,
    created_at: "2024-07-20T16:00:00Z",
    event_context: true,
    access_level: "standard",
    is_default_attendees: true
  },
  {
    id: 'evt-grp-5',
    title: "Product Demo Team",
    description: "Team members presenting product demonstrations",
    event_id: 2,
    card_type_name: "Demo Access",
    status: "active",
    memberCount: 10,
    created_at: "2024-07-20T16:45:00Z",
    event_context: true,
    access_level: "demo"
  },
  {
    id: 'evt-grp-6',
    title: "Launch Event Sponsors",
    description: "Sponsor representatives and partners",
    event_id: 2,
    card_type_name: "Sponsor Access",
    status: "active",
    memberCount: 15,
    created_at: "2024-07-21T10:30:00Z",
    event_context: true,
    access_level: "sponsor"
  },
  {
    id: 'evt-grp-attendees-3',
    title: "Attendees",
    description: "General event attendees",
    event_id: 3,
    card_type_name: "Standard Access",
    status: "active",
    memberCount: 20,
    created_at: "2024-08-01T09:00:00Z",
    event_context: true,
    access_level: "standard",
    is_default_attendees: true
  },
  {
    id: 'evt-grp-attendees-4',
    title: "Attendees",
    description: "General event attendees",
    event_id: 4,
    card_type_name: "Standard Access",
    status: "active",
    memberCount: 75,
    created_at: "2024-09-01T09:00:00Z",
    event_context: true,
    access_level: "standard",
    is_default_attendees: true
  },
  {
    id: 'evt-grp-attendees-5',
    title: "Attendees",
    description: "General event attendees",
    event_id: 5,
    card_type_name: "Standard Access",
    status: "active",
    memberCount: 111,
    created_at: "2024-07-10T09:00:00Z",
    event_context: true,
    access_level: "standard",
    is_default_attendees: true
  },
  {
    id: 'evt-grp-7',
    title: "Training Participants",
    description: "Employees participating in security training",
    event_id: 5,
    card_type_name: "Training Access",
    status: "active",
    memberCount: 45,
    created_at: "2024-07-10T09:30:00Z",
    event_context: true,
    access_level: "training"
  },
  {
    id: 'evt-grp-8',
    title: "Security Instructors",
    description: "Trainers and security experts leading sessions",
    event_id: 5,
    card_type_name: "Instructor Access",
    status: "active",
    memberCount: 5,
    created_at: "2024-07-11T14:20:00Z",
    event_context: true,
    access_level: "instructor"
  }
];

// Event-specific members that are associated with event groups
export const mockEventMembers = [
  {
    id: 'evt-mem-1',
    name: "Dr. Sarah Mitchell",
    email: "<EMAIL>",
    department: "External Speaker",
    role: "Keynote Speaker",
    event_id: 1,
    group_id: 'evt-grp-1',
    image: null,
    event_context: true,
    access_level: "vip"
  },
  {
    id: 'evt-mem-2',
    name: "James Rodriguez",
    email: "<EMAIL>",
    department: "Executive",
    role: "CEO",
    event_id: 1,
    group_id: 'evt-grp-1',
    image: null,
    event_context: true,
    access_level: "vip"
  },
  {
    id: 'evt-mem-3',
    name: "Lisa Chen",
    email: "<EMAIL>",
    department: "Training",
    role: "Workshop Leader",
    event_id: 1,
    group_id: 'evt-grp-2',
    image: null,
    event_context: true,
    access_level: "facilitator"
  },
  {
    id: 'evt-mem-4',
    name: "Mark Thompson",
    email: "<EMAIL>",
    department: "Media",
    role: "Tech Journalist",
    event_id: 1,
    group_id: 'evt-grp-3',
    image: null,
    event_context: true,
    access_level: "media"
  },
  {
    id: 'evt-mem-5',
    name: "Emily Davis",
    email: "<EMAIL>",
    department: "Product",
    role: "Product Manager",
    event_id: 2,
    group_id: 'evt-grp-5',
    image: null,
    event_context: true,
    access_level: "demo"
  },
  {
    id: 'evt-mem-6',
    name: "Robert Kim",
    email: "<EMAIL>",
    department: "External Partner",
    role: "Sponsor Representative",
    event_id: 2,
    group_id: 'evt-grp-6',
    image: null,
    event_context: true,
    access_level: "sponsor"
  },
  {
    id: 'evt-mem-7',
    name: "Jennifer Walsh",
    email: "<EMAIL>",
    department: "IT Security",
    role: "Security Analyst",
    event_id: 5,
    group_id: 'evt-grp-7',
    image: null,
    event_context: true,
    access_level: "training"
  },
  {
    id: 'evt-mem-8',
    name: "David Park",
    email: "<EMAIL>",
    department: "External Trainer",
    role: "Security Consultant",
    event_id: 5,
    group_id: 'evt-grp-8',
    image: null,
    event_context: true,
    access_level: "instructor"
  }
];

// Available event groups that can be added to events (not yet associated)
export const mockAvailableEventGroups = [
  {
    id: 'evt-grp-avail-1',
    title: "Event Volunteers",
    description: "Volunteers helping with event coordination",
    card_type_name: "Volunteer Access",
    status: "active",
    memberCount: 0,
    event_context: true,
    access_level: "volunteer"
  },
  {
    id: 'evt-grp-avail-2',
    title: "Technical Support",
    description: "IT and technical support team for the event",
    card_type_name: "Tech Support Access",
    status: "active",
    memberCount: 0,
    event_context: true,
    access_level: "tech_support"
  },
  {
    id: 'evt-grp-avail-3',
    title: "Catering Staff",
    description: "Food service and catering personnel",
    card_type_name: "Staff Access",
    status: "active",
    memberCount: 0,
    event_context: true,
    access_level: "staff"
  },
  {
    id: 'evt-grp-avail-4',
    title: "Security Personnel",
    description: "Event security and safety team",
    card_type_name: "Security Access",
    status: "active",
    memberCount: 0,
    event_context: true,
    access_level: "security"
  },
  {
    id: 'evt-grp-avail-5',
    title: "Photography Team",
    description: "Official event photographers and videographers",
    card_type_name: "Media Access",
    status: "active",
    memberCount: 0,
    event_context: true,
    access_level: "media"
  }
];

// Available members that can be added to event groups
export const mockAvailableEventMembers = [
  {
    id: 'evt-mem-avail-1',
    name: "Alex Johnson",
    email: "<EMAIL>",
    department: "Operations",
    role: "Event Coordinator",
    image: null,
    event_context: true
  },
  {
    id: 'evt-mem-avail-2',
    name: "Maria Garcia",
    email: "<EMAIL>",
    department: "IT",
    role: "Technical Support",
    image: null,
    event_context: true
  },
  {
    id: 'evt-mem-avail-3',
    name: "Tom Wilson",
    email: "<EMAIL>",
    department: "External Service",
    role: "Catering Manager",
    image: null,
    event_context: true
  },
  {
    id: 'evt-mem-avail-4',
    name: "Rachel Brown",
    email: "<EMAIL>",
    department: "External Service",
    role: "Security Officer",
    image: null,
    event_context: true
  },
  {
    id: 'evt-mem-avail-5',
    name: "Kevin Lee",
    email: "<EMAIL>",
    department: "External Service",
    role: "Event Photographer",
    image: null,
    event_context: true
  },
  {
    id: 'evt-mem-avail-6',
    name: "Sophie Turner",
    email: "<EMAIL>",
    department: "Marketing",
    role: "Event Marketing Specialist",
    image: null,
    event_context: true
  }
];

// Event-specific card types for event groups {THIS IS THE ONLY ONE USED AS OF RIGHT NOW}
export const mockEventCardTypes = [
  {
    id: 'evt-card-1',
    name: "VIP Access",
    description: "Full access to all event areas and VIP amenities",
    color: "#FFD700",
    permissions: ["full_access", "vip_lounge", "priority_seating", "backstage_access"],
    event_context: true
  },
  {
    id: 'evt-card-2',
    name: "Facilitator Access",
    description: "Access for workshop leaders and facilitators",
    color: "#9B59B6",
    permissions: ["workshop_areas", "presentation_tools", "speaker_lounge"],
    event_context: true
  },
  {
    id: 'evt-card-3',
    name: "Media Pass",
    description: "Special access for media personnel and press",
    color: "#E94B3C",
    permissions: ["media_area", "interview_access", "photo_rights", "press_room"],
    event_context: true
  },
  {
    id: 'evt-card-4',
    name: "Standard Access",
    description: "General event access with standard amenities",
    color: "#4A90E2",
    permissions: ["general_access", "standard_seating", "networking_areas"],
    event_context: true
  },
  {
    id: 'evt-card-5',
    name: "Staff Access",
    description: "Access for event staff and service personnel",
    color: "#50C878",
    permissions: ["staff_areas", "service_access", "back_of_house"],
    event_context: true
  }
];

// Helper functions for event group management
export const getEventGroupsByEventId = (eventId) => {
  return mockEventGroups.filter(group => group.event_id === eventId);
};

export const getAvailableEventGroupsForEvent = (eventId) => {
  const associatedGroups = getEventGroupsByEventId(eventId);
  const associatedIds = associatedGroups.map(g => g.id);
  return mockAvailableEventGroups.filter(group => !associatedIds.includes(group.id));
};

export const getEventMembersByGroupId = (groupId) => {
  return mockEventMembers.filter(member => member.group_id === groupId);
};

export const getAvailableEventMembersForEvent = (eventId) => {
  const eventMembers = mockEventMembers.filter(member => member.event_id === eventId);
  const assignedIds = eventMembers.map(m => m.id);
  return mockAvailableEventMembers.filter(member => !assignedIds.includes(member.id));
};
