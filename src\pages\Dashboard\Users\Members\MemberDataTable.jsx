import { useEffect, useRef, useState, useCallback } from 'react';
import { isEmpty } from 'lodash';
import { Dialog } from 'primereact/dialog';
import { motion } from 'framer-motion';
import { saveAs } from 'file-saver';
import { FaFileExport, FaFileImport } from 'react-icons/fa';
import AssignGroupDialog from '../Groups/AssignGroupDialog';
import AddMemberDialog from './AddMemberDialog';
import GroupForm from '../../Backages/CreateGroupForm';
import 'react-toastify/dist/ReactToastify.css';
import '../../../../styles/mobile-modals.css';
import { usersTableConfig, defaultTableConfig } from '@constants';
import { useDeleteUserMutation } from '@quires/user';
import { useMembersDataTableContext } from '@contexts/MembersDataTableContext';
import { useGlobalContext } from '@contexts/GlobalContext';
import { useQueryParams } from '@utils/helper';
import { useLayout } from '@contexts/LayoutContext';
import { DataTable } from 'primereact/datatable';
import { Tooltip } from 'primereact/tooltip';
import { Column } from 'primereact/column';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import { Toast } from 'primereact/toast';

import ImportDialog from './ImportDialog';
import axiosInstance from "../../../../config/Axios";
import { TfiTrash } from "react-icons/tfi";
import { FiEdit } from "react-icons/fi";
import { FaRegEye } from 'react-icons/fa';

import profile_img from "@images/Profile_img.jpg"

import { FaSearch } from 'react-icons/fa';
import { HiDotsVertical } from 'react-icons/hi';

import { createPortal } from 'react-dom';
import Container from '@components/Container';

const statusStyles = {
    printed: "bg-[#22C55E] ",
    unprinted: "bg-[#64748B] ",
    onProgress: "bg-[#D97706] ",
}

// Mobile FAB Styles
const mobileFabStyles = `
  .mobile-fab-container {
    position: fixed;
    bottom: 24px;
    right: 24px;
    z-index: 1000;
  }

  .mobile-fab {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg, #00c3ac 0%, #02aa96 100%);
    color: white;
    border: none;
    box-shadow: 0 4px 16px rgba(0, 195, 172, 0.4), 0 2px 8px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform: scale(1);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
  }

  .mobile-fab:hover {
    transform: scale(1.1) translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 195, 172, 0.5), 0 4px 12px rgba(0, 0, 0, 0.2);
    background: linear-gradient(135deg, #02aa96 0%, #00c3ac 100%);
  }

  .mobile-fab:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }

  /* Ensure FAB stays above other elements but below burger menu */
  @media (max-width: 768px) {
    .mobile-fab-container {
      position: fixed;
      bottom: 24px;
      right: 24px;
      z-index: 1000;
    }
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = mobileFabStyles;
  if (!document.head.querySelector('style[data-mobile-fab-members]')) {
    styleElement.setAttribute('data-mobile-fab-members', 'true');
    document.head.appendChild(styleElement);
  }
}

function MemberDataTable() {
    const { totalRecords, lazyParams, setLazyParams, data, loading } = useMembersDataTableContext();
    const { dialogHandler, openDialog, selectedMembers, setSelectedMembers } = useGlobalContext();
    const { isMobile } = useLayout();
    const queryParams = useQueryParams();
    const groupID = queryParams.get("group-id");
    const designID = queryParams.get("design-id");
    const deleteRow = useDeleteUserMutation();
    const toast = useRef(null);
    const userType = localStorage.getItem('user_type');

    // All state declarations
    const [importDialogVisible, setImportDialogVisible] = useState(false);
    const [selectedMember, setSelectedMember] = useState();
    const [actionType, setActionType] = useState("create");
    const [searchQuery, setSearchQuery] = useState('');
    const [isCreateGroupModalOpen, setIsCreateGroupModalOpen] = useState(false);
    const [subscriptionError, setSubscriptionError] = useState(null);
    const [subscriptionLoading, setSubscriptionLoading] = useState(true);
    const [noPackage, setNoPackage] = useState(false);
    const [mobileActionMenuOpen, setMobileActionMenuOpen] = useState(null);
    const [selectedCard, setSelectedCard] = useState(null);
    const [allCards, setAllCards] = useState([]);
    const [currentCardIndex, setCurrentCardIndex] = useState(0);
    const [isCardViewOpen, setIsCardViewOpen] = useState(false);
    const [isFlipped, setIsFlipped] = useState(false);

    const activeBtn = isEmpty(selectedMembers.data);

    // Delete confirmation handler
    const handleDeleteClick = (rowData) => {
        confirmDialog({
            message: `Are you sure you want to delete "${rowData.name || 'this member'}"? This action cannot be undone.`,
            header: 'Delete Member Confirmation',
            icon: 'pi pi-exclamation-triangle',
            acceptClassName: 'p-button-danger',
            acceptLabel: 'Yes, Delete',
            rejectLabel: 'Cancel',
            accept: () => deleteRowHandler(rowData),
        });
    };

    // Delete logic: remove from UI and show toast
    const deleteRowHandler = async (rowData) => {
        await deleteRow.mutateAsync({
            id: rowData?.id,
        }, {
            onSuccess: () => {
                setLazyParams(prev => ({ ...prev, ...usersTableConfig }));

                // Show success toast
                toast.current.show({
                    severity: 'success',
                    summary: 'Success',
                    detail: 'Member deleted successfully',
                    life: 3000
                });
            },
            onError: () => {
                // Show error toast
                toast.current.show({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to delete member',
                    life: 3000
                });
            }
        });
    };

    // Define dataHandler first
    const dataHandler = useCallback((e = {}) => {
        const nameFilter = { value: searchQuery, matchMode: 'contains' };

        setLazyParams(prev => {
            const newFirst = e?.first !== undefined ? e.first : prev.first;
            const newRows = e?.rows !== undefined ? e.rows : prev.rows;
            const newPage = e?.first !== undefined ? Math.floor(e.first / (e.rows || prev.rows)) : prev.page;
            const newSortField = e?.sortField !== undefined ? e.sortField : prev.sortField;
            const newSortOrder = e?.sortOrder !== undefined ? e.sortOrder : prev.sortOrder;
            const prevNameFilter = prev.filters?.name?.value;
            const filtersChanged = prevNameFilter !== searchQuery;

            if (
                newFirst !== prev.first ||
                newRows !== prev.rows ||
                newPage !== prev.page ||
                newSortField !== prev.sortField ||
                newSortOrder !== prev.sortOrder ||
                filtersChanged
            ) {
                return {
                    ...prev,
                    filters: {
                        ...prev.filters,
                        name: nameFilter
                    },
                    first: newFirst,
                    rows: newRows,
                    page: newPage,
                    sortField: newSortField,
                    sortOrder: newSortOrder,
                };
            }
            return prev;
        });
    }, [searchQuery, setLazyParams]);

    // Then use it in effects
    useEffect(() => {
        const timeout = setTimeout(() => {
            if (lazyParams.filters?.name?.value !== searchQuery) {
                dataHandler();
            }
        }, 100); // تقليل التأخير من 500ms إلى 100ms

        return () => clearTimeout(timeout);
    }, [searchQuery, dataHandler, lazyParams.filters?.name?.value]);

    useEffect(() => {
        setLazyParams({
            ...defaultTableConfig,
            ...usersTableConfig,
            groupID: groupID,
            designID: designID
        })
    }, [groupID, designID, setLazyParams]);

    // Subscription check effect
    useEffect(() => {
        const checkSubscription = async () => {
            try {
                const token = localStorage.getItem('token');
                const userId = localStorage.getItem('user_id');
                if (!token || !userId) {
                    setSubscriptionError({ message: 'User not authenticated.' });
                    setSubscriptionLoading(false);
                    return;
                }
                const response = await axiosInstance.get(`packages/show-package-by-id/${userId}`, {
                    headers: { Authorization: `Bearer ${token}` }
                });
                if (!response.data || Object.keys(response.data).length === 0 || response.data.error === 'No package found for this user') {
                    setNoPackage(true);
                } else {
                    setNoPackage(false);
                }
                setSubscriptionError(null);
            } catch (error) {
                if (error.response && error.response.data) {
                    const errMsg = error.response.data.error?.toLowerCase() || '';
                    if (error.response.data.error === "Your subscription has expired. Please renew your subscription to continue.") {
                        setSubscriptionError({ message: error.response.data.error });
                    } else if (
                        errMsg.includes('not found') ||
                        errMsg.includes('no package') ||
                        errMsg.includes('no active package found for this user') ||
                        errMsg.includes('must have an active package')
                    ) {
                        setNoPackage(true);
                    } else {
                        setSubscriptionError(null);
                    }
                } else {
                    setSubscriptionError(null);
                }
            } finally {
                setSubscriptionLoading(false);
            }
        };
        checkSubscription();
    }, []);

    // Mobile action menu cleanup effect
    useEffect(() => {
        if (!isMobile) {
            setMobileActionMenuOpen(null);
        }
    }, [isMobile]);

    // Initial setup effect
    // useEffect(() => {
    //     setLazyParams({
    //         ...defaultTableConfig,
    //         ...usersTableConfig,
    //         groupID: groupID,
    //         designID: designID
    //     })
    // }, [groupID, designID, setLazyParams]);

    // Data handler callback
    // const dataHandler = useCallback((e = {}) => {
    //     const nameFilter = { value: searchQuery, matchMode: 'contains' };

    //     setLazyParams(prev => {
    //         const newFirst = e?.first !== undefined ? e.first : prev.first;
    //         const newRows = e?.rows !== undefined ? e.rows : prev.rows;
    //         const newPage = e?.first !== undefined ? Math.floor(e.first / (e.rows || prev.rows)) : prev.page;
    //         const newSortField = e?.sortField !== undefined ? e.sortField : prev.sortField;
    //         const newSortOrder = e?.sortOrder !== undefined ? e.sortOrder : prev.sortOrder;
    //         const prevNameFilter = prev.filters?.name?.value;
    //         const filtersChanged = prevNameFilter !== searchQuery;

    //         if (
    //             newFirst !== prev.first ||
    //             newRows !== prev.rows ||
    //             newPage !== prev.page ||
    //             newSortField !== prev.sortField ||
    //             newSortOrder !== prev.sortOrder ||
    //             filtersChanged
    //         ) {
    //             return {
    //                 ...prev,
    //                 filters: {
    //                     ...prev.filters,
    //                     name: nameFilter
    //                 },
    //                 first: newFirst,
    //                 rows: newRows,
    //                 page: newPage,
    //                 sortField: newSortField,
    //                 sortOrder: newSortOrder,
    //             };
    //         }
    //         return prev;
    //     });
    // }, [searchQuery, setLazyParams]);

    // Now we can safely do our conditional returns after all hooks
    if (subscriptionLoading) {
        return <p className="text-center">Loading...</p>;
    }

    if (subscriptionError && subscriptionError.message === "Your subscription has expired. Please renew your subscription to continue.") {
        return (
            <Container>
                <div className="relative isolate bg-white px-6 py-12 sm:py-16 lg:px-8">
                    <div className="absolute inset-x-0 -top-3 -z-10 transform-gpu overflow-hidden px-36 blur-3xl" aria-hidden="true">
                        <motion.div
                            className="mx-auto aspect-[1155/678] w-[72.1875rem] opacity-80"
                            style={{
                                clipPath: 'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)'
                            }}
                            animate={{
                                background: [
                                    'linear-gradient(45deg, #ff2299 0%, #9922ff 100%)',
                                    'linear-gradient(45deg, #9922ff 0%, #22aaff 100%)',
                                    'linear-gradient(45deg, #22aaff 0%, #ff2299 100%)'
                                ],
                                rotate: -360,
                            }}
                            transition={{
                                duration: 20,
                                repeat: Infinity,
                                ease: "linear",
                            }}
                        />
                    </div>
                    <div className="mx-auto max-w-4xl text-center">
                        <h2 className="text-base font-semibold leading-7 text-indigo-600">Subscription Status</h2>
                        <h1 className="mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
                            Subscription Expired
                        </h1>
                        <p className="mx-auto mt-6 max-w-2xl text-lg leading-8 text-gray-600">
                            {subscriptionError.message}
                        </p>
                        <p className="mt-4 text-center text-gray-600">
                            Please renew your subscription to continue using our services.
                        </p>
                    </div>
                    <div className="mt-16 mx-auto max-w-2xl">
                        <div className="relative rounded-3xl p-8 ring-1 ring-gray-900/10 shadow-xl bg-gradient-to-br from-red-50 to-white"
                            style={{ borderTop: '6px solid #ef4444' }}>
                            <h3 className="text-2xl font-bold text-gray-900 mb-2">Expired Plan</h3>
                            <div className="flex items-center">
                                <svg className="h-5 w-5 flex-none text-red-600" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clipRule="evenodd" />
                                </svg>
                                <span className="ml-3 text-red-600 font-medium">Your subscription has expired</span>
                            </div>
                            <div className="mt-6 p-3 rounded-lg bg-red-100 text-red-800 flex items-center justify-center">
                                <span className="font-medium">⚠️ Please renew your subscription to continue</span>
                            </div>
                            <div className="mt-10 flex items-center justify-center gap-x-6">
                                <button
                                    onClick={() => window.location.href = `${import.meta.env.VITE_ENV}/manager/Packages`}
                                    className="main-btn text-md shadow-md px-5 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-200"
                                >
                                    Renew Now
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </Container>
        );
    }

    if (noPackage) {
        return (
            <Container>
                <div className="relative isolate bg-white px-6 py-12 sm:py-16 lg:px-8">
                    <div className="absolute inset-x-0 -top-3 -z-10 transform-gpu overflow-hidden px-36 blur-3xl" aria-hidden="true">
                        <motion.div
                            className="mx-auto aspect-[1155/678] w-[72.1875rem] opacity-80"
                            style={{
                                clipPath: 'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)'
                            }}
                            animate={{
                                background: [
                                    'linear-gradient(45deg, #ff2299 0%, #9922ff 100%)',
                                    'linear-gradient(45deg, #9922ff 0%, #22aaff 100%)',
                                    'linear-gradient(45deg, #22aaff 0%, #ff2299 100%)'
                                ],
                                rotate: -360,
                            }}
                            transition={{
                                duration: 20,
                                repeat: Infinity,
                                ease: "linear",
                            }}
                        />
                    </div>
                    <div className="mx-auto max-w-4xl text-center">
                        <h2 className="text-base font-semibold leading-7 text-indigo-600">No Package Found</h2>
                        <h1 className="mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
                            Unlock All Features With a Package
                        </h1>
                        <p className="mx-auto mt-6 max-w-2xl text-lg leading-8 text-gray-600">
                            We couldn&apos;t find any active package for your account. To access all features and design templates, please purchase a package.
                        </p>
                        <p className="mt-4 text-center text-gray-600">
                            Click the button below to explore available packages and unlock the full potential of your dashboard.
                        </p>
                    </div>
                    <div className="mt-16 mx-auto max-w-2xl">
                        <div className="relative rounded-3xl p-8 ring-1 ring-gray-900/10 shadow-xl bg-gradient-to-br from-blue-50 to-white"
                            style={{ borderTop: '6px solid #3b82f6' }}>
                            <h3 className="text-2xl font-bold text-gray-900 mb-2">No Active Package</h3>
                            <div className="flex items-center">
                                <svg className="h-5 w-5 flex-none text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clipRule="evenodd" />
                                </svg>
                                <span className="ml-3 text-blue-600 font-medium">No package is currently assigned to your account</span>
                            </div>
                            <div className="mt-6 p-3 rounded-lg bg-blue-100 text-blue-800 flex items-center justify-center">
                                <span className="font-medium">💡 Purchase a package to unlock all features</span>
                            </div>
                            <div className="mt-10 flex items-center justify-center gap-x-6">
                                <button
                                    onClick={() => window.location.href = `${import.meta.env.VITE_ENV}/manager/Packages`}
                                    className="main-btn text-md shadow-md px-5 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-200"
                                >
                                    View Packages
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </Container>
        );
    }

    const createMember = () => {
        setActionType("create")
        setSelectedMember({});
        dialogHandler("addMember");
    }

    const createGroup = () => {
        if (!isEmpty(selectedMembers.data)) {
            console.log("Selected members for group creation:", selectedMembers.data);
            setIsCreateGroupModalOpen(true);
        }
    }

    const editMember = (data) => {
        setActionType("update");
        const updatedData = { ...data };
        delete updatedData.role;
        delete updatedData.group_permission;
        delete updatedData.design;
        setSelectedMember(updatedData);
        dialogHandler("addMember");
    }


    const handleExport = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axiosInstance.get('users/export', {
                responseType: 'blob',
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            const blob = new Blob([response.data], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            });
            saveAs(blob, `users_export_${new Date().toISOString().split('T')[0]}.xlsx`);

            toast.current.show({
                severity: 'success',
                summary: 'Success',
                detail: 'Data exported successfully',
                life: 3000
            });
        } catch (error) {
            console.error('Error exporting data:', error);
            toast.current.show({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to export data',
                life: 3000
            });
        }
    };

    // Helper to fetch full card info from package API
    const fetchCardDetailsFromPackage = async (cardId) => {
        try {
            const token = localStorage.getItem('token');
            const userId = localStorage.getItem('user_id');
            const response = await axiosInstance.get(`packages/show-package-by-id/${userId}`, {
                headers: { Authorization: `Bearer ${token}` }
            });
            if (response.data && response.data.cards) {
                // Find the card by id
                const card = response.data.cards.find(c => c.id === cardId);
                return card || null;
            }
            return null;
        } catch (error) {
            console.error('Error fetching card details from package API:', error);
            return null;
        } finally {
            // nothing
        }
    };

    // عند عرض البطاقة، جلب بياناتها الكاملة من API الباكيج
    const viewMemberCard = async (rowData) => {
        if (rowData.cards && rowData.cards.length > 0) {
            setAllCards(rowData.cards);
            setCurrentCardIndex(0);
            setIsCardViewOpen(true);
            // جلب بيانات البطاقة الكاملة لأول بطاقة
            const fullCard = await fetchCardDetailsFromPackage(rowData.cards[0].id);
            setSelectedCard(fullCard || rowData.cards[0]);
        }
    };

    // عند التنقل بين البطاقات، جلب بياناتها الكاملة من API الباكيج
    const goToNextCard = async () => {
        if (currentCardIndex < allCards.length - 1) {
            const nextIndex = currentCardIndex + 1;
            setCurrentCardIndex(nextIndex);
            setIsFlipped(false);
            const fullCard = await fetchCardDetailsFromPackage(allCards[nextIndex].id);
            setSelectedCard(fullCard || allCards[nextIndex]);
        }
    };
    const goToPreviousCard = async () => {
        if (currentCardIndex > 0) {
            const prevIndex = currentCardIndex - 1;
            setCurrentCardIndex(prevIndex);
            setIsFlipped(false);
            const fullCard = await fetchCardDetailsFromPackage(allCards[prevIndex].id);
            setSelectedCard(fullCard || allCards[prevIndex]);
        }
    };

    const handleImportSuccess = () => {
        setLazyParams(prev => ({ ...prev, ...usersTableConfig }));
        toast.current.show({
            severity: 'success',
            summary: 'Success',
            detail: 'Users imported successfully',
            life: 3000
        });
    };

    const handleGroupSuccess = () => {
        setLazyParams(prev => ({ ...prev, ...usersTableConfig }));
        setIsCreateGroupModalOpen(false);
    };


    const actionBodyTemplate = (rowData) => {
        const currentUserId = localStorage.getItem('user_id');



        return (
            <>
                <div className="d-inline-block text-nowrap">
                    {/* Eye Icon - View Card */}
                    {rowData.cards && rowData.cards.length > 0 && (
                        <>
                            <Tooltip target={`.eye-button-${rowData.id}`} showDelay={100} className="fs-8" />
                            <button
                                className={`btn btn-sm btn-icon eye-button-${rowData.id} me-5 text-blue-500 hover:text-blue-700`}
                                data-pr-position="bottom"
                                data-pr-tooltip="View Card"
                                onClick={() => viewMemberCard(rowData)}>
                                <FaRegEye size={20} />
                            </button>
                        </>
                    )}

                    {/* Edit */}
                    <Tooltip target={`.update-button-${rowData.id}`} showDelay={100} className="fs-8" />
                    <button
                        className={`btn btn-sm btn-icon update-button-${rowData.id} me-3`}
                        data-pr-position="bottom"
                        data-pr-tooltip="Update"
                        onClick={() => editMember(rowData)}>
                        <FiEdit />
                    </button>


                    {/* Delete */}
                    {rowData.id.toString() !== currentUserId && (
                        <>
                            <Tooltip target={`.delete-button-${rowData.id}`} showDelay={100} className="fs-8" />
                            <button
                                className={`btn btn-sm btn-icon delete-button-${rowData.id}`}
                                data-pr-position="bottom"
                                data-pr-tooltip="Delete"
                                onClick={() => handleDeleteClick(rowData)}>
                                <TfiTrash />
                            </button>
                        </>
                    )}
                </div>
            </>
        );
    }

    const selectAllHandler = (rowsData) => {
        setSelectedMembers((prev) => ({
            ...prev,
            data: rowsData,
            groupData: prev.groupData || {},
            action: !isEmpty(rowsData) ? "update" : "create"
        }));
    }

    const statusBodyTemplate = (rowData) => {
        const status = rowData?.print_status || "unprinted";
        return (
            <span className={`text-[white] rounded-[6px] font-bold text-sm py-2 px-3 capitalize ${statusStyles?.[status]}`}>
                {status}
            </span>
        )
    }

    const render3DCard = () => {
        if (!selectedCard) return null;

        return (
            <>
                {/* Card Navigation Header */}
                {!isMobile && allCards.length > 1 && (
                    <div className="flex justify-between items-center mb-4 w-full max-w-md mx-auto">
                        <motion.button
                            onClick={goToPreviousCard}
                            disabled={currentCardIndex === 0}
                            style={{
                                padding: '8px 16px',
                                borderRadius: '8px',
                                fontWeight: '500',
                                transition: 'all 0.2s ease',
                                border: 'none',
                                cursor: currentCardIndex === 0 ? 'not-allowed' : 'pointer',
                                backgroundColor: currentCardIndex === 0 ? '#d1d5db' : '#2563eb',
                                color: currentCardIndex === 0 ? '#6b7280' : '#ffffff',
                                boxShadow: currentCardIndex === 0 ? 'none' : '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                                opacity: currentCardIndex === 0 ? 0.6 : 1,
                            }}
                            whileHover={currentCardIndex > 0 ? { scale: 1.05, backgroundColor: '#1d4ed8' } : {}}
                            whileTap={currentCardIndex > 0 ? { scale: 0.95 } : {}}
                        >
                            ← Previous
                        </motion.button>

                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <span style={{
                                fontSize: '14px',
                                color: '#4b5563',
                                fontWeight: '500',
                                margin: '0 8px'
                            }}>
                                {currentCardIndex + 1} of {allCards.length}
                            </span>
                            {/* Card indicators */}
                            <div style={{ display: 'flex', gap: '4px' }}>
                                {allCards.map((_, index) => (
                                    <motion.div
                                        key={index}
                                        style={{
                                            width: '8px',
                                            height: '8px',
                                            borderRadius: '50%',
                                            backgroundColor: index === currentCardIndex ? '#2563eb' : '#d1d5db',
                                            cursor: 'pointer',
                                            transition: 'all 0.2s ease',
                                        }}
                                        whileHover={{ scale: 1.2 }}
                                        onClick={() => {
                                            if (index !== currentCardIndex) {
                                                setCurrentCardIndex(index);
                                                setIsFlipped(false);
                                                fetchCardDetailsFromPackage(allCards[index].id).then(fullCard => {
                                                    setSelectedCard(fullCard || allCards[index]);
                                                });
                                            }
                                        }}
                                    />
                                ))}
                            </div>
                        </div>

                        <motion.button
                            onClick={goToNextCard}
                            disabled={currentCardIndex === allCards.length - 1}
                            style={{
                                padding: '8px 16px',
                                borderRadius: '8px',
                                fontWeight: '500',
                                transition: 'all 0.2s ease',
                                border: 'none',
                                cursor: currentCardIndex === allCards.length - 1 ? 'not-allowed' : 'pointer',
                                backgroundColor: currentCardIndex === allCards.length - 1 ? '#d1d5db' : '#2563eb',
                                color: currentCardIndex === allCards.length - 1 ? '#6b7280' : '#ffffff',
                                boxShadow: currentCardIndex === allCards.length - 1 ? 'none' : '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                                opacity: currentCardIndex === allCards.length - 1 ? 0.6 : 1,
                            }}
                            whileHover={currentCardIndex < allCards.length - 1 ? { scale: 1.05, backgroundColor: '#1d4ed8' } : {}}
                            whileTap={currentCardIndex < allCards.length - 1 ? { scale: 0.95 } : {}}
                        >
                            Next →
                        </motion.button>
                    </div>
                )}

                <div className="relative flex flex-col items-center w-full"
                    style={{
                      maxWidth: isMobile ? '100%' : '400px',
                      marginLeft: 'auto',
                      marginRight: 'auto',
                      padding: isMobile ? '8px' : '16px',
                      boxSizing: 'border-box',
                      background: 'none',
                      position: 'relative',
                      zIndex: 1,
                    }}
                >
                    {/* Card */}
                    <motion.div
                        className="relative w-full flex justify-center"
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.5 }}
                        onClick={() => setIsFlipped(!isFlipped)}
                        style={{
                          width: '100%',
                          maxWidth: isMobile ? '90vw' : '350px',
                          height: 'auto',
                          boxSizing: 'border-box',
                          background: 'none',
                          position: 'relative',
                          zIndex: 3,
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}
                    >
                        <motion.div
                            className="relative cursor-pointer"
                            style={{
                                transformStyle: 'preserve-3d',
                                perspective: '1000px',
                                width: isMobile ? '100%' : 'fit-content',
                                maxWidth: '100%',
                                display: 'flex',
                                justifyContent: 'center',
                            }}
                            animate={{
                                rotateY: isFlipped ? 180 : 0,
                            }}
                            transition={{
                                duration: 0.6,
                                ease: "easeInOut",
                            }}
                            whileHover={{ scale: isMobile ? 1.01 : 1.02 }}
                        >
                            {/* Card Front */}
                            <motion.div
                                className="flex flex-col items-center justify-center rounded-xl shadow-2xl overflow-hidden"
                                style={{
                                    backfaceVisibility: 'hidden',
                                    background: 'linear-gradient(145deg, #1a2a3a, #2a3a4a)',
                                    border: '2px solid rgba(255, 255, 255, 0.1)',
                                    width: isMobile ? '100%' : 'fit-content',
                                    minWidth: isMobile ? '280px' : '300px',
                                    minHeight: isMobile ? '350px' : '400px',
                                    maxWidth: isMobile ? '100%' : '350px',
                                    margin: '0 auto',
                                }}
                            >
                                <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-white opacity-10 rounded-xl"></div>

                                <div className="absolute inset-0 rounded-xl overflow-hidden">
                                    <div className="absolute -top-10 -left-10 w-32 h-32 bg-blue-500 rounded-full filter blur-3xl opacity-20"></div>
                                    <div className="absolute -bottom-10 -right-10 w-32 h-32 bg-purple-500 rounded-full filter blur-3xl opacity-20"></div>
                                </div>

                                <div className={`relative ${isMobile ? 'p-3' : 'p-4'} flex flex-col items-center justify-center w-full h-full`}>
                                    <div className="relative overflow-hidden flex items-center justify-center w-full h-full">
                                        {selectedCard.image_path ? (
                                            <motion.img
                                                src={selectedCard.image_path}
                                                alt="Card"
                                                className="object-contain w-full h-full"
                                                style={{
                                                    maxWidth: '100%',
                                                    maxHeight: isMobile ? '300px' : '350px',
                                                    width: 'auto',
                                                    height: 'auto',
                                                }}
                                                initial={{ opacity: 0 }}
                                                animate={{ opacity: 1 }}
                                                transition={{ delay: 0.3 }}
                                                whileHover={{ scale: isMobile ? 1.02 : 1.05 }}
                                            />
                                        ) : (
                                            <div className={`${isMobile ? 'w-full h-48' : 'w-64 h-64'} bg-gray-700 flex items-center justify-center rounded-lg`}>
                                                <span className="text-gray-300">No Image</span>
                                            </div>
                                        )}
                                        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-white opacity-10"></div>

                                        <div className="absolute inset-0 overflow-hidden">
                                            {[...Array(10)].map((_, i) => (
                                                <motion.div
                                                    key={i}
                                                    className="absolute bg-white rounded-full"
                                                    style={{
                                                        width: `${Math.random() * 3 + 1}px`,
                                                        height: `${Math.random() * 3 + 1}px`,
                                                        left: `${Math.random() * 100}%`,
                                                        top: `${Math.random() * 100}%`,
                                                        opacity: Math.random() * 0.3,
                                                    }}
                                                    animate={{
                                                        y: [0, (Math.random() - 0.5) * 20],
                                                        x: [0, (Math.random() - 0.5) * 20],
                                                    }}
                                                    transition={{
                                                        duration: Math.random() * 5 + 3,
                                                        repeat: Infinity,
                                                        repeatType: "reverse",
                                                    }}
                                                />
                                            ))}
                                        </div>
                                    </div>

                                    <motion.div
                                        className="absolute inset-0 bg-white opacity-0 pointer-events-none"
                                        initial={{ opacity: 0 }}
                                        animate={{
                                            opacity: isFlipped ? 0 : [0, 0.1, 0],
                                            x: [-100, 300],
                                        }}
                                        transition={{
                                            duration: 1.5,
                                            repeat: Infinity,
                                            repeatDelay: 3,
                                        }}
                                        style={{
                                            background: 'linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 100%)',
                                        }}
                                    />
                                </div>
                            </motion.div>

                            {/* Card Back */}
                            <motion.div
                                className="absolute top-0 left-0 w-full h-full flex flex-col items-center justify-center rounded-xl shadow-2xl overflow-hidden"
                                style={{
                                    backfaceVisibility: 'hidden',
                                    background: 'linear-gradient(145deg, #2a3a4a, #1a2a3a)',
                                    border: '2px solid rgba(255, 255, 255, 0.1)',
                                    transform: 'rotateY(180deg)',
                                    width: '100%',
                                    height: '100%',
                                    minWidth: isMobile ? '280px' : '300px',
                                    minHeight: isMobile ? '350px' : '400px',
                                    maxWidth: isMobile ? '100%' : '350px',
                                    margin: '0 auto',
                                }}
                            >
    {/* Glass Overlay */}
    <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-white opacity-10 rounded-xl"></div>
    
    {/* Glowing Effect */}
    <div className="absolute inset-0 rounded-xl overflow-hidden">
        <div className="absolute -top-10 -left-10 w-32 h-32 bg-purple-500 rounded-full filter blur-3xl opacity-20"></div>
        <div className="absolute -bottom-10 -right-10 w-32 h-32 bg-blue-500 rounded-full filter blur-3xl opacity-20"></div>
    </div>
    
    {/* Back Content - تم تعديل padding والعناصر الداخلية */}
    <div className="relative w-full h-full p-4 flex flex-col justify-between overflow-auto">
        {/* Magnetic Strip */}
        <motion.div
            className="w-full h-8 bg-gradient-to-r from-black to-gray-800 rounded-sm mb-3"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
        />
        
        {/* Barcode Placeholder */}
        <motion.div
            className="w-full h-16 bg-white bg-opacity-10 rounded-sm flex items-center justify-center mb-1"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3 }}
        >
            <div className="w-full h-12 bg-gray-800 rounded-sm flex items-center justify-center">
                <span className="text-xs text-gray-400">card information</span>
            </div>
        </motion.div>
        
        {/* Information Section - تم تعديل padding وحجم الخط */}
        <motion.div
            className="w-full space-y-2 px-3"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
        >
            <div className="flex justify-between items-center">
                <span className="text-xs text-blue-200">Member Name:</span>
                <span className="text-xs text-white truncate max-w-[60%]">{selectedCard.member_name || selectedCard.name || 'N/A'}</span>
            </div>
            <div className="flex justify-between items-center">
                <span className="text-xs text-blue-200">Member Type:</span>
                <span className="text-xs text-white truncate max-w-[60%]">{selectedCard.member_type || selectedCard.card_type?.name || 'N/A'}</span>
            </div>
            <div className="flex justify-between items-center">
                <span className="text-xs text-blue-200">Serial No:</span>
                <span className="text-xs text-white">{selectedCard.number || 'N/A'}</span>
            </div>
            <div className="flex justify-between items-center">
                <span className="text-xs text-blue-200">Number of colors:</span>
                <span className="text-xs text-white">{selectedCard.card_type?.number_of_colors ?? selectedCard.number_of_colors ?? 0}</span>
            </div>
            <div className="flex justify-between items-center">
                <span className="text-xs text-blue-200">Design Template:</span>
                <span className="text-xs text-white truncate max-w-[60%]">{selectedCard.template?.name || selectedCard.design_name || selectedCard.design_template || 'No Design'}</span>
            </div>
            <div className="flex justify-between items-center">
                <span className="text-xs text-blue-200">Group:</span>
                <span className="text-xs text-white truncate max-w-[60%]">{selectedCard.group?.title || selectedCard.group_name || 'No Group'}</span>
            </div>
            <div className="flex justify-between items-center">
                <span className="text-xs text-blue-200">Phone:</span>
                <span className="text-xs text-white">{selectedCard.member_phone || 'N/A'}</span>
            </div>
            <div className="flex justify-between items-center">
                <span className="text-xs text-blue-200">Position:</span>
                <span className="text-xs text-white truncate max-w-[60%]">{selectedCard.member_position || 'N/A'}</span>
            </div>
            <div className="flex justify-between items-center">
                <span className="text-xs text-blue-200">Department:</span>
                <span className="text-xs text-white truncate max-w-[60%]">{selectedCard.member_department || 'N/A'}</span>
            </div>
            {/* Print Date (English) */}
            {selectedCard.print_status === 'printed' && selectedCard.printed_at && (
                <div className="flex justify-between items-center mt-1">
                    <span className="text-xs text-blue-200">Printed At:</span>
                    <span className="text-xs text-white">
                        {new Date(selectedCard.printed_at).toLocaleString('en-GB', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                        })}
                    </span>
                </div>
            )}
        </motion.div>
        
        {/* Signature Area */}
        <motion.div
            className="mt-3 pt-2 border-t border-gray-700 px-3"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
        >
            <div className="flex justify-between items-center">
                <div>
                    <p className="text-xs text-blue-200 mb-1">Type Of Connection</p>
                    <div className="h-8 w-24 bg-gray-700 rounded-sm"></div>
                </div>
                <div className="text-right">
                    <p className="text-xs text-blue-200">Connection</p>
                    <p className="text-xs text-white flex items-center justify-end">
                        {selectedCard?.card_type?.type_of_connection === 'NFC' && (
                            <svg className="w-4 h-4 text-blue-300 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 4C7.58 4 4 7.58 4 12C4 16.42 7.58 20 12 20C16.42 20 20 16.42 20 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                                <path d="M12 8C9.79 8 8 9.79 8 12C8 14.21 9.79 16 12 16C14.21 16 16 14.21 16 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                                <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                                <path d="M20 4L20 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                                <path d="M20 4L16 4" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                            </svg>
                        )}
                        {selectedCard?.card_type?.type_of_connection === 'Bluetooth' && (
                            <svg className="w-4 h-4 text-blue-300 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M6 8L18 16L12 22V2L18 8L6 16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                        )}
                        {selectedCard?.card_type?.type_of_connection || 'N/A'}
                    </p>
                </div>
            </div>
        </motion.div>
        
        {/* Footer - تم تعديل حجم الخط */}
        <motion.div
            className="w-full text-center mt-2 px-3 pb-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
        >
            <p className="text-[7px] text-blue-300 opacity-70">
                This badge is property of the card owner. If found, please return to nearest office.
            </p>
        </motion.div>
    </div>
</motion.div>
                        </motion.div>
                    </motion.div>
                </div>

                {/* Control buttons */}
                <div className={`${isMobile ? 'mt-6' : 'mt-8'} text-center space-y-4 w-full`}>
                    {/* Flip button */}
                    <div className="flex flex-col items-center space-y-3">
                        <motion.p
                            style={{
                                fontSize: '14px',
                                color: '#6b7280',
                                margin: '0 0 12px 0',
                                textAlign: 'center'
                            }}
                            animate={{ opacity: [0.6, 1, 0.6] }}
                            transition={{ duration: 2, repeat: Infinity }}
                        >
                            Click card or button to flip
                        </motion.p>
                        <motion.button
                            onClick={() => setIsFlipped(!isFlipped)}
                            style={{
                                padding: '12px 24px',
                                borderRadius: '8px',
                                fontWeight: '500',
                                border: 'none',
                                cursor: 'pointer',
                                background: 'linear-gradient(to right, #2563eb, #7c3aed)',
                                color: '#ffffff',
                                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                                transition: 'all 0.2s ease',
                            }}
                            whileHover={{
                                scale: 1.05,
                                boxShadow: "0px 0px 15px rgba(99, 102, 241, 0.5)",
                                opacity: 0.9
                            }}
                            whileTap={{ scale: 0.95 }}
                        >
                            {isFlipped ? 'Show Front' : 'Show Back'}
                        </motion.button>
                    </div>

                    {/* Mobile carousel controls */}
                    {isMobile && allCards.length > 1 && (
                        <div style={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            gap: '16px',
                            marginTop: '16px',
                            paddingTop: '16px',
                            borderTop: '1px solid #e5e7eb'
                        }}>
                            <motion.button
                                onClick={goToPreviousCard}
                                disabled={currentCardIndex === 0}
                                style={{
                                    padding: '8px 16px',
                                    borderRadius: '8px',
                                    fontWeight: '500',
                                    border: 'none',
                                    cursor: currentCardIndex === 0 ? 'not-allowed' : 'pointer',
                                    backgroundColor: currentCardIndex === 0 ? '#d1d5db' : '#2563eb',
                                    color: currentCardIndex === 0 ? '#6b7280' : '#ffffff',
                                    boxShadow: currentCardIndex === 0 ? 'none' : '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                                    opacity: currentCardIndex === 0 ? 0.6 : 1,
                                    transition: 'all 0.2s ease',
                                }}
                                whileHover={currentCardIndex > 0 ? { scale: 1.05, backgroundColor: '#1d4ed8' } : {}}
                                whileTap={currentCardIndex > 0 ? { scale: 0.95 } : {}}
                            >
                                ← Prev
                            </motion.button>

                            <span style={{
                                fontSize: '14px',
                                color: '#4b5563',
                                fontWeight: '500',
                                padding: '0 12px'
                            }}>
                                {currentCardIndex + 1} / {allCards.length}
                            </span>

                            <motion.button
                                onClick={goToNextCard}
                                disabled={currentCardIndex === allCards.length - 1}
                                style={{
                                    padding: '8px 16px',
                                    borderRadius: '8px',
                                    fontWeight: '500',
                                    border: 'none',
                                    cursor: currentCardIndex === allCards.length - 1 ? 'not-allowed' : 'pointer',
                                    backgroundColor: currentCardIndex === allCards.length - 1 ? '#d1d5db' : '#2563eb',
                                    color: currentCardIndex === allCards.length - 1 ? '#6b7280' : '#ffffff',
                                    boxShadow: currentCardIndex === allCards.length - 1 ? 'none' : '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                                    opacity: currentCardIndex === allCards.length - 1 ? 0.6 : 1,
                                    transition: 'all 0.2s ease',
                                }}
                                whileHover={currentCardIndex < allCards.length - 1 ? { scale: 1.05, backgroundColor: '#1d4ed8' } : {}}
                                whileTap={currentCardIndex < allCards.length - 1 ? { scale: 0.95 } : {}}
                            >
                                Next →
                            </motion.button>
                        </div>
                    )}
                </div>
            </>
        );
    };




    // Mobile action menu component
    const MobileActionMenu = ({ member, isOpen, onClose }) => {
    const currentUserId = localStorage.getItem('user_id');
    
    if (!isOpen) return null;
    
    return createPortal(
        <div 
            className="fixed inset-0 bg-black bg-opacity-60 z-[9999] flex items-center justify-center"  //I dont care anymore just leave it at 9999
            style={{
                position: 'fixed',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                backdropFilter: 'blur(2px)'
            }}
            onClick={onClose}
        >
            <div 
                className="bg-white rounded-lg p-4 m-4 w-full max-w-sm relative shadow-2xl"
                style={{
                    zIndex: 10000,
                    backgroundColor: '#ffffff',
                    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.5)',
                    border: '1px solid rgba(0, 0, 0, 0.1)'
                }}
                onClick={(e) => e.stopPropagation()}
            >
                <div className="flex items-center mb-4 border-b pb-3">
                    <img
                        src={member.image || profile_img}
                        alt="Profile"
                        className="w-10 h-10 rounded-full object-cover mr-3"
                    />
                    <div>
                        <h3 className="font-semibold">{member.name}</h3>
                        <p className="text-sm text-gray-500">{member.position}</p>
                    </div>
                </div>

                <div className="space-y-2 bg-gray-50 p-2 rounded-lg">
                    {/* View Card */}
                    {member.cards && member.cards.length > 0 && (
                        <button
                            className="w-full flex items-center p-3 text-left bg-white hover:bg-blue-50 rounded-lg border border-gray-200"
                            onClick={() => {
                                viewMemberCard(member);
                                onClose();
                            }}
                        >
                            <FaRegEye className="mr-3 text-blue-500" size={18} />
                            <span>View Card</span>
                        </button>
                    )}

                    {/* Edit */}
                    <button
                        className="w-full flex items-center p-3 text-left bg-white hover:bg-green-50 rounded-lg border border-gray-200"
                        onClick={() => {
                            editMember(member);
                            onClose();
                        }}
                    >
                        <FiEdit className="mr-3 text-green-500" size={18} />
                        <span>Edit Member</span>
                    </button>

                    {/* Delete */}
                    {member.id.toString() !== currentUserId && (
                        <button
                            className="w-full flex items-center p-3 text-left bg-white hover:bg-red-50 rounded-lg border border-gray-200 text-red-600"
                            onClick={() => {
                                handleDeleteClick(member);
                                onClose();
                            }}
                        >
                            <TfiTrash className="mr-3" size={18} />
                            <span>Delete Member</span>
                        </button>
                    )}
                </div>

                <button
                    className="w-full mt-4 p-2 bg-gray-200 hover:bg-gray-300 rounded-lg text-center font-medium transition-colors"
                    onClick={onClose}
                >
                    Cancel
                </button>
            </div>
        </div>,
        document.body
    );
};

    // Mobile list view component
    const MobileListView = () => {
        if (loading) {
            return (
                <div className="space-y-2">
                    {[...Array(5)].map((_, index) => (
                        <div key={index} className="bg-white border rounded-lg p-4 shadow-sm animate-pulse">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center flex-1">
                                    <div className="w-12 h-12 bg-gray-300 rounded-full mr-3"></div>
                                    <div className="flex-1">
                                        <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                                        <div className="h-3 bg-gray-300 rounded w-1/2 mb-1"></div>
                                        <div className="h-3 bg-gray-300 rounded w-1/3"></div>
                                    </div>
                                </div>
                                <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                            </div>
                        </div>
                    ))}
                </div>
            );
        }

        if (!data || data.length === 0) {
            return (
                <div className="text-center py-8">
                    <p className="text-gray-500">No members found</p>
                </div>
            );
        }

        return (
            <div className="space-y-2">
                {data.map((member) => (
                    <div key={member.id} className="bg-white border rounded-lg p-4 shadow-sm">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center flex-1">
                                <img
                                    src={member.image || profile_img}
                                    alt="Profile"
                                    className="w-12 h-12 rounded-full object-cover mr-3"
                                />
                                <div className="flex-1">
                                    <h3 className="font-semibold text-gray-900">{member.name}</h3>
                                    <p className="text-sm text-gray-500">{member.position}</p>
                                    <p className="text-xs text-gray-400">{member.department}</p>
                                    {member.cards && member.cards.length > 0 && (
                                        <p className="text-xs text-blue-600 mt-1">
                                            {member.cards.length === 1
                                                ? member.cards[0]?.number
                                                : `${member.cards.length} Cards`
                                            }
                                        </p>
                                    )}
                                </div>
                            </div>
                            <button
                                className="p-2 hover:bg-gray-100 rounded-full"
                                onClick={() => setMobileActionMenuOpen(member.id)}
                            >
                                <HiDotsVertical className="text-gray-500" size={20} />
                            </button>
                        </div>
                    </div>
                ))}

                {/* Mobile Action Menu */}
                {mobileActionMenuOpen && (
                    <MobileActionMenu
                        member={data.find(m => m.id === mobileActionMenuOpen)}
                        isOpen={!!mobileActionMenuOpen}
                        onClose={() => setMobileActionMenuOpen(null)}
                    />
                )}
            </div>
        );
    };




    return (
        <div className="w-full min-w-full h-full flex flex-col">

        <Toast ref={toast} position="top-right" />

        <ConfirmDialog                                       //This is a small confirm modal for the delete button
            group="headless"
            content={(options) => (
                <div className="flex flex-col items-center p-5">
                    <i className="pi pi-exclamation-triangle text-6xl text-yellow-500 mb-3"/>
                    <span className="text-xl font-bold mb-2">{options.message}</span>
                    <div className="flex gap-3">
                        <button className="p-button p-component" onClick={options.accept}>
                            Yes
                        </button>
                        <button className="p-button p-component p-button-outlined" onClick={options.reject}>
                            No
                        </button>
                    </div>
                </div>
            )}
        />



            {/*Buttons & Search Bar Container*/}
            <div className={`w-full mb-4 gap-4 mt-1 ${isMobile ? 'space-y-4' : 'flex justify-between items-start'}`}>
                {/* Action buttons */}
                <div className={`${isMobile ? 'flex flex-wrap gap-2' : 'flex items-center'}`}>
                    {/* Desktop Add Member button */}
                    {!isMobile && (
                        <button className="main-btn text-md shadow-md mr-[20px]" onClick={() => createMember()}>
                            Add Member
                        </button>
                    )}

                   {!isMobile && <button
                        className={`${activeBtn ? "gray-btn" : "main-btn"} ${isMobile ? 'text-sm' : 'text-md'} me-2 shadow-md`}
                        disabled={activeBtn}
                        onClick={() => createGroup()}
                    >
                        Create Group
                    </button>                       //This is so mobile users can't create groups since they cant select
                    }


                    {/* Mobile export/import buttons */}
                    {isMobile && (
                        <div className="w-full flex justify-end">
                            <div className="flex space-x-2">
                            <button
                                onClick={() => setImportDialogVisible(true)}
                                className="main-btn text-sm shadow-md flex items-center"
                            >
                                <FaFileImport className="mr-1" size={14} />
                                <span>Import</span>
                            </button>

                            <button
                                onClick={handleExport}
                                className="main-btn text-sm shadow-md flex items-center"
                            >
                                <FaFileExport className="mr-1" size={14} />
                                <span>Export</span>
                            </button>
                            </div>
                        </div>
                        )}
                </div>

                {/* Search input */}
                <div className={`relative ${isMobile ? 'w-full' : 'flex-grow max-w-[700px]'}`}>
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <FaSearch className="h-4 w-4 text-gray-400" aria-hidden="true" />
                    </div>
                    <input
                        type="text"
                        placeholder="Search by name..."
                        className="w-full pl-10 pr-4 py-2 border rounded-md shadow-md
                                focus:outline-none focus:ring-2 focus:ring-blue-300
                                focus:border-blue-300 transition-all duration-200"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                    />
                </div>

                {/* Desktop export/import buttons */}
                {!isMobile && (
                    <div className="flex items-center space-x-2">
                        <button
                            onClick={handleExport}
                            className="main-btn text-md shadow-md mr-[20px] flex items-center"
                        >
                            <FaFileExport className="mr-2" />
                            <span>Export Excel</span>
                        </button>

                        <button
                            onClick={() => setImportDialogVisible(true)}
                            className="main-btn text-md shadow-md mr-[20px] flex items-center"
                        >
                            <FaFileImport className="mr-2" />
                            <span>Import Excel</span>
                        </button>
                    </div>
                )}
            </div>



            <div className="flex-grow h-full">
                {isMobile ? (
                    // Mobile view
                    <div className="h-full overflow-y-auto">
                        <MobileListView />

                        {/* Mobile pagination */}
                        {totalRecords > lazyParams?.rows && (
                            <div className="flex justify-between items-center mt-4 p-4 bg-white border-t">
                                <button
                                    className="px-4 py-2 bg-blue-500 text-white rounded disabled:bg-gray-300"
                                    disabled={lazyParams?.first === 0}
                                    onClick={() => dataHandler({
                                        first: Math.max(0, lazyParams.first - lazyParams.rows),
                                        page: Math.max(0, lazyParams.page - 1)
                                    })}
                                >
                                    Previous
                                </button>
                                <span className="text-sm text-gray-600">
                                    {lazyParams?.first + 1} - {Math.min(lazyParams?.first + lazyParams?.rows, totalRecords)} of {totalRecords}
                                </span>
                                <button
                                    className="px-4 py-2 bg-blue-500 text-white rounded disabled:bg-gray-300"
                                    disabled={lazyParams?.first + lazyParams?.rows >= totalRecords}
                                    onClick={() => dataHandler({
                                        first: lazyParams.first + lazyParams.rows,
                                        page: lazyParams.page + 1
                                    })}
                                >
                                    Next
                                </button>
                            </div>
                        )}
                    </div>
                ) : (
                    // Desktop view
                    <DataTable
                        className="table border w-full h-full"
                        // scrollHeight="flex"
                        selection={selectedMembers.data}
                        onSelectionChange={(e) => selectAllHandler(e.value)}
                        lazy
                        // filterDisplay="row"
                        // header={Header}
                        responsiveLayout="stack"
                        breakpoint="960px"
                        dataKey="id"
                        paginator
                        value={data}
                        first={lazyParams?.first}
                        rows={lazyParams?.rows}
                        rowsPerPageOptions={[5, 25, 50, 100]}
                        totalRecords={totalRecords}
                        onPage={(e) => {
                            // Only update if actually changed
                            if (e.first !== lazyParams.first || e.rows !== lazyParams.rows) {
                                dataHandler({
                                    first: e.first,
                                    rows: e.rows,
                                    page: Math.floor(e.first / e.rows)
                                });
                            }
                        }}
                        onSort={(e) => {
                            // Only update if actually changed
                            if (e.sortField !== lazyParams.sortField || e.sortOrder !== lazyParams.sortOrder) {
                                dataHandler({
                                    sortField: e.sortField,
                                    sortOrder: e.sortOrder
                                });
                            }
                        }}
                        onRowsPerPageChange={(e) => {
                            // Only update if actually changed
                            if (e.rows !== lazyParams.rows) {
                                dataHandler({
                                    rows: e.rows,
                                    first: 0,
                                    page: 0
                                });
                            }
                        }}
                        sortField={lazyParams?.sortField}
                        sortOrder={lazyParams?.sortOrder}
                        onFilter={(e) => {
                            // Only update if filters actually changed
                            dataHandler({
                                filters: e.filters
                            });
                        }}
                        filters={lazyParams?.filters}
                        loading={loading}
                        paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} products"
                        scrollable
                        scrollHeight="100%"
                    >
                        <Column selectionMode="multiple" headerStyle={{ width: '3rem' }} exportable={false} />
                        {/* <Column body={profileBodyTemplate} header="Profile Image" className="text-center" /> */}
                        {/* <Column body={imageBodyTemplate} header="Template Image" className="text-center" /> */}
                        <Column
                            field="name"
                            header="Name"
                            className="text-left"
                            sortable
                            body={(rowData) => (
                                <div className="flex items-center gap-2">
                                    <img
                                        src={rowData.image || profile_img} // Adjust this key to match your data
                                        // alt="Profile"
                                        className="w-8 h-8 rounded-full object-cover"
                                    />
                                    <span>{rowData.name}</span>
                                </div>
                            )}
                        />
                        <Column field="type" header="Type" className="text-center" filter sortable showFilterMenu={false} />
                        <Column field="position" header="Position" className="text-center" filter sortable showFilterMenu={false} />
                        <Column
                            field="cards"
                            header="Associated Card(s)"
                            body={(rowData) => {
                                if (!rowData.cards || rowData.cards.length === 0) {
                                    return 'No Card';
                                } else if (rowData.cards.length === 1) {
                                    return rowData.cards[0]?.number;
                                } else {
                                    return (
                                        <span>
                                            <span className="text-blue-600 font-semibold">{rowData.cards.length}</span> Cards
                                        </span>
                                    );
                                }
                            }}
                        />
                        <Column field="department" header="Department" className="text-center" filter sortable showFilterMenu={false} />
                        <Column body={actionBodyTemplate} header="Action" className="text-center w-96" exportable={false} style={{ minWidth: '8rem' }} />

                        {userType === 'admin' && (
                            <Column field="status" body={statusBodyTemplate} header="Package Status" className="text-center" showFilterMenu={false} filter sortable />
                        )}
                    </DataTable>
                )}
            </div>

            {/* Dialogs */}

            <ImportDialog
                visible={importDialogVisible}
                onHide={() => setImportDialogVisible(false)}
                onImportSuccess={handleImportSuccess}
            />
            {openDialog?.addMember && (
  <AddMemberDialog
    data={selectedMember}
    actionType={actionType}
    onSuccess={() => {
      setLazyParams(prev => ({ ...prev, ...usersTableConfig }));
      toast.current.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Member updated successfully',
        life: 3000
      });
    }}
  />
)}
            {openDialog?.updateGroup && <AssignGroupDialog />}

            {/* Create Group Modal */}
            {isCreateGroupModalOpen && (
                <GroupForm
                    isModalOpen={isCreateGroupModalOpen}
                    setIsModalOpen={setIsCreateGroupModalOpen}
                    onSuccess={handleGroupSuccess}
                    toast={toast}
                    preselectedMembers={selectedMembers.data}
                />
            )}

            {/* Card View Dialog */}
            <Dialog
                header={`Card Preview${selectedCard ? ` - ${selectedCard.template?.name || selectedCard.design_name || selectedCard.design_template || 'No Design'} | ${selectedCard.group?.title || selectedCard.group_name || 'No Group'}` : ''}`}
                visible={isCardViewOpen}
                style={{
                    width: isMobile ? '95vw' : '60vw',
                    maxWidth: isMobile ? '95vw' : '900px',
                    height: isMobile ? '90vh' : 'auto',
                    boxSizing: 'border-box',
                    zIndex: 9999,
                }}
                breakpoints={{
                    '960px': '95vw',
                    '641px': '95vw'
                }}
                onHide={() => {
                    setIsCardViewOpen(false);
                    setIsFlipped(false);
                    setCurrentCardIndex(0);
                    setAllCards([]);
                }}
                className={`badge-view-dialog ${isMobile ? 'mobile-card-dialog' : ''}`}
                contentStyle={{
                    height: isMobile ? 'calc(90vh - 60px)' : 'auto',
                    overflow: isMobile ? 'auto' : 'visible',
                    padding: isMobile ? '10px' : '20px',
                    boxSizing: 'border-box',
                    zIndex: 9999,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                }}
            >
                {render3DCard()}
            </Dialog>

            {/* Mobile Floating Action Button (FAB) */}
            {isMobile && (
                <div className="mobile-fab-container">
                    <button
                        onClick={() => createMember()}
                        className="mobile-fab"
                        title="Add New Member"
                    >
                        <i className="pi pi-plus text-xl"></i>
                    </button>
                </div>
            )}
        </div>
    );
}

export default MemberDataTable;