import { useDesignSpace } from '@contexts/DesignSpaceContext';
import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';
import { Dialog } from 'primereact/dialog';

// Icons
import { FiRotateCcw, FiRotateCw, FiLock, FiUnlock, FiSave, FiMinus, FiPlus, FiMaximize, FiCopy, FiCheck, FiX } from 'react-icons/fi';
import {
    BiSolidLayerPlus,
    BiSolidLayerMinus,
    BiGroup,
    BiLayer
} from 'react-icons/bi';

const CanvaToolbar = ({ saveDesign<PERSON>and<PERSON>, saveAsHandler, isDirty = true, isCreateMode = false, isSaving = false }) => {
    const {
        selectedIds,
        undo,
        redo,
        toggleLock,
        groupElements,
        ungroupElements,
        zoomLevel,
        zoom,
        bringToFront,
        sendToBack,
        setSelectedIds,
        cardType, // <-- add cardType
        elements,  // <-- add elements
        userSelectedCardType // <-- add userSelectedCardType
    } = useDesignSpace();
    const { groupId } = useDesignSpace();
    const [showGroupModal, setShowGroupModal] = useState(false);

    // إضافة اختصارات لوحة المفاتيح للتكبير والتصغير
    useEffect(() => {
        const handleKeyDown = (e) => {
            // تجاهل إذا كان التركيز على input أو textarea
            const active = document.activeElement;
            if (active && (active.tagName === 'INPUT' || active.tagName === 'TEXTAREA' || active.isContentEditable)) {
                return;
            }

            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case '=':
                    case '+':
                        e.preventDefault();
                        handleZoomIn();
                        break;
                    case '-':
                        e.preventDefault();
                        handleZoomOut();
                        break;
                    case '0':
                        e.preventDefault();
                        handleZoomReset();
                        break;
                }
            }
        };

        window.addEventListener('keydown', handleKeyDown);
        return () => window.removeEventListener('keydown', handleKeyDown);
    }, [zoomLevel]);

    const handleLockToggle = () => {
        if (selectedIds.length > 0) {
            toggleLock(selectedIds);
        }
    };

    const handleGroup = () => {
        if (selectedIds.length > 1) {
            groupElements(selectedIds);
        }
    };

    const handleUngroup = () => {
        if (selectedIds.length === 1) {
            const groupId = selectedIds[0];
            if (groupId.startsWith('group_')) {
                ungroupElements(groupId);
            }
        }
    };

    // دوال التحكم في الزوم
    const handleZoomIn = () => {
        const newZoomLevel = Math.min(zoomLevel + 10, 200);
        if (zoomLevel !== newZoomLevel) {
            zoom(newZoomLevel);
        }
    };
    
    const handleZoomOut = () => {
        const newZoomLevel = Math.max(zoomLevel - 10, 50);
        if (zoomLevel !== newZoomLevel) {
            zoom(newZoomLevel);
        }
    };
    
    const handleZoomReset = () => {
        if (zoomLevel !== 100) {
            zoom(100);
        }
    };
    // دالة قفل العنصر
    const isElementLocked = () => {
        // إذا كان لديك منطق للقفل، ضعه هنا. مؤقتاً يرجع false دائماً
        return false;
    };

    // تعطيل زر Save As New إذا لم يتم اختيار مجموعة
    console.log('Current groupId:', groupId, 'Type:', typeof groupId);
    console.log('Current cardType:', cardType);
    console.log('userSelectedCardType:', userSelectedCardType);
    
    // فحص بسيط - هل اختار المستخدم نوع البطاقة فعلاً؟
    const isCardTypeSelected = userSelectedCardType;
    
    console.log('isCardTypeSelected:', isCardTypeSelected, 'cardType.id:', cardType?.id);
    const isSaveAsDisabled = !isCardTypeSelected || !elements || elements.length === 0 || (!groupId && groupId !== 0);
    console.log('isSaveAsDisabled:', isSaveAsDisabled);

    return (
        <div className="canva-toolbar w-full bg-white border-b border-gray-200 flex items-center justify-between px-0 py-2">
            <div className="flex items-center space-x-4">
                {/* Divider */}
                <div className="h-6 w-px bg-gray-300"></div>

                {/* Undo/Redo */}
                <div className="flex items-center space-x-2">
                    <button
                        className="p-2 rounded hover:bg-gray-100"
                        onClick={undo}
                        title="Undo"
                    >
                        <FiRotateCcw />
                    </button>
                    <button
                        className="p-2 rounded hover:bg-gray-100"
                        onClick={redo}
                        title="Redo"
                    >
                        <FiRotateCw />
                    </button>
                </div>

                {/* Divider */}
                <div className="h-6 w-px bg-gray-300"></div>

                {/* Zoom Controls */}
                <div className="flex items-center space-x-2 zoom-controls">
                    <button
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleZoomOut();
                        }}
                        className={`p-2 hover:bg-gray-100 rounded transition-colors ${zoomLevel <= 50 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        title="Zoom Out (Ctrl + -)"
                        disabled={zoomLevel <= 50}
                    >
                        <FiMinus />
                    </button>
                    <span className="text-sm font-medium zoom-level-display">
                        {zoomLevel}%
                    </span>
                    <button
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleZoomIn();
                        }}
                        className={`p-2 hover:bg-gray-100 rounded transition-colors ${zoomLevel >= 200 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        title="Zoom In (Ctrl + +)"
                        disabled={zoomLevel >= 200}
                    >
                        <FiPlus />
                    </button>
                    <button
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleZoomReset();
                        }}
                        className={`p-2 hover:bg-gray-100 rounded transition-colors ${zoomLevel === 100 ? 'bg-blue-100 text-blue-600' : ''}`}
                        title="Reset Zoom (Ctrl + 0)"
                    >
                        <FiMaximize />
                    </button>
                </div>

                {/* Divider */}
                <div className="h-6 w-px bg-gray-300"></div>

                {/* Element Controls */}
                <div className="flex items-center space-x-2">
                    <button
                        className={`p-2 rounded hover:bg-gray-100 ${selectedIds.length === 0 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={handleLockToggle}
                        disabled={selectedIds.length === 0}
                        title={selectedIds.length > 0 && isElementLocked() ? "Unlock" : "Lock"}
                    >
                        {selectedIds.length > 0 && isElementLocked() ? <FiUnlock /> : <FiLock />}
                    </button>
                    <button
                        className={`p-2 rounded hover:bg-gray-100 ${selectedIds.length < 2 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={handleGroup}
                        disabled={selectedIds.length < 2}
                        title="Group"
                    >
                        <BiGroup />
                    </button>
                    <button
                        className={`p-2 rounded hover:bg-gray-100 ${selectedIds.length !== 1 || !selectedIds[0].startsWith('group_') ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={handleUngroup}
                        disabled={selectedIds.length !== 1 || !selectedIds[0].startsWith('group_')}
                        title="Ungroup"
                    >
                        <BiLayer />
                    </button>
                </div>

                {/* Divider */}
                <div className="h-6 w-px bg-gray-300"></div>

                {/* Layer Controls */}
                <div className="flex items-center space-x-2">
                    <button
                        className={`p-2 rounded hover:bg-gray-100 ${selectedIds.length !== 1 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={() => selectedIds.length === 1 && bringToFront(selectedIds[0])}
                        disabled={selectedIds.length !== 1}
                        title="Bring to Front"
                    >
                        <BiSolidLayerPlus />
                    </button>
                    <button
                        className={`p-2 rounded hover:bg-gray-100 ${selectedIds.length !== 1 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={() => selectedIds.length === 1 && sendToBack(selectedIds[0])}
                        disabled={selectedIds.length !== 1}
                        title="Send to Back"
                    >
                        <BiSolidLayerMinus />
                    </button>
                </div>

                {/* Divider */}
                <div className="h-6 w-px bg-gray-300"></div>

                {/* Image Editing Tools */}
               
            </div>

            {/* Save Design Button */}
            <div className="flex items-center">
                {!isCreateMode && (
                    <button
                        className={`main-btn text-md shadow-sm flex items-center ${(!isDirty || isSaving) ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={isDirty && !isSaving ? (() => {
                            setSelectedIds([]);
                            setTimeout(() => {
                                if (saveDesignHandler) saveDesignHandler();
                            }, 0);
                        }) : undefined}
                        style={{
                            backgroundColor: !isDirty || isSaving ? '#bdbdbd' : 'var(--main_color, #00c3ac)',
                            borderColor: !isDirty || isSaving ? '#bdbdbd' : 'var(--main_color, #00c3ac)',
                            fontWeight: 'bold',
                            borderRadius: '6px',
                            padding: '6px 10px',
                            color: '#ffffff',
                            transition: 'background 0.2s, border 0.2s',
                            marginRight: '12px'
                        }}
                        disabled={!isDirty || isSaving}
                        title={!isDirty ? 'No changes to save' : (isSaving ? 'Saving...' : 'Save Design')}
                    >
                        <FiSave className="mr-2" style={{ color: '#ffffff' }} />
                        <span style={{ color: '#ffffff' }}>
                            {isSaving ? (
                                <>
                                    <span className="animate-spin inline-block mr-1" style={{ border: '2px solid #fff', borderRadius: '50%', borderTop: '2px solid #00c3ac', width: '16px', height: '16px', verticalAlign: 'middle' }}></span>
                                    Saving...
                                </>
                            ) : 'Save Design'}
                        </span>
                    </button>
                )}
                <button
                    className={`main-btn text-md shadow-sm flex items-center ${isSaveAsDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                    onClick={() => {
                        if (isSaveAsDisabled) {
                            setShowGroupModal(true);
                            return;
                        }
                        setSelectedIds([]);
                        setTimeout(() => {
                            if (saveAsHandler) saveAsHandler();
                        }, 0);
                    }}
                    style={{
                        backgroundColor: isSaveAsDisabled ? '#bdbdbd' : 'var(--main_color, #6c63ff)',
                        borderColor: isSaveAsDisabled ? '#bdbdbd' : 'var(--main_color, #6c63ff)',
                        fontWeight: 'bold',
                        borderRadius: '6px',
                        padding: '6px 10px',
                        marginRight:'20px',
                        color: '#ffffff',
                        transition: 'background 0.2s, border 0.2s',
                    }}
                    title={isSaveAsDisabled ? 'You must select a group and have elements in the design area' : 'Save As Design'}
                >
                    <FiCopy className="mr-2" style={{ color: '#ffffff' }} />
                    <span style={{ color: '#ffffff' }}>Save As New</span>
                </button>
               

                <Dialog
                    visible={showGroupModal}
                    onHide={() => setShowGroupModal(false)}
                    header={<span className="text-blue-700 font-bold">Requirements Not Met</span>}
                    style={{ width: '500px', borderRadius: '12px' }}
                    className="p-fluid text-center"
                    modal
                    closable
                    draggable={false}
                    resizable={false}
                    footer={<button className="main-btn px-6 py-2 rounded text-white bg-blue-600 hover:bg-blue-700 transition" onClick={() => setShowGroupModal(false)}>OK</button>}
                >
                    <div className="flex flex-col items-center justify-center">
                        <i className="pi pi-exclamation-triangle text-4xl text-blue-500 mb-3" />
                        <p className="text-gray-700 mb-4">
                            To <b>Save Design</b>, you must meet the following requirements:
                        </p>
                        
                        {/* Requirements List with Icons */}
                        <div className="w-full space-y-3 mb-4">
                            {/* Card Type Requirement */}
                            <div className={`flex items-center justify-between p-3 rounded-lg border transition-all duration-300 ${
                                userSelectedCardType
                                    ? 'bg-green-50 border-green-200 shadow-sm' 
                                    : 'bg-red-50 border-red-200 shadow-sm'
                            }`}>
                                <div className="flex items-center space-x-3">
                                    <div className={`flex items-center justify-center w-8 h-8 rounded-full transition-all duration-300 ${
                                        userSelectedCardType
                                            ? 'bg-green-500 text-white shadow-lg scale-110' 
                                            : 'bg-red-500 text-white shadow-lg'
                                    }`}>
                                        {userSelectedCardType ? (
                                            <FiCheck className="w-5 h-5 animate-pulse" />
                                        ) : (
                                            <FiX className="w-5 h-5" />
                                        )}
                                    </div>
                                    <span className={`font-medium ${
                                        userSelectedCardType ? 'text-green-700' : 'text-red-700'
                                    }`}>
                                        Select a card type from the dropdown
                                    </span>
                                </div>
                                {userSelectedCardType && (
                                    <div className="text-green-500 text-sm font-medium animate-bounce">
                                        ✓ Complete
                                    </div>
                                )}
                            </div>

                            {/* Elements Requirement */}
                            <div className={`flex items-center justify-between p-3 rounded-lg border transition-all duration-300 ${
                                elements && elements.length > 0 
                                    ? 'bg-green-50 border-green-200 shadow-sm' 
                                    : 'bg-red-50 border-red-200 shadow-sm'
                            }`}>
                                <div className="flex items-center space-x-3">
                                    <div className={`flex items-center justify-center w-8 h-8 rounded-full transition-all duration-300 ${
                                        elements && elements.length > 0 
                                            ? 'bg-green-500 text-white shadow-lg scale-110' 
                                            : 'bg-red-500 text-white shadow-lg'
                                    }`}>
                                        {elements && elements.length > 0 ? (
                                            <FiCheck className="w-5 h-5 animate-pulse" />
                                        ) : (
                                            <FiX className="w-5 h-5" />
                                        )}
                                    </div>
                                    <span className={`font-medium ${
                                        elements && elements.length > 0 ? 'text-green-700' : 'text-red-700'
                                    }`}>
                                        Add elements to the design area
                                    </span>
                                </div>
                                {elements && elements.length > 0 && (
                                    <div className="text-green-500 text-sm font-medium animate-bounce">
                                        ✓ Complete
                                    </div>
                                )}
                            </div>

                            {/* Group Requirement */}
                            <div className={`flex items-center justify-between p-3 rounded-lg border transition-all duration-300 ${
                                groupId !== undefined && groupId !== null 
                                    ? 'bg-green-50 border-green-200 shadow-sm' 
                                    : 'bg-red-50 border-red-200 shadow-sm'
                            }`}>
                                <div className="flex items-center space-x-3">
                                    <div className={`flex items-center justify-center w-8 h-8 rounded-full transition-all duration-300 ${
                                        groupId !== undefined && groupId !== null 
                                            ? 'bg-green-500 text-white shadow-lg scale-110' 
                                            : 'bg-red-500 text-white shadow-lg'
                                    }`}>
                                        {groupId !== undefined && groupId !== null ? (
                                            <FiCheck className="w-5 h-5 animate-pulse" />
                                        ) : (
                                            <FiX className="w-5 h-5" />
                                        )}
                                    </div>
                                    <span className={`font-medium ${
                                        groupId !== undefined && groupId !== null ? 'text-green-700' : 'text-red-700'
                                    }`}>
                                        Choose a group from the group selection
                                    </span>
                                </div>
                                {groupId !== undefined && groupId !== null && (
                                    <div className="text-green-500 text-sm font-medium animate-bounce">
                                        ✓ Complete
                                    </div>
                                )}
                            </div>
                        </div>

                        <p className="text-gray-700 text-sm">
                            Please complete these requirements before saving your design as a new template.
                        </p>
                    </div>
                </Dialog>
            </div>
        </div>
    );
};

CanvaToolbar.propTypes = {
    saveDesignHandler: PropTypes.func.isRequired,
    saveAsHandler: PropTypes.func.isRequired,
    isDirty: PropTypes.bool,
    isCreateMode: PropTypes.bool,
    isSaving: PropTypes.bool
};

export default CanvaToolbar;