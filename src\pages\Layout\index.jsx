import React from 'react'
import SideMenu from './SideMenu'
import Banner from './Banner'
import { useLayout } from '../../contexts/LayoutContext'
import { Bars3Icon, XMarkIcon, ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline'

function Layout({ children }) {
    const {
        getSidebarClasses,
        getMainContentClasses,
        isMobile,
        isSidebarCollapsed,
        isBurgerMenuOpen,
        toggleSidebar
    } = useLayout();

    return (
        <main className='flex h-[100vh] bg-[#e5e7eb] relative'>
            {/* Burger Icon - positioned outside of any stacking contexts */}
            <button
                onClick={toggleSidebar}
                className={`fixed top-4 z-[9999] p-2 bg-gray-200 hover:bg-gray-300 rounded-md transition-all duration-300 shadow-md ${
                    isMobile ? 'left-4' : (isSidebarCollapsed ? 'left-4' : 'left-4')
                }`}
                title={isMobile ? 'Toggle Menu' : (isSidebarCollapsed ? 'Expand Sidebar' : 'Collapse Sidebar')}
            >
                {isMobile ? (
                    isBurgerMenuOpen ? (
                        <XMarkIcon className="h-6 w-6 text-gray-700" />
                    ) : (
                        <Bars3Icon className="h-6 w-6 text-gray-700" />
                    )
                ) : (
                    isSidebarCollapsed ? (
                        <ChevronRightIcon className="h-6 w-6 text-gray-700" />
                    ) : (
                        <ChevronLeftIcon className="h-6 w-6 text-gray-700" />
                    )
                )}
            </button>

            <aside className={`
                ${getSidebarClasses()}
                layout-sidebar
                ${isMobile ? (isBurgerMenuOpen ? 'mobile-open' : '') : (isSidebarCollapsed ? 'sidebar-collapsed' : 'sidebar-expanded')}
            `}>
                <SideMenu />
            </aside>
            <section className={`
                ${getMainContentClasses()}
                layout-main-content
                ${!isMobile && isSidebarCollapsed ? 'layout-main-expanded' : 'layout-main-normal'}
                flex flex-col
            `}>
                {/* <Banner />                                      Disabled Navbar */}

                <div className="w-full p-5 h-[95vh] overflow-y-auto">
                    {children}
                </div>
            </section>
        </main>
    )
}

export default Layout