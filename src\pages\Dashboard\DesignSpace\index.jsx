import { useParams } from "react-router-dom";
import { useEffect, useState } from "react";
import "@css/design-space.css"

import { useGetDesign, useGetCardsTypes } from "@quires/template";
import { useGlobalContext } from "@contexts/GlobalContext";
import { useDesignSpace } from "@contexts/DesignSpaceContext";
import CreationDialog from "./components/CreationDialog";
import ImageGenerationModal from "./components/ImageGenerationModal";
import DesignSpace from "./DesignSpace";


const DesignIndex = () => {
  const { setElements, setCardType, setGroupId } = useDesignSpace();
  const { openDialog, dialogHandler } = useGlobalContext();
  const [formData, setFormData] = useState({});
  const [showImageGenerationModal, setShowImageGenerationModal] = useState(false);
  const [isSaveAsMode, setIsSaveAsMode] = useState(false);
  const [navigationOptions, setNavigationOptions] = useState({
    stayOnPage: true,
    goToTemplates: false
  });
  const [hasSetInitialGroupId, setHasSetInitialGroupId] = useState(false);
  const [groupIds, setGroupIds] = useState([]);

  // Add event listener for formGroupIdsChanged
  useEffect(() => {
    const handleFormGroupIdsChanged = (event) => {
      console.log('formGroupIdsChanged event received in parent:', event.detail);
      setGroupIds(event.detail.group_ids || []);
    };

    window.addEventListener('formGroupIdsChanged', handleFormGroupIdsChanged);

    return () => {
      window.removeEventListener('formGroupIdsChanged', handleFormGroupIdsChanged);
    };
  }, []);

  const { id } = useParams();
  const [currentDesignId, setCurrentDesignId] = useState(id);

  // Log when currentDesignId changes
  useEffect(() => {
    console.log("index.jsx: currentDesignId changed to:", currentDesignId);
  }, [currentDesignId]);

  const { data: design } = useGetDesign(currentDesignId);
  const { data: typesOptions } = useGetCardsTypes();

  // Create a ref to store the latest template data
  const templateDataRef = useState({
    htmlTemplate: null,
    initTemplate: null,
    background: null,
    backgroundStyle: null
  });

  // Log design data when it's loaded
  useEffect(() => {
    if (design) {
      console.log("Design loaded with background data:", {
        background: design.background,
        background_style: design.background_style
      });
    }
  }, [design]);

  useEffect(() => {
    if (design && design.group_id !== undefined && design.group_id !== null && !hasSetInitialGroupId) {
      console.log("Setting groupId from design:", design.group_id);
      setGroupId(design.group_id);
      setHasSetInitialGroupId(true);
    }
  }, [design, setGroupId, hasSetInitialGroupId]);

  useEffect(() => {
    setHasSetInitialGroupId(false);
  }, [currentDesignId]);

  // Listen for canvas background changes and update formData
  useEffect(() => {
    const handleBackgroundChange = (event) => {
      console.log("Background change event received:", event.detail);

      // Update formData with the new background information
      setFormData((prev) => {
        const newFormData = {
          ...prev,
          background: event.detail.background,
          backgroundStyle: event.detail.backgroundStyle ?
            (typeof event.detail.backgroundStyle === 'string' ?
              event.detail.backgroundStyle :
              JSON.stringify(event.detail.backgroundStyle)) :
            null
        };

        console.log("Updated formData with new background:", {
          background: newFormData.background,
          backgroundStyle: newFormData.backgroundStyle
        });

        return newFormData;
      });
    };

    // Add event listener
    window.addEventListener('canvasBackgroundChanged', handleBackgroundChange);

    // Clean up
    return () => {
      window.removeEventListener('canvasBackgroundChanged', handleBackgroundChange);
    };
  }, []);

  // Function to update template data (will be passed to CanvaToolbar)
  const updateTemplateData = (data) => {
    console.log("updateTemplateData called in DesignIndex with data:", {
      hasHtmlTemplate: !!data?.htmlTemplate,
      htmlTemplateLength: data?.htmlTemplate?.length || 0,
      hasInitTemplate: !!data?.initTemplate,
      hasBackground: !!data?.background,
      hasBackgroundStyle: !!data?.backgroundStyle
    });

    // Update the template data ref
    templateDataRef[1](data);

    // Also update formData for backward compatibility
    setFormData((prev) => {
      const newFormData = { ...prev, ...data };
      console.log("Updated formData:", {
        hasHtmlTemplate: !!newFormData?.htmlTemplate,
        htmlTemplateLength: newFormData?.htmlTemplate?.length || 0,
        name: newFormData?.name
      });
      return newFormData;
    });
  };

  useEffect(() => {
    if (design) {
      const card = typesOptions?.find(obj => obj.id === design?.card_type_id);

      if (card) {
        setCardType({ ...card.setting, id: card.id }, false); // Set isUserSelection to false for loaded design
      }

      if (design?.init_template) {
        try {
          setElements(JSON.parse(design.init_template));
        } catch (error) {
          console.error("Error parsing init_template:", error);
        }
      }

      // Update form data with design information including background
      setFormData((prev) => ({
        ...prev,
        name: design?.name,
        background: design?.background || null,
        backgroundStyle: design?.background_style || null
      }));

      // Log background information from the loaded design
      console.log("Loaded design with background:", {
        background: design?.background,
        backgroundStyle: design?.background_style
      });
    }

    return () => {
      setElements([]);
    }
  }, [design, typesOptions]);

  // Combine the design name with the template data
  const combinedFormData = {
    ...formData,
    ...templateDataRef[0]
  };

  // Log the combined form data that will be passed to CreationDialog
  console.log("Combined form data for CreationDialog:", {
    hasHtmlTemplate: !!combinedFormData?.htmlTemplate,
    htmlTemplateLength: combinedFormData?.htmlTemplate?.length || 0,
    hasName: !!combinedFormData?.name,
    name: combinedFormData?.name
  });

  return (
    <div className='w-full flex flex-col bg-white min-h-screen h-screen p-0 overflow-hidden'>
      <div className="w-full flex h-full">
        <DesignSpace
          design={design}
          updateTemplateData={updateTemplateData}
          onImageGenerationStart={() => setShowImageGenerationModal(true)}
          onSaveAsDesign={() => {
            if (window && window.updateTemplateDataFromDesignSpace) {
              window.updateTemplateDataFromDesignSpace(() => {
                setIsSaveAsMode(true);
                if (typeof dialogHandler === 'function') {
                  dialogHandler("createDesignTemplate");
                }
              });
            } else {
              setIsSaveAsMode(true);
              if (typeof dialogHandler === 'function') {
                dialogHandler("createDesignTemplate");
              }
            }
          }}
        />
      </div>
      {openDialog?.createDesignTemplate ? (
        <CreationDialog
          formData={combinedFormData}
          isSaveAsMode={isSaveAsMode}
          onSuccess={(newDesignId, navOptions) => {
            setIsSaveAsMode(false);
            console.log("New design created with ID:", newDesignId);
            if (newDesignId) {
              setCurrentDesignId(newDesignId);
            }
            // Update navigation options
            if (navOptions) {
              setNavigationOptions(navOptions);
            }
            setShowImageGenerationModal(true);
          }}
        />
      ) : ""}

      {/* Image Generation Modal */}
      <ImageGenerationModal
        visible={showImageGenerationModal}
        designId={currentDesignId}
        groupIds={groupIds}
        navigationOptions={navigationOptions}
        onHide={() => setShowImageGenerationModal(false)}
      />
    </div>
  );
};

export default DesignIndex;
