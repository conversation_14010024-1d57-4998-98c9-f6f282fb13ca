import { useEffect, useState, useMemo } from 'react';
import { Dropdown } from 'primereact/dropdown';
import { useMembersDataTableContext } from '@contexts/MembersDataTableContext';

const GroupFilter = ({ designs: propDesigns }) => {
    const { setLazyParams } = useMembersDataTableContext();
    const [designId, setDesignId] = useState();

    // Filter designs based on the group's card type
    const filteredDesigns = useMemo(() => {
        // If propDesigns is provided and has designs, use only those (group-specific designs)
        if (Array.isArray(propDesigns) && propDesigns.length > 0) {
            return propDesigns;
        }

        // If no group-specific designs are provided, return empty array
        return [];
    }, [propDesigns]);

    useEffect(() => {
        // Update lazyParams when designId changes (including when it becomes null)
        setLazyParams(prev => ({
            ...prev,
            designID: designId || null // Use null when designId is undefined/null/empty (thus fixing the bug relating to pressing "x" on the dropdown and not properly clearing the filter)
        }));
    }, [designId, setLazyParams]);

    const handleChange = (e) => {
        // Set designId to the new value (or undefined if cleared)
        setDesignId(e.value);
    };

    return (
        <Dropdown
            filter
            showClear
            filterBy="name"
            className='rounded-[6px] text-[black] w-full'
            optionLabel="name"
            optionValue="id"
            value={designId}
            options={filteredDesigns}
            onChange={handleChange}
            placeholder={
                filteredDesigns.length > 0
                    ? "Select design"
                    : "No designs available"
            }
            emptyMessage={filteredDesigns.length > 0 ? "No designs available for this group" : "No designs available"}
        />
    );
};

export default GroupFilter;