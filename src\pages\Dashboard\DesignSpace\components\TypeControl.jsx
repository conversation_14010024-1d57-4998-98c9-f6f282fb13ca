import { useEffect, useMemo, useState, useCallback, useRef } from 'react';
import { useQueryClient } from 'react-query';
import PropTypes from 'prop-types';

import { Dropdown } from 'primereact/dropdown';
import { MultiSelect } from 'primereact/multiselect';

import { useDesignSpace } from '@contexts/DesignSpaceContext';
import { useGetCardsTypes } from "@quires/template";
import { isEmpty } from 'lodash';
import axiosInstance from '../../../../config/Axios';

// Default card types if API fails
const DEFAULT_CARD_TYPES = [
    {
        id: 'default-business-card',
        name: 'Business Card',
        setting: {
            id: 'default-business-card',
            width: 350,
            height: 200
        }
    },
    {
        id: 'default-instagram',
        name: 'Instagram Post',
        setting: {
            id: 'default-instagram',
            width: 1080,
            height: 1080
        }
    },
    {
        id: 'default-id-badge',
        name: 'ID Badge',
        setting: {
            id: 'default-id-badge',
            width: 300,
            height: 450
        }
    }
];

function TypeControl({ hideLabel = false }) {
    const queryClient = useQueryClient();
    const { data: cardsTypesData, isLoading } = useGetCardsTypes();

    const { cardType, setCardType, groupId, setGroupId } = useDesignSpace();
    
    // Add logging to verify context values
    console.log('TypeControl - Context values:', {
        cardType: cardType?.id,
        groupId: groupId,
        setGroupId: typeof setGroupId
    });
    
    const [typesOptions, setTypesOptions] = useState(DEFAULT_CARD_TYPES);
    const [groups, setGroups] = useState([]);
    const [groupsLoading, setGroupsLoading] = useState(false);
    
    // Add ref to track if groupId was set from loaded data
    const initialGroupIdRef = useRef(null);
    const hasProcessedInitialGroupId = useRef(false);
    const userSelectedGroupRef = useRef(false);

    // Track initial groupId when component mounts or groupId changes
    useEffect(() => {
        // Handle both single value and array
        const currentGroupId = Array.isArray(groupId) ? (groupId.length === 1 ? groupId[0] : groupId) : groupId;
        
        if (currentGroupId !== undefined && currentGroupId !== null && !hasProcessedInitialGroupId.current) {
            initialGroupIdRef.current = currentGroupId;
            hasProcessedInitialGroupId.current = true;
            userSelectedGroupRef.current = false; // This is from loaded data, not user selection
            console.log('TypeControl - Initial groupId set from loaded data:', currentGroupId);
        }
    }, [groupId]);

    // Reset the flag when component unmounts or when navigating to a different design
    useEffect(() => {
        return () => {
            hasProcessedInitialGroupId.current = false;
            initialGroupIdRef.current = null;
            userSelectedGroupRef.current = false;
        };
    }, []);

    // Ensure groupId is preserved when groups are loaded for the first time
    useEffect(() => {
        if (groups.length > 0 && initialGroupIdRef.current !== null && initialGroupIdRef.current !== undefined && !userSelectedGroupRef.current) {
            const initialGroupExists = groups.some(group => group.id === initialGroupIdRef.current);
            if (initialGroupExists) {
                // Check if current groupId is different from initial
                const currentGroupId = Array.isArray(groupId) ? (groupId.length === 1 ? groupId[0] : groupId) : groupId;
                if (currentGroupId !== initialGroupIdRef.current) {
                    console.log('Restoring initial groupId after groups loaded:', initialGroupIdRef.current);
                    setGroupId(initialGroupIdRef.current);
                }
            }
        }
    }, [groups, groupId, setGroupId]);

    // Load design groups when component mounts (for editing existing designs)
    useEffect(() => {
        const loadDesignGroups = async () => {
            try {
                // Get design ID from URL or props
                const urlParams = new URLSearchParams(window.location.search);
                const designId = urlParams.get('id') || window.location.pathname.split('/').pop();
                
                if (designId && designId !== 'new' && !isNaN(designId)) {
                    console.log('Loading design groups for design ID:', designId);
                    
                    const response = await axiosInstance.get(`/designs/${designId}/groups`);
                    
                    if (response.data.success && response.data.groups) {
                        const designGroups = response.data.groups;
                        console.log('Loaded design groups:', designGroups);
                        
                        // Set the groups as selected in the MultiSelect
                        if (designGroups.length > 0) {
                            const groupIds = designGroups.map(group => group.id);
                            setGroupId(groupIds.length === 1 ? groupIds[0] : groupIds);
                            userSelectedGroupRef.current = false; // This is from loaded data
                            initialGroupIdRef.current = groupIds.length === 1 ? groupIds[0] : groupIds[0]; // Store initial value
                            console.log('Set groupId from loaded design groups:', groupIds);
                        }
                    }
                }
            } catch (error) {
                console.error('Error loading design groups:', error);
            }
        };

        loadDesignGroups();
    }, []); // Run only once when component mounts

    // Load groups for current card type when component mounts or card type changes
    useEffect(() => {
        if (cardType && cardType.id) {
            console.log('Loading groups for card type on mount/change:', cardType.id);
            setGroupsLoading(true);
            
            axiosInstance.get(`/groups?card_type_id=${cardType.id}`)
                .then(res => {
                    console.log('Groups API response for card type:', res.data);
                    const data = Array.isArray(res.data) ? res.data : (res.data.data || []);
                    console.log('Processed groups data for card type:', data);
                    setGroups(data);
                    
                    // If we have an initial groupId from loaded design data, check if it's valid
                    if (initialGroupIdRef.current !== null && initialGroupIdRef.current !== undefined && !userSelectedGroupRef.current) {
                        const initialGroupExists = data.some(group => group.id === initialGroupIdRef.current);
                        if (initialGroupExists) {
                            console.log('Setting groupId to initial value from loaded data:', initialGroupIdRef.current);
                            setGroupId(initialGroupIdRef.current);
                        } else {
                            console.log('Initial groupId is not valid for this cardType, setting to undefined');
                            setGroupId(undefined);
                        }
                    }
                })
                .catch((error) => {
                    console.error('Error fetching groups for card type:', error);
                    setGroups([{ id: 0, title: 'All Groups' }]);
                })
                .finally(() => setGroupsLoading(false));
        }
    }, [cardType?.id]); // Run when card type changes

    // Get data from query client or from direct hook
    useEffect(() => {
        try {
            const cachedData = queryClient.getQueryData('getCardsTypes');
            if (cachedData && Array.isArray(cachedData) && cachedData.length > 0) {
                setTypesOptions(cachedData);
            } else if (cardsTypesData && Array.isArray(cardsTypesData) && cardsTypesData.length > 0) {
                setTypesOptions(cardsTypesData);
            } else {
                console.log('Using default card types');
                setTypesOptions(DEFAULT_CARD_TYPES);
            }
        } catch (error) {
            console.error('Error loading card types:', error);
            setTypesOptions(DEFAULT_CARD_TYPES);
        }
    }, [queryClient, cardsTypesData]);

    useEffect(() => {
        console.log('useEffect triggered - cardType changed:', cardType?.id);
        if (cardType && cardType.id) {
            setGroupsLoading(true);
            console.log('Fetching groups for card_type_id:', cardType.id); 
            axiosInstance.get(`/groups?card_type_id=${cardType.id}`)
                .then(res => {
                    console.log('Groups API response:', res.data);
                    const data = Array.isArray(res.data) ? res.data : (res.data.data || []);
                    console.log('Processed groups data:', data);
                    setGroups(data);
                    
                    // Check if current groupId is still valid for the new cardType
                    if (groupId && groupId !== null && groupId !== undefined) {
                        // Handle both single value and array
                        const groupIds = Array.isArray(groupId) ? groupId : [groupId];
                        const validGroups = groupIds.filter(id => data.some(group => group.id === id));
                        
                        if (validGroups.length === 0) {
                            console.log('Current groupId is not valid for new cardType, resetting to undefined');
                            setGroupId(undefined);
                            userSelectedGroupRef.current = false; // Reset user selection flag
                        } else if (validGroups.length !== groupIds.length) {
                            console.log('Some groups are not valid, keeping only valid ones:', validGroups);
                            setGroupId(validGroups.length === 1 ? validGroups[0] : validGroups);
                        } else {
                            console.log('Current groupId is still valid, keeping it');
                        }
                    } else if (initialGroupIdRef.current !== null && initialGroupIdRef.current !== undefined && !userSelectedGroupRef.current) {
                        // Check if the initial groupId from loaded data is valid for this cardType
                        const initialGroupExists = data.some(group => group.id === initialGroupIdRef.current);
                        if (initialGroupExists) {
                            console.log('Setting groupId to initial value from loaded data:', initialGroupIdRef.current);
                            setGroupId(initialGroupIdRef.current);
                        } else {
                            console.log('Initial groupId is not valid for this cardType, setting to undefined');
                            setGroupId(undefined);
                        }
                    } else {
                        console.log('No current groupId and no initial groupId, setting to undefined');
                        setGroupId(undefined);
                    }
                })
                .catch((error) => {
                    console.error('Error fetching groups:', error);
                    setGroups([{ id: 0, title: 'All Groups' }]);
                    // Don't reset groupId on error if we have an initial value and user hasn't selected
                    if ((initialGroupIdRef.current === null || initialGroupIdRef.current === undefined) && !userSelectedGroupRef.current) {
                        setGroupId(undefined);
                    } else if (userSelectedGroupRef.current) {
                        // If user has selected groups, check if they're still valid
                        const currentGroupIds = Array.isArray(groupId) ? groupId : [groupId];
                        const validGroups = currentGroupIds.filter(id => id === 0 || id === initialGroupIdRef.current);
                        if (validGroups.length === 0) {
                    setGroupId(undefined);
                        } else if (validGroups.length !== currentGroupIds.length) {
                            setGroupId(validGroups.length === 1 ? validGroups[0] : validGroups);
                        }
                    }
                })
                .finally(() => setGroupsLoading(false));
        } else {
            console.log('No cardType or cardType.id, setting default groups');
            setGroups([{ id: 0, title: 'All Groups' }]);
            // Don't reset groupId if we have an initial value and user hasn't selected
            if ((initialGroupIdRef.current === null || initialGroupIdRef.current === undefined) && !userSelectedGroupRef.current) {
                setGroupId(undefined);
            } else if (userSelectedGroupRef.current) {
                // If user has selected groups, check if they're still valid
                const currentGroupIds = Array.isArray(groupId) ? groupId : [groupId];
                const validGroups = currentGroupIds.filter(id => id === 0 || id === initialGroupIdRef.current);
                if (validGroups.length === 0) {
            setGroupId(undefined);
                } else if (validGroups.length !== currentGroupIds.length) {
                    setGroupId(validGroups.length === 1 ? validGroups[0] : validGroups);
                }
            }
        }
    }, [cardType]); // Remove setGroupId from dependencies to avoid infinite loop

    // Create options from typesOptions with safe fallbacks
    const options = useMemo(() => {
        try {
            if (!typesOptions || !Array.isArray(typesOptions) || typesOptions.length === 0) {
                return DEFAULT_CARD_TYPES;
            }

            return typesOptions.map(item => ({
                name: item.name || 'Unnamed Card',
                setting: {
                    id: item.id || `card-${item.name?.replace(/\s+/g, '-').toLowerCase() || 'unnamed'}`,
                    width: (item.setting?.width) || 300,
                    height: (item.setting?.height) || 200
                }
            }));
        } catch (error) {
            console.error('Error creating options:', error);
            return DEFAULT_CARD_TYPES;
        }
    }, [typesOptions]);

    // Set default card type if none is selected - only runs once when options are loaded
    useEffect(() => {
        try {
            // Only set card type if it's empty and we have options
            if (isEmpty(cardType) && options && options.length > 0) {
                setCardType({ ...options[0]?.setting });
            }
        } catch (error) {
            console.error('Error setting default card type:', error);
            if (DEFAULT_CARD_TYPES.length > 0) {
                setCardType({ ...DEFAULT_CARD_TYPES[0].setting });
            }
        }
        // No cleanup function to avoid resetting card type
        // This effect should only run when options change
    }, [options]); // Remove cardType and setCardType dependencies
    // Loading and error states
    const isDataReady = options && options.length > 0;

    // Memoize dropdown options to prevent re-renders
    const dropdownOptions = useMemo(() => {
        return isDataReady ? options : DEFAULT_CARD_TYPES;
    }, [isDataReady, options]);

    // Handle dropdown change safely - memoized to prevent re-renders
    const handleDropdownChange = useCallback((e) => {
        try {
            if (e && e.value) {
                setCardType(e.value, true); 
            }
        } catch (error) {
            console.error('Error changing card type:', error);
        }
    }, [setCardType]);

    // Memoize the entire dropdown component to prevent re-renders
    const dropdownComponent = useMemo(() => {
        if (isLoading) {
            return <div className="rounded-[6px] me-3 text-sm bg-gray-100 p-2 text-gray-500">Loading...</div>;
        }

        return (
            <Dropdown
                className='rounded-[6px] me-3 text-[black] text-sm'
                optionLabel="name"
                optionValue="setting"
                value={cardType}
                options={dropdownOptions}
                onChange={handleDropdownChange}
                placeholder="Select a Type"
                panelClassName="text-sm"
            />
        );
    }, [isLoading, cardType, dropdownOptions, handleDropdownChange]);

    const groupDropdown = (
        <MultiSelect
            className="rounded-[6px] me-3 text-[black] text-sm min-w-[200px]"
            optionLabel="title"
            optionValue="id"
            value={Array.isArray(groupId) ? groupId : (groupId ? [groupId] : [])}
            options={groups}
            onChange={e => {
                console.log('Group MultiSelect changed:', e.value);
                console.log('Previous groupId was:', groupId);
                console.log('Available groups:', groups);

                // Handle single selection (backward compatibility)
                if (e.value && e.value.length === 1) {
                    setGroupId(e.value[0]);
                    // Mark that user has selected a group
                    if (e.value[0] !== initialGroupIdRef.current) {
                        userSelectedGroupRef.current = true;
                        console.log('User selected a different group, marking as user selection');
                    }
                } else if (e.value && e.value.length > 1) {
                    // Multiple selection - store as array
                setGroupId(e.value);
                    userSelectedGroupRef.current = true;
                    console.log('User selected multiple groups:', e.value);
                } else {
                    // No selection
                    setGroupId(undefined);
                    userSelectedGroupRef.current = true;
                    console.log('User cleared group selection');
                }

                // Emit custom event for parent components to handle group_ids
                const event = new CustomEvent('groupIdsChanged', {
                    detail: {
                        groupIds: e.value || [],
                        groupId: e.value && e.value.length === 1 ? e.value[0] : (e.value && e.value.length > 1 ? e.value[0] : null)
                    }
                });
                window.dispatchEvent(event);

                // Also emit a more specific event for form submission
                const formEvent = new CustomEvent('formGroupIdsChanged', {
                    detail: {
                        group_ids: e.value || [], // This is what the backend expects
                        group_id: e.value && e.value.length === 1 ? e.value[0] : (e.value && e.value.length > 1 ? e.value[0] : null)
                    }
                });
                window.dispatchEvent(formEvent);
            }}
            placeholder="Select Groups"
            panelClassName="text-sm"
            filter
            showClear
            loading={groupsLoading}
            key={`group-multiselect-${cardType?.id}-${groups.length}`}
            display="chip"
            maxSelectedLabels={3}
            selectedItemsLabel="..."
            filterPlaceholder="Search groups..."
            emptyFilterMessage="No groups match your search"
            emptyMessage="No groups available"
            showSelectAll={true}
            selectAllLabel="Select All"
            unselectAllLabel="Unselect All"
        />
    );

    // Add logging to show current state
    console.log('TypeControl render - Current state:', {
        cardType: cardType?.id,
        groupId: groupId,
        groupIdType: Array.isArray(groupId) ? 'array' : typeof groupId,
        groupsCount: groups.length,
        groupsLoading: groupsLoading,
        initialGroupId: initialGroupIdRef.current,
        userSelectedGroup: userSelectedGroupRef.current
    });

    return (
        <div className="flex flex-col">
            {!hideLabel && <label className="mr-1 text-sm">Card Type</label>}
            <div className="flex flex-row items-center gap-2">
                {dropdownComponent}
                {groupDropdown}
            </div>
        </div>
    )
}

TypeControl.propTypes = {
    hideLabel: PropTypes.bool,
};

export default TypeControl