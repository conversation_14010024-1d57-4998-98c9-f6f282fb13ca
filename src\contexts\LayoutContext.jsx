import React, { createContext, useContext, useState, useEffect } from 'react';

const LayoutContext = createContext();

export const LayoutProvider = ({ children }) => {
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [isBurgerMenuOpen, setIsBurgerMenuOpen] = useState(false);

  // Effect to update isMobile state on window resize
  useEffect(() => {
    const handleResize = () => {
      const mobileView = window.innerWidth < 768;
      setIsMobile(mobileView);
      if (!mobileView) {
        setIsBurgerMenuOpen(false); // Close mobile menu when switching to desktop
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const toggleSidebar = () => {
    if (isMobile) {
      setIsBurgerMenuOpen(!isBurgerMenuOpen);
    } else {
      setIsSidebarCollapsed(!isSidebarCollapsed);
    }
  };

  const closeMobileMenu = () => {
    if (isMobile) {
      setIsBurgerMenuOpen(false);
    }
  };

  // Calculate sidebar width based on state
  const getSidebarWidth = () => {
    if (isMobile) {
      return isBurgerMenuOpen ? '256px' : '0px'; // w-64 = 256px
    } else {
      return isSidebarCollapsed ? '80px' : '16.666667%'; // w-20 = 80px, w-2/12 = 16.666667%
    }
  };

  // Calculate main content width based on sidebar state
  const getMainContentWidth = () => {
    if (isMobile) {
      return '100%';
    } else {
      return isSidebarCollapsed ? 'calc(100% - 80px)' : '83.333333%'; // w-10/12 = 83.333333%
    }
  };

  // Get Tailwind classes for sidebar
  const getSidebarClasses = () => {
    if (isMobile) {
      return isBurgerMenuOpen ? 'w-64' : 'w-0 -translate-x-full';
    } else {
      return isSidebarCollapsed ? 'w-20' : 'w-2/12';
    }
  };

  // Get Tailwind classes for main content
  const getMainContentClasses = () => {
    if (isMobile) {
      return 'w-full';
    } else {
      return isSidebarCollapsed ? 'flex-1' : 'w-10/12';
    }
  };

  const value = {
    isMobile,
    isSidebarCollapsed,
    isBurgerMenuOpen,
    toggleSidebar,
    closeMobileMenu,
    getSidebarWidth,
    getMainContentWidth,
    getSidebarClasses,
    getMainContentClasses,
  };

  return (
    <LayoutContext.Provider value={value}>
      {children}
    </LayoutContext.Provider>
  );
};

export const useLayout = () => {
  const context = useContext(LayoutContext);
  if (!context) {
    throw new Error('useLayout must be used within a LayoutProvider');
  }
  return context;
};
