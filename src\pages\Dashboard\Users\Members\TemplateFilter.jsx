import { useEffect, useState } from 'react';
import { Dropdown } from 'primereact/dropdown';
import { useMembersDataTableContext } from '@contexts/MembersDataTableContext';
import { useQueryClient } from "react-query";

const TemplateFilter = ({ templates: propTemplates }) => {
    const { setLazyParams, lazyParams } = useMembersDataTableContext();
    const [templates, setTemplates] = useState(propTemplates);
    const [selectedDesignId, setSelectedDesignId] = useState(null);

    const queryClient = useQueryClient();
    const cachedDesigns = queryClient.getQueryData("getDesigns");

    const onchange = (val) => {
        setLazyParams(prev => ({ ...prev, designID: val }));
    };

    useEffect(() => {
        if (lazyParams?.designID) {
            setSelectedDesignId(Number(lazyParams.designID));
        }
    }, [lazyParams?.designID]);

    return (
        <Dropdown
            value={selectedDesignId}
            options={cachedDesigns}
            onChange={(e) => {
                setSelectedDesignId(e.value);
                onchange(e.value);
            }}
            optionLabel="name"
            optionValue="id"
            filter 
            showClear 
            filterBy="name"
            placeholder="select design"
            className='rounded-[6px] me-3 text-[black]'
        />
    );
};

export default TemplateFilter;