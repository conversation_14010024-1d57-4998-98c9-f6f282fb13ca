import TemplatesDataTable from './TemplatesDataTable'
import Container from '@components/Container'

function Templates() {
  // لا يمكن معرفة حالة الاشتراك أو الباقة هنا مباشرة بدون رفع الحالة من TemplatesDataTable
  // الحل الأفضل: نقل زر Create New Template إلى TemplatesDataTable ليتم التحكم فيه بناءً على الشروط هناك
  // مؤقتاً: أخفي الزر دائماً هنا وأبلغ المستخدم بذلك
  return (
    
      <TemplatesDataTable />
   
  )
}

export default Templates
