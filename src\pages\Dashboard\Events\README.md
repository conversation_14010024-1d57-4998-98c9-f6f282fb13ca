# Events Management Page

This document describes the Events Management page implementation for manager users, following the established website design philosophy.

## Overview

The Events page provides a comprehensive solution for creating and managing events with temporary card assignments. It includes full CRUD operations for events and the ability to assign temporary member cards for specified time periods.

## Features

### Core Functionality
- **Create Events**: Add new events with detailed information (name, description, date/time, duration, location, attendees)
- **Edit Events**: Modify existing event details
- **Delete Events**: Remove events with confirmation dialogs
- **View Event Details**: Comprehensive event overview with assigned cards
- **Assign Temporary Cards**: Assign temporary member cards to events for specified time periods
- **Search & Filter**: Real-time search and filtering by status, date, and other criteria

### Design Implementation
- **Consistent UI**: Follows established color scheme and design patterns
- **Mobile Responsive**: Optimized for mobile devices with touch-friendly interfaces
- **Accessibility**: WCAG compliant with keyboard navigation and screen reader support
- **Animations**: Smooth transitions using Framer Motion
- **Loading States**: Proper loading indicators and error handling

## File Structure

```
src/pages/Dashboard/Events/
├── index.jsx                    # Main Events page component
├── Events.css                   # Mobile-responsive styles
├── README.md                    # This documentation
└── components/
    ├── CreateEventModal.jsx     # Create/Edit event modal
    ├── EventDetailsModal.jsx    # Event details view modal
    └── AssignCardsModal.jsx     # Card assignment modal
```

## Data Structure

### Event Object
```javascript
{
  id: number,
  name: string,
  description: string,
  startDate: string (YYYY-MM-DD),
  startTime: string (HH:MM),
  endDate: string (YYYY-MM-DD),
  endTime: string (HH:MM),
  duration: string,
  status: 'active' | 'upcoming' | 'completed' | 'cancelled' | 'draft',
  location: string,
  maxAttendees: number,
  currentAttendees: number,
  createdAt: string (ISO),
  createdBy: string,
  temporaryCards: TemporaryCard[]
}
```

### Temporary Card Assignment Object
```javascript
{
  id: number,
  memberId: number,
  memberName: string,
  memberEmail: string,
  cardType: string,
  cardTypeId: number,
  assignedAt: string (ISO),
  validFrom: string (ISO),
  validUntil: string (ISO),
  status: 'active' | 'pending' | 'expired' | 'revoked',
  accessLevel: string
}
```

## Components

### Main Events Page (`index.jsx`)
- **State Management**: Uses React hooks for local state management
- **Filtering**: Client-side filtering by search term, status, and date
- **Data Table**: PrimeReact DataTable for desktop view
- **Mobile List**: Custom mobile-friendly list view
- **Modal Integration**: Manages all modal states and interactions

### CreateEventModal (`components/CreateEventModal.jsx`)
- **Form Validation**: Comprehensive form validation with error handling
- **Date/Time Logic**: Validates date/time relationships
- **Responsive Design**: Mobile-optimized form layout
- **Edit Mode**: Supports both create and edit operations

### EventDetailsModal (`components/EventDetailsModal.jsx`)
- **Event Overview**: Comprehensive event information display
- **Card Assignments**: Shows all temporary card assignments
- **Action Integration**: Quick access to edit and assign cards
- **Visual Indicators**: Progress bars and status tags

### AssignCardsModal (`components/AssignCardsModal.jsx`)
- **Member Selection**: Multi-select member assignment
- **Card Type Selection**: Choose from available card types
- **Validity Period**: Set custom validity periods for assignments
- **Search & Filter**: Find members by name, email, or department

## Navigation Integration

The Events page is integrated into the navigation structure:

1. **Menu Item**: Added to `src/constants/menuTabs.jsx` for manager users
2. **Routing**: Configured in `src/routes/CompanyRoutes.jsx` at `/manager/events`
3. **Access Control**: Available only to manager users (`onlyAdmin: false`)
4. **Active State**: Proper navigation highlighting when on Events page

## Mock Data

Mock data is provided in `src/data/mockEventsData.js` including:
- Sample events with various statuses
- Mock members for card assignment
- Card types with permissions
- Status configurations

## Mobile Responsiveness

### Mobile Features
- **Touch-Friendly**: Large touch targets and swipe gestures
- **Responsive Layout**: Adapts to different screen sizes
- **Mobile Modals**: Full-screen modals on mobile devices
- **Action Menus**: Dropdown action menus for mobile interactions
- **Optimized Forms**: Single-column form layouts on mobile

### CSS Classes
- `.mobile-events-modal`: Mobile modal adjustments
- `.mobile-action-menu`: Touch-friendly action menus
- `.mobile-events-list`: Mobile list view styles
- `.mobile-event-card`: Individual event card styling

## Accessibility Features

- **Keyboard Navigation**: Full keyboard support for all interactions
- **Screen Reader Support**: Proper ARIA labels and semantic HTML
- **High Contrast**: Support for high contrast mode
- **Reduced Motion**: Respects user's motion preferences
- **Focus Management**: Proper focus handling in modals

## Future Enhancements

### Backend Integration
When backend APIs become available, the following endpoints will be needed:

```javascript
// Events API
GET    /api/events              // List events with filtering
POST   /api/events              // Create new event
GET    /api/events/:id          // Get event details
PUT    /api/events/:id          // Update event
DELETE /api/events/:id          // Delete event

// Card Assignments API
GET    /api/events/:id/cards    // Get event card assignments
POST   /api/events/:id/cards    // Assign cards to event
DELETE /api/cards/:id           // Remove card assignment

// Members API
GET    /api/members             // List available members
GET    /api/card-types          // List available card types
```

### Additional Features
- **Event Templates**: Save and reuse event configurations
- **Bulk Operations**: Bulk assign/remove cards
- **Event Analytics**: Attendance tracking and reporting
- **Notifications**: Email/SMS notifications for event updates
- **Calendar Integration**: Export to calendar applications
- **QR Code Generation**: Generate QR codes for temporary cards

## Testing

### Manual Testing Checklist
- [ ] Create new event with all required fields
- [ ] Edit existing event and verify changes
- [ ] Delete event with confirmation
- [ ] Search and filter events
- [ ] Assign temporary cards to event
- [ ] View event details with card assignments
- [ ] Test mobile responsiveness
- [ ] Verify accessibility features
- [ ] Test error handling and validation

### Browser Compatibility
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Performance Considerations

- **Client-side Filtering**: Fast filtering without API calls
- **Memoized Components**: Prevents unnecessary re-renders
- **Lazy Loading**: Components loaded only when needed
- **Optimized Images**: Proper image optimization for avatars
- **Bundle Size**: Minimal impact on overall bundle size

## Maintenance

### Code Quality
- **ESLint**: Follows project ESLint configuration
- **TypeScript Ready**: Can be easily converted to TypeScript
- **Component Reusability**: Modular component design
- **Consistent Patterns**: Follows established codebase patterns

### Documentation
- **Inline Comments**: Key functionality documented
- **PropTypes**: Component props documented
- **README**: Comprehensive documentation (this file)
- **Code Examples**: Usage examples provided
