import { useMutation } from 'react-query';
import axiosInstance from '../config/Axios';

import { useGlobalContext } from '@contexts/GlobalContext';


//-------------------------------------------------Get DataTable for Managers-------------------------------------------------
const getManagersDataTable = async (payload) => {
    let ordered_col = payload.sortField ? payload.sortField : 'created_at';
    let url = `/datatable/managers?page=${payload.page + 1}&per_page=${payload.rows}`;

    if (url.includes("datatable")) {
        url = `${url}&render_html=0`;

        if (payload?.designID) {
            url = `${url}&design_id=${payload?.designID}`;
        }
        if (payload?.groupID) {
            url = `${url}&group_id=${payload?.groupID}`;
        }
    }

    const filteredFilters = Object.fromEntries(
        Object.entries(payload.filters).filter(([key, { value }]) => value !== '')
    );

    console.log("🔍 Filtered filters:", filteredFilters);

    Object.keys(filteredFilters).forEach(key => {
        url = `${url}&${key}=${filteredFilters[key].value}`;
        console.log("🔍 Adding filter to URL:", key, "=", filteredFilters[key].value);
    });

    console.log("🔍 Final API URL:", url);

    if (payload.filters_date) {
        url = `${url}&filters_date=${payload.filters_date}`;
    }

    const orderBy = payload.sortOrder === 1 ? "desc" : 'asc';
    url = `${url}&order_by[${ordered_col}]=${orderBy}`;

    try {
        const { data } = await axiosInstance.get(url, {
            timeout: 30000, // timeout 30 ثانية
        });
        console.log("📌 API Response:", data);
        console.log("📌 API Response data array:", data.data);
        console.log("📌 API Response pagination:", data.pagination);

        return data;
    } catch (error) {
        console.error("❌ Error in getManagersDataTable:", error);
        throw error;
    }
}


export const useGetManagersDataTable = () => {
    const { showToast } = useGlobalContext();

    return useMutation(getManagersDataTable, {
        onError: (error) => {
            console.error("❌ Managers data table error:", error);
            showToast("error", "Error ", error.response?.data?.message || "Failed to load managers data");
        },
        retry: 2, // إعادة المحاولة مرتين
        retryDelay: 1000, // تأخير ثانية واحدة بين المحاولات
    });
}

//-------------------------------------------------Delete Manager Row-------------------------------------------------
const deleteManagerRow = async (payload) => {
    const { data } = await axiosInstance.delete(`/datatable/managers/${payload?.id}/delete`);

    return data.data;
}

export const useDeleteManagerDataTableRow = () => {
    const { showToast } = useGlobalContext();

    return useMutation(deleteManagerRow, {
        onError: (error) => showToast("error", "Error ", error.response?.data?.message)
    });
}
