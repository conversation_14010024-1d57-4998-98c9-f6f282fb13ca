import _ from "lodash";
import { useMutation } from "react-query"
import { useNavigate } from "react-router-dom";

import axiosInstance from '../config/Axios';
// import { emptyLocalStorage, setProfile } from "../config/global";
import { useGlobalContext } from "@contexts/GlobalContext"
import { handleErrors } from "../utils/helper";

// Login

const login = async (payload) => {
    const { data } = await axiosInstance.post("/login", payload);

    return data;
}

export const useLogInMutation = (options = {}) => {
    const { showToast, userType, setUserType } = useGlobalContext();
    const navigate = useNavigate();

    return useMutation(login, {
        onSuccess: async (data) => {
            console.log('🔥 useLogInMutation onSuccess - skipStorageAndRedirect:', options.skipStorageAndRedirect);
            console.log('🔥 useLogInMutation onSuccess - full data:', data);
            console.log('🔥 useLogInMutation onSuccess - user data:', data?.user);
            console.log('🔥 useLogInMutation onSuccess - RAW user JSON:', JSON.stringify(data?.user, null, 2));
            
            console.log('🔍 useLogInMutation - Checking all user fields:', {
                role: data?.user?.role,
                user_role: data?.user?.user_role,
                type: data?.user?.type,
                user_type: data?.user?.user_type,
                position: data?.user?.position,
                level: data?.user?.level,
                account_type: data?.user?.account_type
            });
            
            const type = data?.user?.user_type || "company"
            console.log('🔥 useLogInMutation onSuccess - user type determined:', type);
            
            // Only store data and navigate if not disabled
            if (!options.skipStorageAndRedirect) {
                console.log('🚀 useLogInMutation - Storing data and navigating');
                localStorage.setItem("email", data?.user?.email);
                localStorage.setItem("token", data?.token);
                localStorage.setItem("user_id", data?.user?.id);
                localStorage.setItem("user_name", data?.user?.name);
                
                // ONLY store user_type (ignore role field)
                localStorage.setItem("user_type", type);
                
                setUserType(type);
                
                // Note: AuthContext is updated in Login.jsx component
                // This prevents circular dependency issues
                
                // Use redirect logic based on user_type ONLY
                console.log('🚀 useLogInMutation redirect based on user_type:', type);
                if (type === 'admin') {
                    console.log('🎯 useLogInMutation: Admin detected - redirecting to admin dashboard');
                    navigate("/admin/dashboard");
                } else if (type === 'manager') {
                    console.log('🎯 useLogInMutation: Manager detected - redirecting to manager dashboard');
                    navigate("/manager/dashboard");
                } else {
                    console.log('🎯 useLogInMutation: Unknown type - redirecting to default');
                    navigate("/users/members"); // Default for regular users
                }
            } else {
                console.log('🚫 useLogInMutation - Skipping storage and navigation');
            }
            
            // Call custom onSuccess if provided
            if (options.onSuccess) {
                console.log('🎯 useLogInMutation - Calling custom onSuccess');
                options.onSuccess(data);
            } else {
                console.log('❌ useLogInMutation - No custom onSuccess provided');
            }
        },
        onError: () => showToast("error", "Login Credential", "Invalid Username Or Password!")
    })
}

// Registration
const register = async (payload) => {
    const { data } = await axiosInstance.post("/register", payload);

    return data;
}

export const useRegisterMutation = () => {
    const { showToast } = useGlobalContext();
    const navigate = useNavigate();

    return useMutation(register, {
        onSuccess: async (data) => {
            // localStorage.setItem("email", data?.user?.email);
            // localStorage.setItem("user_name", data?.user?.name)
            // localStorage.setItem("user_type", type);
            // localStorage.setItem("token", data?.token);
            // localStorage.setItem("user_id", data?.user?.id);
            navigate("/login")
        },
        onError: (error) => {
            handleErrors(showToast, error)
        }
    })
}

// Verify Email
const verifyEmail = async (payload) => {
    const { data } = await axiosInstance.post("/verify-email", payload);
    return data;
}

export const useVerifyEmailMutation = () => {
    const { showToast } = useGlobalContext();
    const navigate = useNavigate();

    return useMutation(verifyEmail, {
        onSuccess: async (data) => {
            showToast('success', 'Email Verification', "Email has been verified successfully!");
            navigate("/login")
        },
        onError: (error) => {
            showToast("error", "Email Verification", error?.response?.data?.message || "Failed to verify email")
        }
    })
}

// Resend Verification Email
const resendVerificationEmail = async (payload) => {
    const { data } = await axiosInstance.post("/resend-verification-email", payload);
    return data;
}

export const useResendVerificationEmailMutation = () => {
    const { showToast } = useGlobalContext();

    return useMutation(resendVerificationEmail, {
        onSuccess: async (data) => {
            showToast("success", "Email Verification", data?.message || "Verification code sent successfully");
        },
        onError: (error) => {
            showToast("error", "Email Verification", error?.response?.data?.message || "Failed to send verification code")
        }
    })
}

// Forget Password
const forgetPassword = async (payload) => {
    const { data } = await axiosInstance.post("/users/forget-password", payload);

    return data.data;
}

export const useForgetPasswordMutation = () => {
    const { showToast } = useGlobalContext();

    return useMutation(forgetPassword, {
        onSuccess: async (data) => {
            showToast('success', 'Forget Password', data?.msg);

        },
        onError: (error) => {
            showToast('error', 'Forget Password', error?.response?.data?.message);
        }
    })
}

// Reset Password
const resetPassword = async (payload) => {
    const { data } = await axiosInstance.post(`/users/reset-password/${payload?.token}`, payload.data)

    return data.data;
}

export const useResetPasswordMutation = () => {
    const { showToast } = useGlobalContext();
    const navigate = useNavigate();

    return useMutation(resetPassword, {
        onSuccess: async (data) => {
            showToast('success', 'Reset Password', data?.msg);
            navigate("/login");
        },
        onError: (error) => {
            showToast('error', 'Reset Password', error.response?.data?.message);
        }
    })
}

// Send OTP for Password Reset
const sendOtpForPasswordReset = async (payload) => {
    const { data } = await axiosInstance.post("/users/send-otp-for-password-reset", payload);
    return data;
}

export const useSendOtpForPasswordResetMutation = () => {
    const { showToast } = useGlobalContext();

    return useMutation(sendOtpForPasswordReset, {
        onSuccess: async (data) => {
            showToast('success', 'OTP Sent', data?.message);
        },
        onError: (error) => {
            showToast('error', 'OTP Error', error?.response?.data?.message || 'Failed to send OTP');
        }
    })
}

// Verify OTP for Password Reset
const verifyOtpForPasswordReset = async (payload) => {
    const { data } = await axiosInstance.post("/users/verify-otp-for-password-reset", payload);
    return data;
}

export const useVerifyOtpForPasswordResetMutation = () => {
    const { showToast } = useGlobalContext();

    return useMutation(verifyOtpForPasswordReset, {
        onSuccess: async (data) => {
            showToast('success', 'OTP Verified', data?.message);
        },
        onError: (error) => {
            showToast('error', 'OTP Verification Failed', error?.response?.data?.message || 'Failed to verify OTP');
        }
    })
}

// Reset Password with Token
const resetPasswordWithToken = async (payload) => {
    const { data } = await axiosInstance.post(`/users/reset-password-with-token/${payload.token}`, {
        password: payload.password,
        password_confirmation: payload.password_confirmation
    });
    return data;
}

export const useResetPasswordWithTokenMutation = () => {
    const { showToast } = useGlobalContext();
    const navigate = useNavigate();

    return useMutation(resetPasswordWithToken, {
        onSuccess: async (data) => {
            showToast('success', 'Password Reset', data?.message);
            navigate("/login");
        },
        onError: (error) => {
            showToast('error', 'Password Reset Failed', error?.response?.data?.message || 'Failed to reset password');
        }
    })
}

// Send Email Verification for Profile Update
const sendEmailVerificationForProfile = async (payload) => {
    const { data } = await axiosInstance.post("/users/send-email-verification-for-profile", payload);
    return data;
}

export const useSendEmailVerificationForProfileMutation = () => {
    const { showToast } = useGlobalContext();

    return useMutation(sendEmailVerificationForProfile, {
        onSuccess: async (data) => {
            showToast('success', 'Verification Email Sent', data?.message || 'Verification email sent successfully');
        },
        onError: (error) => {
            const errorMessage = error?.response?.data?.message || 'Failed to send verification email';
            showToast('error', 'Email Verification Failed', errorMessage);
            
            // Log detailed error for debugging
            console.error('Send email verification error details:', {
                message: error?.response?.data?.message,
                details: error?.response?.data?.details,
                errors: error?.response?.data?.errors,
                status: error?.response?.status
            });
            
            // Show additional error details if available
            if (error?.response?.data?.details) {
                Object.keys(error.response.data.details).forEach(field => {
                    const fieldErrors = error.response.data.details[field];
                    if (Array.isArray(fieldErrors) && fieldErrors.length > 0) {
                        showToast('error', `${field} Error`, fieldErrors[0]);
                    }
                });
            }
        }
    })
}

// Verify Email for Profile Update
const verifyEmailForProfile = async (payload) => {
    const { data } = await axiosInstance.post("/users/verify-email-for-profile", payload);
    return data;
}

export const useVerifyEmailForProfileMutation = () => {
    const { showToast } = useGlobalContext();

    return useMutation(verifyEmailForProfile, {
        onSuccess: async (data) => {
            showToast('success', 'Email Verified', data?.message || 'Email verified successfully');
        },
        onError: (error) => {
            const errorMessage = error?.response?.data?.message || 'Failed to verify email';
            showToast('error', 'Email Verification Failed', errorMessage);
            
            // Log detailed error for debugging
            console.error('Verify email error details:', {
                message: error?.response?.data?.message,
                details: error?.response?.data?.details,
                errors: error?.response?.data?.errors,
                status: error?.response?.status
            });
            
            // Show additional error details if available
            if (error?.response?.data?.details) {
                Object.keys(error.response.data.details).forEach(field => {
                    const fieldErrors = error.response.data.details[field];
                    if (Array.isArray(fieldErrors) && fieldErrors.length > 0) {
                        showToast('error', `${field} Error`, fieldErrors[0]);
                    }
                });
            }
        }
    })
}

// Resend Email Verification for Profile Update
const resendEmailVerificationForProfile = async (payload) => {
    const { data } = await axiosInstance.post("/users/resend-email-verification-for-profile", payload);
    return data;
}

export const useResendEmailVerificationForProfileMutation = () => {
    const { showToast } = useGlobalContext();

    return useMutation(resendEmailVerificationForProfile, {
        onSuccess: async (data) => {
            showToast('success', 'Verification Email Resent', data?.message || 'Verification email resent successfully');
        },
        onError: (error) => {
            const errorMessage = error?.response?.data?.message || 'Failed to resend verification email';
            showToast('error', 'Resend Failed', errorMessage);
            
            // Log detailed error for debugging
            console.error('Resend email verification error details:', {
                message: error?.response?.data?.message,
                details: error?.response?.data?.details,
                errors: error?.response?.data?.errors,
                status: error?.response?.status
            });
            
            // Show additional error details if available
            if (error?.response?.data?.details) {
                Object.keys(error.response.data.details).forEach(field => {
                    const fieldErrors = error.response.data.details[field];
                    if (Array.isArray(fieldErrors) && fieldErrors.length > 0) {
                        showToast('error', `${field} Error`, fieldErrors[0]);
                    }
                });
            }
        }
    })
}

// Logout
const logout = async () => {
    const { data } = await axiosInstance.post(`/logout`)

    return data.data;
}

export const useLogoutMutation = () => {
    const { showToast, setUserType } = useGlobalContext();
    const navigate = useNavigate();

    return useMutation(logout, {
        onSuccess: async (data) => {
            // Clear all localStorage items
            localStorage.clear();
            // Use window.location for a full page reload to ensure clean state thus no white page . Yaay
                window.location.href = "/login";
            showToast('success', 'Logout', data?.message);
        },
        onError: (error) => {
            // Even on error, try to logout the user
            localStorage.clear();
            setTimeout(() => {
                window.location.href = "/login";
            }, 100);

            showToast('error', 'Logout', error.response?.data?.message || 'Logout failed');
        }
    })
}