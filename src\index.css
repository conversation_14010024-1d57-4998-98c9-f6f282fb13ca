/* Location Picker Styles */
@import './assets/css/location-picker.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --main_color: #00c3ac;
  --main_color_hover: #02aa96;
  --gray: #dcdcdc;
}
body * {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.main-color {
  background: var(--main_color);
}

.main-btn {
  color: white;
  border-radius: 6px;
  padding: 6px 10px;
  background-color: var(--main_color);
  border-color: var(--main_color);
  font-weight: bold;
  transition: all 0.5s;
}

.main-btn:hover {
  background-color: var(--main_color_hover);
}

.gray-btn {
  color: rgb(93, 93, 93);
  font-weight: bold;
  border-radius: 6px;
  padding: 6px 10px;
  background-color: var(--gray);
  border-color: var(--gray);
}

.auth-input {
  padding: 23px 30px;
  border-radius: 6px;
  border: 1px solid #8692a6;
  background-color: white !important;
}

.pass-input input {
  width: 100%;
}

.side-image-container {
  background-image: url("./assets/images/auth.jfif");
  background-position: center;
  background-size: cover;
}

.active_tab {
  font-weight: bold;
  background-color: #7de1d55e;
  border-radius: 10px;
}

.filter-field {
  border-right: 2px solid #d7d7d7;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.active-bh {
  background-color: #7979795e;
}

/* Remove border from the TabMenu container */
.p-tabmenu.types-tab-menu {
  border: none;
}

/* Remove borders from individual tabs */
.p-tabmenu.types-tab-menu .p-tabmenu-nav {
  border: none;
}

/* Remove borders from tab items */
.p-tabmenu.types-tab-menu .p-tabmenuitem {
  border: none;
  box-shadow: none; /* Remove any shadows if present */
}

.p-tabmenu.types-tab-menu .p-tabmenuitem {
  border-color: var(--main_color_hover) !important;
}

/* Style the active tab */
.p-tabmenu.types-tab-menu .p-highlight * {
  color: var(--main_color);
}

.p-tabmenu.types-tab-menu:hover * {
  color: var(--main_color_hover);
}
/* Remove hover border effect */
.p-tabmenu.types-tab-menu .p-tabmenuitem:hover {
  border: none;
  background-color: #f0f0f0;
}

.p-selectbutton .p-button.p-highlight {
  background: var(--main_color_hover);
  border-color:var(--main_color_hover);
  color: #ffffff;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.3;
  }
}


.sticky-header {
  position: sticky;
  top: 0;
  background: white;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.table-responsive {
  overflow-y: auto;
  height: calc(100vh - 300px);
}

body {
  overflow-x: hidden;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.sticky-header {
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 50;
}

.sticky-header thead th {
  position: sticky;
  top: 0;
  background: #fff;
  z-index: 10;
  box-shadow: 0 2px 2px -1px rgba(0,0,0,0.1);
}

/* Responsive DataTable Styles */
.p-datatable-responsive .p-datatable-tbody > tr > td .p-column-title {
  display: none;
}

@media screen and (max-width: 768px) {
  .p-datatable.p-datatable-responsive .p-datatable-thead > tr > th,
  .p-datatable.p-datatable-responsive .p-datatable-tfoot > tr > td {
    display: none !important;
  }

  .p-datatable.p-datatable-responsive .p-datatable-tbody > tr > td {
    text-align: left;
    display: block;
    width: 100%;
    float: left;
    clear: left;
    border: 0 none;
  }

  .p-datatable.p-datatable-responsive .p-datatable-tbody > tr > td .p-column-title {
    padding: 0.4rem;
    min-width: 30%;
    display: inline-block;
    margin: -0.4em 1em -0.4em -0.4rem;
    font-weight: bold;
  }

  .p-datatable.p-datatable-responsive .p-datatable-tbody > tr > td:last-child {
    border-bottom: 1px solid var(--surface-d);
  }
}

/* Responsive utilities */
.responsive-container {
  width: 100%;
  padding-right: 1rem;
  padding-left: 1rem;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 640px) {
  .responsive-container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .responsive-container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .responsive-container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .responsive-container {
    max-width: 1280px;
  }
}



.professional-library-section .image-container {
  gap: 8px;
  padding: 8px; 
}


.professional-library-section .image-item:hover {
  opacity: 0.7; 
  transition: opacity 0.2s ease;
}

/* تحسين react-phone-input-2 */
.react-tel-input .flag-dropdown {
  z-index: 2;
  border-radius: 1rem 0 0 1rem !important;
  border-right: none !important;
  box-shadow: none !important;
}
.react-tel-input .form-control {
  border-left: 2px solid #d1d5db !important;
  border-radius: 0 1rem 1rem 0 !important;
  padding-left: 80px !important;
  position: relative;
}
.react-tel-input .form-control::before {
  content: '';
  position: absolute;
  left: 60px;
  top: 50%;
  transform: translateY(-50%);
  height: 28px;
  width: 2px;
  background: #d1d5db;
  border-radius: 2px;
  z-index: 2;
  display: block;
}
.react-tel-input .country-list {
  border-radius: 1rem !important;
  box-shadow: 0 8px 32px rgba(66,123,240,0.18) !important;
  border: 2px solid #d1d5db !important;
  background: #fff !important;
  font-size: 1.08rem !important;
  padding: 8px 0 !important;
  max-height: 350px !important;
  overflow-y: auto !important;
  z-index: 50 !important;
}
.react-tel-input .country-list .search {
  margin: 8px 16px !important;
  padding: 8px 12px !important;
  border-radius: 0.75rem !important;
  border: 1.5px solid #d1d5db !important;
  font-size: 1rem !important;
  background: #f9fafb !important;
  color: #222 !important;
  box-shadow: none !important;
  width: calc(100% - 32px) !important;
  display: block !important;
}
.react-tel-input .country-list .country {
  padding: 10px 18px !important;
  border-radius: 8px !important;
  margin: 2px 8px !important;
  display: flex;
  align-items: center;
  transition: background 0.18s;
  font-weight: 500;
  font-size: 1.08rem;
}
.react-tel-input .country-list .country:hover, .react-tel-input .country-list .country.highlight {
  background: #f0f6ff !important;
  color: #427bf0 !important;
}
.react-tel-input .country-list .country .country-flag {
  width: 28px !important;
  height: 20px !important;
  border-radius: 4px !important;
  box-shadow: 0 1px 4px rgba(66,123,240,0.10);
  margin-right: 14px !important;
  object-fit: cover;
  border: 1px solid #e5e7eb;
  background: #fff;
  display: inline-block;
}
.react-tel-input .country-list .country .country-name {
  font-size: 1.08rem !important;
  font-weight: 500;
  color: #2d3748;
}
.react-tel-input .country-list .country .dial-code {
  color: #6b7280;
  font-size: 1rem;
  margin-left: auto;
  font-weight: 400;
}




.text-assistant {
  pointer-events: auto !important;
}

.text-assistant * {
  pointer-events: auto !important;
}

.text-assistant .p-button {
  pointer-events: auto !important;
}

.text-assistant .p-dropdown {
  pointer-events: auto !important;
}

.text-assistant .p-slider {
  pointer-events: auto !important;
}

.text-assistant input[type="color"] {
  pointer-events: auto !important;
}

.text-assistant .p-inputtext {
  pointer-events: auto !important;
}

/* تحسين مظهر الأزرار */
.text-assistant .p-button.p-button-primary {
  background-color: #4338ca !important;
  border-color: #4338ca !important;
  color: white !important;
}

.text-assistant .p-button.p-button-primary:hover {
  background-color: #3730a3 !important;
  border-color: #3730a3 !important;
}

.text-assistant .p-button.p-button-outlined {
  background-color: transparent !important;
}

.text-assistant .p-button.p-button-outlined:hover {
  background-color: rgba(67, 56, 202, 0.1) !important;
}

/* تحسين مظهر الأزرار المعطلة */
.text-assistant .p-button:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
}

/* تحسين مظهر الألوان */
.text-assistant input[type="color"] {
  border: 2px solid #e5e7eb !important;
  border-radius: 4px !important;
  cursor: pointer !important;
}

.text-assistant input[type="color"]:hover {
  border-color: #d1d5db !important;
}

/* تحسين مظهر القوائم المنسدلة */
.text-assistant .p-dropdown {
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
}

.text-assistant .p-dropdown:hover {
  border-color: #9ca3af !important;
}

/* تحسين مظهر حقول الإدخال */
.text-assistant .p-inputtext {
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
}

.text-assistant .p-inputtext:focus {
  border-color: #4338ca !important;
  box-shadow: 0 0 0 3px rgba(67, 56, 202, 0.1) !important;
}

/* تحسين مظهر الشرائح */
.text-assistant .p-slider {
  margin: 0.5rem 0 !important;
}

.text-assistant .p-slider .p-slider-handle {
  background-color: #4338ca !important;
  border: 2px solid white !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.text-assistant .p-slider .p-slider-range {
  background-color: #4338ca !important;
}

/* تحسين مظهر منطقة المعاينة */
.text-assistant .preview-area {
  border: 2px solid #e5e7eb !important;
  border-radius: 8px !important;
  background-color: white !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
}

/* تحسين مظهر التعليمات */
.text-assistant .instructions-panel {
  background-color: #eff6ff !important;
  border: 1px solid #bfdbfe !important;
  border-radius: 8px !important;
}

/* تحسين مظهر العناوين */
.text-assistant h4 {
  color: #374151 !important;
  font-weight: 600 !important;
  margin-bottom: 0.5rem !important;
}

/* تحسين مظهر التسميات */
.text-assistant label {
  color: #4b5563 !important;
  font-weight: 500 !important;
  margin-bottom: 0.25rem !important;
}

/* تحسين مظهر الشبكة */
.text-assistant .grid {
  gap: 0.75rem !important;
}

/* تحسين مظهر الأزرار في الشبكة */
.text-assistant .grid .p-button {
  font-size: 0.875rem !important;
  padding: 0.5rem 0.75rem !important;
}

/* تحسين مظهر الأزرار المحددة */
.text-assistant .p-button.p-button-outlined.p-button-success {
  color: #10b981 !important;
  border-color: #10b981 !important;
  background-color: transparent !important;
}

.text-assistant .p-button.p-button-outlined.p-button-success:hover {
  background-color: #10b981 !important;
  color: white !important;
}

.text-assistant .p-button.p-button-outlined.p-button-success:disabled {
  color: #9ca3af !important;
  border-color: #9ca3af !important;
  background-color: transparent !important;
}

/* ضمان عمل الخطوط العربية */
@font-face {
  font-family: 'Cairo';
  src: url('https://fonts.gstatic.com/s/cairo/v20/SLXVc1nY6HkvangtZmpcWmhzfH5lWWgcQyyS4J0.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Cairo';
  src: url('https://fonts.gstatic.com/s/cairo/v20/SLXVc1nY6HkvangtZmpcWmhzfH5lWWgcQyyS4J0.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Tajawal';
  src: url('https://fonts.gstatic.com/s/tajawal/v8/Iura6YBj_oCad4k1r_5qA.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Tajawal';
  src: url('https://fonts.gstatic.com/s/tajawal/v8/Iura6YBj_oCad4k1r_5qA.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* ضمان عمل الخطوط الإنجليزية */
@font-face {
  font-family: 'Roboto';
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4mxK.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Playfair Display';
  src: url('https://fonts.gstatic.com/s/playfairdisplay/v30/nuFvD-vYSZviVYUb_rj3ij__anPXJzDwcbmjWBN2PKdFvXDXbtXK-F2qC0s.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Pacifico';
  src: url('https://fonts.gstatic.com/s/pacifico/v22/FwZY7-Qmy14u9lezJ-6H6MmBp0u-.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Bebas Neue';
  src: url('https://fonts.gstatic.com/s/bebasneue/v9/JTUSjIg69CK48gW7PXoo9WlhI.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Dancing Script';
  src: url('https://fonts.gstatic.com/s/dancingscript/v24/If2cXTr6YS-zF4S-kcSWSVi_sxjsohD9F50Ruu7BMSo3ROp6.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Orbitron';
  src: url('https://fonts.gstatic.com/s/orbitron/v25/yMJMMIlzdpvBhQQL_SC3X9yhF25-T1nyGy6BoWgz.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Lobster';
  src: url('https://fonts.gstatic.com/s/lobster/v28/neILzCirqoswsqX9zoKmNg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Abril Fatface';
  src: url('https://fonts.gstatic.com/s/abrilfatface/v12/zOL64pLDpnLkHmFHMKNTQ4g5bs3t6GqJKxeA.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Righteous';
  src: url('https://fonts.gstatic.com/s/righteous/v9/1cXxaUPXBpj2rGoU7C9WiHGA.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Permanent Marker';
  src: url('https://fonts.gstatic.com/s/permanentmarker/v10/Fh4uPib6I9yqygr9j2ePTWi4QKqyC6.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Fredoka One';
  src: url('https://fonts.gstatic.com/s/fredokaone/v8/k3kUo8kEI-tA1RRcTZGmTlHGCaI.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Bangers';
  src: url('https://fonts.gstatic.com/s/bangers/v12/FeVQS0BTqb0h60ACH55Q2J5hm.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Chewy';
  src: url('https://fonts.gstatic.com/s/chewy/v12/uK_94ruUb-k-wn52KjI.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Kaushan Script';
  src: url('https://fonts.gstatic.com/s/kaushanscript/v14/vm8vdRfvXFLG3OLnsO15WYS5DG74wNc.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Satisfy';
  src: url('https://fonts.gstatic.com/s/satisfy/v12/rP2Hp2yn6lkG50LoCZOIHQ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Great Vibes';
  src: url('https://fonts.gstatic.com/s/greatvibes/v12/RWmMoLWRv4ITMsfS8c0tPvQ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Cinzel';
  src: url('https://fonts.gstatic.com/s/cinzel/v16/8vIU7ww63mVu7gtR-kwKxNvkNOjw-tbnTYrvTO5c4A.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'UnifrakturMaguntia';
  src: url('https://fonts.gstatic.com/s/unifrakturmaguntia/v12/WWXPlieVYwiGNomYU-ciRLRvEmK7oaVem2ZI.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Creepster';
  src: url('https://fonts.gstatic.com/s/creepster/v12/AlZy_zVUqJz4yMrniH4Rcn35.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Faster One';
  src: url('https://fonts.gstatic.com/s/fasterone/v12/H4ciBXCHmdfClFd-vWhxXPX5.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Press Start 2P';
  src: url('https://fonts.gstatic.com/s/pressstart2p/v14/e3t4euO8T-267oIAQAu6jDQyK3nVivM.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'VT323';
  src: url('https://fonts.gstatic.com/s/vt323/v12/pxiKyp0ihIEF2isfFJU.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Share Tech Mono';
  src: url('https://fonts.gstatic.com/s/sharetechmono/v10/J7aHnp1uDWRBEqV98dVQztYldFcLowEF.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Space Mono';
  src: url('https://fonts.gstatic.com/s/spacemono/v10/i7dPIFZifjKcF5UAWdDRYEF8RQ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Major Mono Display';
  src: url('https://fonts.gstatic.com/s/majormonodisplay/v6/RWmVoLyb5fEqtsfBX9PDZIGr2tFubRhLCn2QIndPww.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Crimson Text';
  src: url('https://fonts.gstatic.com/s/crimsontext/v19/wlp2gwHKFkZgtmSR3NB0oRJfbwhT.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Libre Baskerville';
  src: url('https://fonts.gstatic.com/s/librebaskerville/v14/kmKnZrc3Hgbbcjq75U4uslyuy4kn0qNZaxY.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Lora';
  src: url('https://fonts.gstatic.com/s/lora/v26/0QI6MX1D_JOuGQbT0gvTJPa787weuyJGmKM.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Source Sans Pro';
  src: url('https://fonts.gstatic.com/s/sourcesanspro/v21/6xK3dSBYKcSV-LCoeQqfX1RYOo3qOK7l.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('https://fonts.gstatic.com/s/nunito/v24/XRXI3I6Li01BKofiOc5wtlZ2di8HDLshdTQ3jw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Quicksand';
  src: url('https://fonts.gstatic.com/s/quicksand/v29/6xK-dSZaM9iE8KbpRA_LJ3z8mH9BOJvgkP8o58a-xw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Comfortaa';
  src: url('https://fonts.gstatic.com/s/comfortaa/v37/1Pt_g8LJRfWJmhDAuUsSQamb1W0lwk4S4TbMDrMfJQ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Varela Round';
  src: url('https://fonts.gstatic.com/s/varelaround/v13/w8gdH283Tvk__Lua32TysjIfp8uPLdshZg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Maven Pro';
  src: url('https://fonts.gstatic.com/s/mavenpro/v32/7Auup_AqnyWWAxW2Wk3swUz56MS91Eww8SX25nCpkp4.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Exo 2';
  src: url('https://fonts.gstatic.com/s/exo2/v20/7cH1v4okm5zmbvwkAx_sfcEuiD8jWfWcPg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Rajdhani';
  src: url('https://fonts.gstatic.com/s/rajdhani/v15/LDI2apCSOBg7S-QT7paQc6M.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Titillium Web';
  src: url('https://fonts.gstatic.com/s/titilliumweb/v15/NaPecZTIAOhVxoMyOr9n_E7fdMPmCA.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Josefin Sans';
  src: url('https://fonts.gstatic.com/s/josefinsans/v25/Qw3PZQNVED7rKGKxtqIqX5E-AVSJrOCfjY46_DjQbMZhKg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Abel';
  src: url('https://fonts.gstatic.com/s/abel/v18/MwQ5bhbm2POE6VhLPw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Anton';
  src: url('https://fonts.gstatic.com/s/anton/v23/1Ptgg87LROyAm3Kz-Co.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Bungee';
  src: url('https://fonts.gstatic.com/s/bungee/v6/N0bU2SZBIuF2PU_0AnR1Gd8.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Black Ops One';
  src: url('https://fonts.gstatic.com/s/blackopsone/v12/qWcsB6-ypo7xBdr6Xshe96H3aDbbtwkh.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Russo One';
  src: url('https://fonts.gstatic.com/s/russoone/v14/Z9XUDmZRWg6M1LvRYsHOz8mJ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Audiowide';
  src: url('https://fonts.gstatic.com/s/audiowide/v8/l7gdbjpo0cum0ckerWCtkQ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Changa One';
  src: url('https://fonts.gstatic.com/s/changaone/v13/xfu00W3wXn3QLUJXhzq46AbouLfbK64.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Sniglet';
  src: url('https://fonts.gstatic.com/s/sniglet/v13/cIf9MaFLtkE2UupGgQYhiA.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Boogaloo';
  src: url('https://fonts.gstatic.com/s/boogaloo/v12/kmK-Zq45GAvOdnaW6x1F_SrQo.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Bubblegum Sans';
  src: url('https://fonts.gstatic.com/s/bubblegumsans/v12/AYCSpXb_Z9EORv1M5QTjEzMEtdaHzoPPbqR4.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Cherry Cream Soda';
  src: url('https://fonts.gstatic.com/s/cherrycreamsoda/v12/UMBIrOxBrW6w2FFyi9paG0fdVdRciQd6A4Q.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Freckle Face';
  src: url('https://fonts.gstatic.com/s/freckleface/v9/AMOWz4SXrmKHCvXTohxY-YI0Uw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gloria Hallelujah';
  src: url('https://fonts.gstatic.com/s/gloriahallelujah/v12/LYjYdHv3pUkNBMypJh7elzXKCM41xtdECq76mk.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Indie Flower';
  src: url('https://fonts.gstatic.com/s/indieflower/v17/m8JVjfNVeKWVnh3QMuKkFcZVaUuC.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Kalam';
  src: url('https://fonts.gstatic.com/s/kalam/v11/YA9Qr0Wd4kDdMtD6GgLLmCUItqGt.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Patrick Hand';
  src: url('https://fonts.gstatic.com/s/patrickhand/v14/Ln1FzOA-y6TkwHrOUc6NnUjKRp9m.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Reenie Beanie';
  src: url('https://fonts.gstatic.com/s/reeniebeanie/v11/z7NSdR76eDkaJKZJFkkjuvWxXPq1qw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Rock Salt';
  src: url('https://fonts.gstatic.com/s/rocksalt/v11/MwQ0bhv11fDH6wL6ZCL4I8Q.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Shadows Into Light';
  src: url('https://fonts.gstatic.com/s/shadowsintolight/v12/UqyNK9UOIntux_czAvDQx_ZcHqZXBNQDcg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Special Elite';
  src: url('https://fonts.gstatic.com/s/specialelite/v11/XLYgIZbkc4JPUL5CVArUVL0ntnAOTQ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Walter Turncoat';
  src: url('https://fonts.gstatic.com/s/walterturncoat/v12/snfys0Gs98ln43n0d-14ULoToe67YB2M.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

/* ضمان عمل الخطوط العربية الجديدة */
@font-face {
  font-family: 'Scheherazade New';
  src: url('https://fonts.gstatic.com/s/scheherazadenew/v4/4UaBrE6tmq0gO-tVs9Ipr5-9-mbRvNUF2I.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Lateef';
  src: url('https://fonts.gstatic.com/s/lateef/v17/hESw6XVnNCxEvkbMpheEZo_H_w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Harmattan';
  src: url('https://fonts.gstatic.com/s/harmattan/v8/gokpH6L2DkFvVvRp9XpTS0CjkP1Yog.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'El Messiri';
  src: url('https://fonts.gstatic.com/s/elmessiri/v12/K2F0fZBRmr9vQ1pHEey6GIGo8_pv3myYjuXCe65ghj3OTw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Aref Ruqaa';
  src: url('https://fonts.gstatic.com/s/arefruqaa/v12/WwkbxPW1E165DjQ5VsZzqN5NjF7Nw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Readex Pro';
  src: url('https://fonts.gstatic.com/s/readexpro/v1/SLXYc1bJ7HE5YDoGPuzj_dh8na74Kiw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'IBM Plex Sans Arabic';
  src: url('https://fonts.gstatic.com/s/ibmplexsansarabic/v9/Qw3CZRtWPQCuHme67tEYUIx3Kh0SHR6x6jOjR9VfPcrKtuJ2Jplg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Arabic';
  src: url('https://fonts.gstatic.com/s/notosansarabic/v18/nwpCt6W9KfF6gV1yPu9T3JqRBNbE8tq1lx20.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Arabic';
  src: url('https://fonts.gstatic.com/s/notoserifarabic/v18/ga6Iaw1J5X9T9RW6j9bNVlsKbJovrb0b8.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Alkalami';
  src: url('https://fonts.gstatic.com/s/alkalami/v1/zOL-4pbPn6Im26Ke4HOxT7Y.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Anek Arabic';
  src: url('https://fonts.gstatic.com/s/anekarabic/v1/5aUz9_-1phKLFgshYDvh6O4hp3wSa0b4.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'IBM Plex Serif Arabic';
  src: url('https://fonts.gstatic.com/s/ibmplexserifarabic/v9/Qw3CZRtWPQCuHme67tEYUIx3Kh0SHR6x6jOjR9VfPcrKtuJ2Jplg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Nastaliq Urdu';
  src: url('https://fonts.gstatic.com/s/notonastaliqurdu/v1/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Rashi Hebrew';
  src: url('https://fonts.gstatic.com/s/notorashihebrew/v1/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Hebrew';
  src: url('https://fonts.gstatic.com/s/notosanshebrew/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Hebrew';
  src: url('https://fonts.gstatic.com/s/notoserifhebrew/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Devanagari';
  src: url('https://fonts.gstatic.com/s/notosansdevanagari/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Devanagari';
  src: url('https://fonts.gstatic.com/s/notoserifdevanagari/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Bengali';
  src: url('https://fonts.gstatic.com/s/notosansbengali/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Bengali';
  src: url('https://fonts.gstatic.com/s/notoserifbengali/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Thai';
  src: url('https://fonts.gstatic.com/s/notosansthai/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Thai';
  src: url('https://fonts.gstatic.com/s/notoserifthai/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Korean';
  src: url('https://fonts.gstatic.com/s/notosanskorean/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Korean';
  src: url('https://fonts.gstatic.com/s/notoserifkorean/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Japanese';
  src: url('https://fonts.gstatic.com/s/notosansjapanese/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Japanese';
  src: url('https://fonts.gstatic.com/s/notoserifjapanese/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

/* تحسين مظهر الشبكة */
.text-assistant .grid {
  gap: 0.75rem !important;
}

/* تحسين مظهر الأزرار في الشبكة */
.text-assistant .grid .p-button {
  font-size: 0.875rem !important;
  padding: 0.5rem 0.75rem !important;
}

/* تحسين مظهر الأزرار المحددة */
.text-assistant .p-button.p-button-outlined.p-button-success {
  color: #10b981 !important;
  border-color: #10b981 !important;
  background-color: transparent !important;
}

.text-assistant .p-button.p-button-outlined.p-button-success:hover {
  background-color: #10b981 !important;
  color: white !important;
}

.text-assistant .p-button.p-button-outlined.p-button-success:disabled {
  color: #9ca3af !important;
  border-color: #9ca3af !important;
  background-color: transparent !important;
}

/* تحسين أداء قائمة الخطوط */
.font-dropdown-panel {
  max-height: 300px !important;
  overflow-y: auto !important;
}

.font-dropdown-panel .p-dropdown-items {
  max-height: 250px !important;
}

.font-dropdown-panel .p-dropdown-item {
  padding: 0.5rem 0.75rem !important;
  font-size: 0.875rem !important;
  border-bottom: 1px solid #f3f4f6 !important;
}

.font-dropdown-panel .p-dropdown-item:hover {
  background-color: #f9fafb !important;
}

.font-dropdown-panel .p-dropdown-item.p-highlight {
  background-color: #3b82f6 !important;
  color: white !important;
}

/* تحسين مظهر البحث في قائمة الخطوط */
.font-dropdown-panel .p-dropdown-filter {
  padding: 0.5rem !important;
  border-bottom: 1px solid #e5e7eb !important;
  margin-bottom: 0.5rem !important;
}

.font-dropdown-panel .p-dropdown-filter-input {
  width: 100% !important;
  padding: 0.5rem !important;
  border: 1px solid #d1d5db !important;
  border-radius: 0.375rem !important;
  font-size: 0.875rem !important;
}

/* تحسين أداء Virtual Scrolling */
.font-dropdown-panel .p-virtualscroller {
  max-height: 200px !important;
}

.font-dropdown-panel .p-virtualscroller-content {
  padding: 0 !important;
}

/* تحسين مظهر مثال الخط */
.font-dropdown-panel .p-dropdown-item span:last-child {
  font-size: 0.75rem !important;
  opacity: 0.7 !important;
  margin-left: 0.5rem !important;
}

/* تحسين أداء التحميل */
.text-assistant .p-dropdown.p-component {
  transition: all 0.2s ease !important;
}

.text-assistant .p-dropdown.p-component:not(.p-disabled):hover {
  border-color: #3b82f6 !important;
}

.text-assistant .p-dropdown.p-component.p-focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}