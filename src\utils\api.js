import axios from 'axios';
import { USER_ROUTES, EMAIL_AVAILABILITY_ROUTE } from '../routes/api-routes';

// Create axios instance with base configuration
const api = axios.create({
    baseURL: import.meta.env.VITE_BACKEND_URL || 'http://192.168.88.79:8000/api',
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Add response interceptor for error handling
api.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response?.status === 401) {
            // Handle unauthorized access
            localStorage.removeItem('token');
            window.location.href = '/login';
        }
        return Promise.reject(error);
    }
);

// User API functions
export const userAPI = {
    // Update user profile
    updateProfile: async (userId, userData) => {
        try {
            const response = await api.put(`${USER_ROUTES.UPDATE}/${userId}`, userData);
            return {
                success: true,
                data: response.data,
                message: response.data.message
            };
        } catch (error) {
            return {
                success: false,
                message: error.response?.data?.message || 'Failed to update profile',
                details: error.response?.data?.details || {},
                error: error.response?.data?.error || error.message
            };
        }
    },

    // Create new user
    createUser: async (userData) => {
        try {
            const response = await api.post(USER_ROUTES.CREATE, userData);
            return {
                success: true,
                data: response.data,
                message: response.data.message
            };
        } catch (error) {
            return {
                success: false,
                message: error.response?.data?.message || 'Failed to create user',
                details: error.response?.data?.details || {},
                error: error.response?.data?.error || error.message
            };
        }
    },

    // Upload user image
    uploadImage: async (userId, imageFile) => {
        try {
            const formData = new FormData();
            formData.append('image', imageFile);

            const response = await api.post(`${USER_ROUTES.UPLOAD_IMAGE}/${userId}`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            return {
                success: true,
                data: response.data,
                message: response.data.message
            };
        } catch (error) {
            return {
                success: false,
                message: error.response?.data?.message || 'Failed to upload image',
                details: error.response?.data?.details || {},
                error: error.response?.data?.error || error.message
            };
        }
    },

    // Get user data
    getUser: async (userId) => {
        try {
            const response = await api.get(`${USER_ROUTES.UPDATE}/${userId}`);
            return {
                success: true,
                data: response.data,
            };
        } catch (error) {
            return {
                success: false,
                message: error.response?.data?.message || 'Failed to get user data',
                error: error.response?.data?.error || error.message
            };
        }
    },

    // Check email availability
    checkEmailAvailability: async (email, userId = null) => {
        try {
            const data = { email };
            if (userId) {
                data.user_id = userId;
            }

            const response = await api.post(EMAIL_AVAILABILITY_ROUTE, data);
            
            return {
                success: true,
                available: response.data.available,
                message: response.data.message
            };
        } catch (error) {
            console.error('Email availability check error:', error);
            
            return {
                success: false,
                available: false,
                message: error.response?.data?.message || 'Failed to check email availability',
                error: error.response?.data?.error || error.message
            };
        }
    }
};

// Email verification API functions
export const emailVerificationAPI = {
    // Send email verification
    sendVerification: async (email, userId) => {
        try {
            const response = await api.post('/users/send-email-verification-for-profile', {
                email,
                user_id: userId
            });
            return {
                success: true,
                data: response.data,
                message: response.data.message
            };
        } catch (error) {
            return {
                success: false,
                message: error.response?.data?.message || 'Failed to send verification email',
                details: error.response?.data?.details || {},
                error: error.response?.data?.error || error.message
            };
        }
    },

    // Verify email
    verifyEmail: async (email, otp, userId) => {
        try {
            const response = await api.post('/users/verify-email-for-profile', {
                email,
                otp,
                user_id: userId
            });
            return {
                success: true,
                data: response.data,
                message: response.data.message
            };
        } catch (error) {
            return {
                success: false,
                message: error.response?.data?.message || 'Failed to verify email',
                details: error.response?.data?.details || {},
                error: error.response?.data?.error || error.message
            };
        }
    },

    // Resend verification email
    resendVerification: async (email, userId) => {
        try {
            const response = await api.post('/users/resend-email-verification-for-profile', {
                email,
                user_id: userId
            });
            return {
                success: true,
                data: response.data,
                message: response.data.message
            };
        } catch (error) {
            return {
                success: false,
                message: error.response?.data?.message || 'Failed to resend verification email',
                details: error.response?.data?.details || {},
                error: error.response?.data?.error || error.message
            };
        }
    }
};

// Utility function to handle API errors
export const handleAPIError = (error) => {
    if (error.response?.data?.details) {
        // Return validation errors
        return {
            success: false,
            message: error.response.data.message || 'Validation failed',
            details: error.response.data.details
        };
    }
    
    return {
        success: false,
        message: error.response?.data?.message || error.message || 'An error occurred',
        error: error.response?.data?.error || error.message
    };
};

export default api;
