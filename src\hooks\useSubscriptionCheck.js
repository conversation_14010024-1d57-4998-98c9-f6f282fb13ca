import { useState, useEffect } from 'react';
import axiosInstance from '../config/Axios';

export default function useSubscriptionCheck() {
  const [subscriptionError, setSubscriptionError] = useState(null);
  const [subscriptionLoading, setSubscriptionLoading] = useState(true);
  const [noPackage, setNoPackage] = useState(false);

  useEffect(() => {
    const checkSubscription = async () => {
      try {
        const token = localStorage.getItem('token');
        const userId = localStorage.getItem('user_id');
        if (!token || !userId) {
          setSubscriptionLoading(false);
          return;
        }
        const response = await axiosInstance.get(`packages/show-package-by-id/${userId}`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        if (!response.data || Object.keys(response.data).length === 0 || response.data.error === 'No package found for this user') {
          setNoPackage(true);
        } else {
          setNoPackage(false);
        }
        setSubscriptionError(null);
      } catch (error) {
        if (error.response && error.response.data) {
          const errMsg = error.response.data.error?.toLowerCase() || '';
          if (error.response.data.error === "Your subscription has expired. Please renew your subscription to continue.") {
            setSubscriptionError({ message: error.response.data.error });
          } else if (
            errMsg.includes('not found') ||
            errMsg.includes('no package') ||
            errMsg.includes('no active package found for this user') ||
            errMsg.includes('must have an active package')
          ) {
            setNoPackage(true);
          } else {
            setSubscriptionError(null);
          }
        } else {
          setSubscriptionError(null);
        }
      } finally {
        setSubscriptionLoading(false);
      }
    };
    checkSubscription();
  }, []);

  return { subscriptionError, subscriptionLoading, noPackage };
} 