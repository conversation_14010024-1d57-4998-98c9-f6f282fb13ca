import React, { useState, useEffect } from 'react';
import { Dialog } from 'primereact/dialog';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { Calendar } from 'primereact/calendar';
import { Dropdown } from 'primereact/dropdown';
import { InputNumber } from 'primereact/inputnumber';
import { useLayout } from '@contexts/LayoutContext';
import { eventStatuses } from '@data/mockEventsData';

const CreateEventModal = ({ visible, onHide, onSave, eventData, isEdit = false }) => {
    const { isMobile } = useLayout();
    
    const [formData, setFormData] = useState({
        name: '',
        description: '',
        startDate: null,
        startTime: '',
        endDate: null,
        endTime: '',
        location: '',
        maxAttendees: 100,
        status: 'draft'
    });
    
    const [errors, setErrors] = useState({});
    const [loading, setLoading] = useState(false);

    // Initialize form data when modal opens or eventData changes
    useEffect(() => {
        if (visible) {
            if (isEdit && eventData) {
                setFormData({
                    name: eventData.name || '',
                    description: eventData.description || '',
                    startDate: eventData.startDate ? new Date(eventData.startDate) : null,
                    startTime: eventData.startTime || '',
                    endDate: eventData.endDate ? new Date(eventData.endDate) : null,
                    endTime: eventData.endTime || '',
                    location: eventData.location || '',
                    maxAttendees: eventData.maxAttendees || 100,
                    status: eventData.status || 'draft'
                });
            } else {
                // Reset form for new event
                setFormData({
                    name: '',
                    description: '',
                    startDate: null,
                    startTime: '',
                    endDate: null,
                    endTime: '',
                    location: '',
                    maxAttendees: 100,
                    status: 'draft'
                });
            }
            setErrors({});
        }
    }, [visible, isEdit, eventData]);

    const validateForm = () => {
        const newErrors = {};

        if (!formData.name.trim()) {
            newErrors.name = 'Event name is required';
        }

        if (!formData.description.trim()) {
            newErrors.description = 'Event description is required';
        }

        if (!formData.startDate) {
            newErrors.startDate = 'Start date is required';
        }

        if (!formData.startTime) {
            newErrors.startTime = 'Start time is required';
        }

        if (!formData.endDate) {
            newErrors.endDate = 'End date is required';
        }

        if (!formData.endTime) {
            newErrors.endTime = 'End time is required';
        }

        if (!formData.location.trim()) {
            newErrors.location = 'Location is required';
        }

        if (!formData.maxAttendees || formData.maxAttendees < 1) {
            newErrors.maxAttendees = 'Maximum attendees must be at least 1';
        }

        // Validate date/time logic
        if (formData.startDate && formData.endDate) {
            const startDateTime = new Date(formData.startDate);
            const endDateTime = new Date(formData.endDate);
            
            if (formData.startTime) {
                const [startHour, startMinute] = formData.startTime.split(':');
                startDateTime.setHours(parseInt(startHour), parseInt(startMinute));
            }
            
            if (formData.endTime) {
                const [endHour, endMinute] = formData.endTime.split(':');
                endDateTime.setHours(parseInt(endHour), parseInt(endMinute));
            }

            if (endDateTime <= startDateTime) {
                newErrors.endDate = 'End date/time must be after start date/time';
            }
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        setLoading(true);
        
        try {
            // Calculate duration
            const startDateTime = new Date(formData.startDate);
            const endDateTime = new Date(formData.endDate);
            const diffMs = endDateTime - startDateTime;
            const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
            const diffHours = Math.ceil(diffMs / (1000 * 60 * 60));
            
            let duration;
            if (diffDays > 1) {
                duration = `${diffDays} days`;
            } else {
                duration = `${diffHours} hours`;
            }

            const eventToSave = {
                ...formData,
                startDate: formData.startDate.toISOString().split('T')[0],
                endDate: formData.endDate.toISOString().split('T')[0],
                duration,
                ...(isEdit && eventData ? { id: eventData.id } : {})
            };

            await onSave(eventToSave);
        } catch (error) {
            console.error('Error saving event:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
        
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: ''
            }));
        }
    };

    const renderFooter = () => (
        <div className="flex justify-end gap-2">
            <Button
                label="Cancel"
                className="gray-btn"
                onClick={onHide}
                disabled={loading}
            />
            <Button
                label={isEdit ? "Update Event" : "Create Event"}
                className="main-btn"
                onClick={handleSubmit}
                loading={loading}
                disabled={loading}
            />
        </div>
    );

    return (
        <Dialog
            header={isEdit ? "Edit Event" : "Create New Event"}
            visible={visible}
            style={isMobile ? { width: "95vw", height: "90vh" } : { width: "60vw", minWidth: '600px' }}
            breakpoints={isMobile ? {} : { '960px': '80vw', '641px': '95vw' }}
            modal
            className="p-fluid"
            onHide={onHide}
            footer={renderFooter()}
            closeOnEscape={!loading}
            maximizable={false}
            resizable={false}
        >
            <form onSubmit={handleSubmit} className="space-y-4">
                {/* Event Name */}
                <div className="field">
                    <label htmlFor="eventName" className="form-label text-sm font-medium">
                        Event Name *
                    </label>
                    <InputText
                        id="eventName"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        placeholder="Enter event name"
                        className={errors.name ? 'p-invalid' : ''}
                    />
                    {errors.name && <small className="p-error">{errors.name}</small>}
                </div>

                {/* Event Description */}
                <div className="field">
                    <label htmlFor="eventDescription" className="form-label text-sm font-medium">
                        Description *
                    </label>
                    <InputTextarea
                        id="eventDescription"
                        value={formData.description}
                        onChange={(e) => handleInputChange('description', e.target.value)}
                        placeholder="Enter event description"
                        rows={3}
                        className={errors.description ? 'p-invalid' : ''}
                    />
                    {errors.description && <small className="p-error">{errors.description}</small>}
                </div>

                {/* Date and Time Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Start Date & Time */}
                    <div className="space-y-4">
                        <div className="field">
                            <label htmlFor="startDate" className="form-label text-sm font-medium">
                                Start Date *
                            </label>
                            <Calendar
                                id="startDate"
                                value={formData.startDate}
                                onChange={(e) => handleInputChange('startDate', e.value)}
                                placeholder="Select start date"
                                showIcon
                                className={errors.startDate ? 'p-invalid' : ''}
                                minDate={new Date()}
                            />
                            {errors.startDate && <small className="p-error">{errors.startDate}</small>}
                        </div>
                        
                        <div className="field">
                            <label htmlFor="startTime" className="form-label text-sm font-medium">
                                Start Time *
                            </label>
                            <InputText
                                id="startTime"
                                type="time"
                                value={formData.startTime}
                                onChange={(e) => handleInputChange('startTime', e.target.value)}
                                className={errors.startTime ? 'p-invalid' : ''}
                            />
                            {errors.startTime && <small className="p-error">{errors.startTime}</small>}
                        </div>
                    </div>

                    {/* End Date & Time */}
                    <div className="space-y-4">
                        <div className="field">
                            <label htmlFor="endDate" className="form-label text-sm font-medium">
                                End Date *
                            </label>
                            <Calendar
                                id="endDate"
                                value={formData.endDate}
                                onChange={(e) => handleInputChange('endDate', e.value)}
                                placeholder="Select end date"
                                showIcon
                                className={errors.endDate ? 'p-invalid' : ''}
                                minDate={formData.startDate || new Date()}
                            />
                            {errors.endDate && <small className="p-error">{errors.endDate}</small>}
                        </div>
                        
                        <div className="field">
                            <label htmlFor="endTime" className="form-label text-sm font-medium">
                                End Time *
                            </label>
                            <InputText
                                id="endTime"
                                type="time"
                                value={formData.endTime}
                                onChange={(e) => handleInputChange('endTime', e.target.value)}
                                className={errors.endTime ? 'p-invalid' : ''}
                            />
                            {errors.endTime && <small className="p-error">{errors.endTime}</small>}
                        </div>
                    </div>
                </div>

                {/* Location and Attendees Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="field">
                        <label htmlFor="location" className="form-label text-sm font-medium">
                            Location *
                        </label>
                        <InputText
                            id="location"
                            value={formData.location}
                            onChange={(e) => handleInputChange('location', e.target.value)}
                            placeholder="Enter event location"
                            className={errors.location ? 'p-invalid' : ''}
                        />
                        {errors.location && <small className="p-error">{errors.location}</small>}
                    </div>

                    <div className="field">
                        <label htmlFor="maxAttendees" className="form-label text-sm font-medium">
                            Maximum Attendees *
                        </label>
                        <InputNumber
                            id="maxAttendees"
                            value={formData.maxAttendees}
                            onValueChange={(e) => handleInputChange('maxAttendees', e.value)}
                            placeholder="Enter maximum attendees"
                            min={1}
                            max={10000}
                            className={errors.maxAttendees ? 'p-invalid' : ''}
                        />
                        {errors.maxAttendees && <small className="p-error">{errors.maxAttendees}</small>}
                    </div>
                </div>

                {/* Status */}
                <div className="field">
                    <label htmlFor="status" className="form-label text-sm font-medium">
                        Status
                    </label>
                    <Dropdown
                        id="status"
                        value={eventStatuses.find(s => s.value === formData.status)}
                        options={eventStatuses}
                        onChange={(e) => handleInputChange('status', e.value.value)}
                        placeholder="Select event status"
                        className="w-full"
                    />
                </div>
            </form>
        </Dialog>
    );
};

export default CreateEventModal;
