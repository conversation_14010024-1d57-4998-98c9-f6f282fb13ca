import React, { useState } from 'react';
import { InputText } from 'primereact/inputtext';
import { handleEmailChange } from '../utils/form';

const EmailField = ({ 
    formManager, 
    field = 'email', 
    value, 
    userId = null, 
    placeholder = 'Enter email address',
    className = '',
    disabled = false,
    required = false,
    onChange = null,
    onValidationChange = null
}) => {
    const [isChecking, setIsChecking] = useState(false);

    const handleChange = (e) => {
        const newValue = e.target.value;
        
        // Call custom onChange if provided
        if (onChange) {
            onChange(e);
        }

        // Handle email validation with availability check
        handleEmailChange(formManager, field, newValue, userId, (error) => {
            setIsChecking(false);
            if (onValidationChange) {
                onValidationChange(error);
            }
        });

        // Set checking state
        if (newValue && newValue.includes('@')) {
            setIsChecking(true);
        } else {
            setIsChecking(false);
        }
    };

    const getFieldError = () => {
        return formManager.getFieldError(field);
    };

    const hasError = () => {
        return formManager.hasFieldError(field);
    };

    const isEmailChecking = () => {
        return formManager.isEmailChecking() || isChecking;
    };

    return (
        <div className={`email-field ${className}`}>
            <div className="p-inputgroup">
                <InputText
                    type="email"
                    value={value}
                    onChange={handleChange}
                    placeholder={placeholder}
                    className={`${hasError() ? 'p-invalid' : ''} ${isEmailChecking() ? 'p-loading' : ''}`}
                    disabled={disabled}
                    required={required}
                    autoComplete="email"
                />
                {isEmailChecking() && (
                    <span className="p-inputgroup-addon">
                        <i className="pi pi-spin pi-spinner" />
                    </span>
                )}
            </div>
            
            {hasError() && (
                <small className="p-error block mt-1">
                    {getFieldError()}
                </small>
            )}
            
            {!hasError() && value && value.includes('@') && !isEmailChecking() && (
                <small className="p-success block mt-1">
                    ✓ Email address is available
                </small>
            )}
        </div>
    );
};

export default EmailField;

