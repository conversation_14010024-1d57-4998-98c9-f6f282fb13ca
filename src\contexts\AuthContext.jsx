import React, { createContext, useState, useContext, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState({ role: '', user_type: '' });

  // Initialize user state from localStorage on mount
  useEffect(() => {
    const userType = localStorage.getItem('user_type');
    const userRole = localStorage.getItem('user_role');
    
    if (userType || userRole) {
      setUser({ 
        role: userRole || '', 
        user_type: userType || '' 
      });
    }
  }, []);

  const login = (role, user_type) => {
    const userData = { role, user_type };
    setUser(userData);
    
    // Store in localStorage
    if (role) localStorage.setItem('user_role', role);
    if (user_type) localStorage.setItem('user_type', user_type);
  };

  const logout = () => {
    setUser({ role: '', user_type: '' });
    // Clear localStorage
    localStorage.removeItem('user_role');
    localStorage.removeItem('user_type');
    localStorage.removeItem('token');
    localStorage.removeItem('user_id');
    localStorage.removeItem('user_name');
    localStorage.removeItem('user_email');
  };

  return (
    <AuthContext.Provider value={{ user, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};
