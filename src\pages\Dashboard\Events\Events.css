/* Events page mobile-responsive styles */

/* Mobile-specific modal adjustments */
@media (max-width: 768px) {
  .mobile-events-modal .p-dialog-content {
    padding: 1rem;
    max-height: calc(90vh - 120px);
    overflow-y: auto;
  }

  .mobile-events-modal .p-dialog-header {
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
  }

  .mobile-events-modal .p-dialog-footer {
    padding: 1rem;
    border-top: 1px solid #e5e7eb;
  }

  /* Form field adjustments for mobile */
  .mobile-events-modal .field {
    margin-bottom: 1.5rem;
  }

  .mobile-events-modal .grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  /* Button adjustments */
  .mobile-events-modal .p-button {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .mobile-events-modal .flex.gap-2 {
    flex-direction: column;
  }
}

/* Mobile action menu styles */
.mobile-action-menu {
  position: absolute;
  right: 0;
  top: 100%;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 200px;
  overflow: hidden;
}

.mobile-action-menu button {
  width: 100%;
  padding: 12px 16px;
  text-align: left;
  border: none;
  background: white;
  color: #374151;
  font-size: 14px;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.mobile-action-menu button:hover {
  background-color: #f9fafb;
}

.mobile-action-menu button.danger {
  color: #dc2626;
}

.mobile-action-menu button.danger:hover {
  background-color: #fef2f2;
}

.mobile-action-menu hr {
  margin: 4px 0;
  border: none;
  border-top: 1px solid #e5e7eb;
}

/* Mobile events list styles */
.mobile-events-list {
  padding: 1rem;
}

.mobile-event-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.mobile-event-card h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.mobile-event-card p {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.mobile-event-meta {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.mobile-event-meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.mobile-event-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.mobile-event-actions .p-button {
  flex: 1;
  min-width: 80px;
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
}

/* Progress bar styles */
.event-progress-bar {
  width: 100%;
  height: 8px;
  background-color: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  margin-top: 4px;
}

.event-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* Status tag styles */
.event-status-tag {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  color: white;
}

/* Filter section mobile adjustments */
@media (max-width: 768px) {
  .events-filters {
    padding: 1rem;
  }

  .events-filters .grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .events-filters .relative input {
    padding-left: 2.5rem;
  }
}

/* Header section mobile adjustments */
@media (max-width: 768px) {
  .events-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .events-header h1 {
    font-size: 1.5rem;
  }

  .events-header p {
    font-size: 0.875rem;
  }

  .events-header .p-button {
    width: 100%;
    justify-content: center;
  }
}

/* DataTable mobile overrides */
@media (max-width: 768px) {
  .events-datatable .p-datatable-wrapper {
    overflow-x: auto;
  }

  .events-datatable .p-datatable-thead > tr > th {
    white-space: nowrap;
    min-width: 120px;
  }

  .events-datatable .p-datatable-tbody > tr > td {
    white-space: nowrap;
    min-width: 120px;
  }
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading states */
.events-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: #6b7280;
}

.events-loading .spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Empty state styles */
.events-empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #6b7280;
}

.events-empty-state svg {
  margin: 0 auto 1rem;
  opacity: 0.5;
}

.events-empty-state h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #374151;
}

.events-empty-state p {
  margin-bottom: 1.5rem;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Accessibility improvements */
.events-page button:focus,
.events-page input:focus,
.events-page select:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.events-page .sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .events-page {
    --border-color: #000;
    --text-color: #000;
    --bg-color: #fff;
  }

  .mobile-event-card {
    border: 2px solid var(--border-color);
  }

  .event-status-tag {
    border: 1px solid var(--border-color);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .events-page *,
  .events-page *::before,
  .events-page *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
