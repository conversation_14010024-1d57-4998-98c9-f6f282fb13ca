import { Route, Routes, Navigate } from 'react-router-dom'

import CompanyRoutes from './CompanyRoutes';
import AdminRoutes from './AdminRoutes';

import Registration from '@pages/Auth/Registration';
import Login from '@pages/Auth/Login';
import ForgotPassword from '@pages/Auth/ForgotPassword';
import ResetPassword from '@pages/Auth/ResetPassword';
import EmailVerification from '@pages/Auth/EmailVerification';
import TwoFactorVerification from '@pages/Auth/TwoFactorVerification';
import AuthChecker from './AuthChecker';

function RoutesContainer() {
  return (
    <Routes>
      {/* Public Routes */}
      <Route path='/login' element={<Login />} />
      <Route path='/register' element={<Registration />} />
      <Route path='/verify-email' element={<EmailVerification />} />
      <Route path='/forget-password' element={<ForgotPassword />} />
      <Route path='/reset-password/:token' element={<ResetPassword />} />
      <Route path='/two-factor-verification' element={<TwoFactorVerification onSuccess={() => window.location.href = '/manager/dashboard'} />} />
      
      {/* Protected Routes with 2FA */}
      <Route element={<AuthChecker />}>
        {/* Admin Routes */}
        <Route path='/admin/*' element={<AdminRoutes />} />
        
        {/* Company Routes */}
        <Route path='/*' element={<CompanyRoutes />} />
      </Route>

      <Route path="*" element={<div>Page Not Found</div>} />
    </Routes>
  )
}

export default RoutesContainer