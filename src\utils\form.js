import { validateAllFields, validateEmailWithDebounce } from './validation';
import { showValidationErrors, showAPIErrors, showActionSuccess, showErrorToast } from './toast';

// Form state management
export class FormManager {
    constructor(initialData = {}) {
        this.initialData = initialData;
        this.data = { ...initialData };
        this.errors = {};
        this.isSubmitting = false;
        this.isValid = false;
        this.emailChecking = false;
    }

    // Update form data
    updateField(field, value) {
        this.data[field] = value;
        this.validateField(field, value);
        this.checkFormValidity();
    }

    // Update multiple fields
    updateFields(fields) {
        Object.keys(fields).forEach(field => {
            this.data[field] = fields[field];
            this.validateField(field, fields[field]);
        });
        this.checkFormValidity();
    }

    // Validate single field
    validateField(field, value) {
        const fieldData = { [field]: value };
        const fieldErrors = validateAllFields(fieldData);
        
        if (fieldErrors[field]) {
            this.errors[field] = fieldErrors[field];
        } else {
            delete this.errors[field];
        }
    }

    // Validate email with availability check
    async validateEmailWithAvailability(email, userId = null, callback = null) {
        this.emailChecking = true;
        
        validateEmailWithDebounce(email, userId, (error) => {
            if (error) {
                this.errors.email = error;
            } else {
                delete this.errors.email;
            }
            this.emailChecking = false;
            this.checkFormValidity();
            
            if (callback) {
                callback(error);
            }
        });
    }

    // Validate all fields
    validateAll() {
        this.errors = validateAllFields(this.data);
        this.checkFormValidity();
        return Object.keys(this.errors).length === 0;
    }

    // Check if form is valid
    checkFormValidity() {
        this.isValid = Object.keys(this.errors).length === 0;
    }

    // Get field error
    getFieldError(field) {
        return this.errors[field] || null;
    }

    // Check if field has error
    hasFieldError(field) {
        return !!this.errors[field];
    }

    // Clear field error
    clearFieldError(field) {
        delete this.errors[field];
        this.checkFormValidity();
    }

    // Clear all errors
    clearAllErrors() {
        this.errors = {};
        this.checkFormValidity();
    }

    // Reset form to initial data
    reset() {
        this.data = { ...this.initialData };
        this.errors = {};
        this.isSubmitting = false;
        this.isValid = false;
        this.emailChecking = false;
    }

    // Get form data
    getData() {
        return { ...this.data };
    }

    // Get form errors
    getErrors() {
        return { ...this.errors };
    }

    // Set submitting state
    setSubmitting(submitting) {
        this.isSubmitting = submitting;
    }

    // Check if email is being validated
    isEmailChecking() {
        return this.emailChecking;
    }
}

// Hook for form management
export const useFormManager = (initialData = {}) => {
    const formManager = new FormManager(initialData);
    
    return {
        data: formManager.data,
        errors: formManager.errors,
        isSubmitting: formManager.isSubmitting,
        isValid: formManager.isValid,
        emailChecking: formManager.emailChecking,
        updateField: formManager.updateField.bind(formManager),
        updateFields: formManager.updateFields.bind(formManager),
        validateField: formManager.validateField.bind(formManager),
        validateEmailWithAvailability: formManager.validateEmailWithAvailability.bind(formManager),
        validateAll: formManager.validateAll.bind(formManager),
        getFieldError: formManager.getFieldError.bind(formManager),
        hasFieldError: formManager.hasFieldError.bind(formManager),
        clearFieldError: formManager.clearFieldError.bind(formManager),
        clearAllErrors: formManager.clearAllErrors.bind(formManager),
        reset: formManager.reset.bind(formManager),
        getData: formManager.getData.bind(formManager),
        getErrors: formManager.getErrors.bind(formManager),
        setSubmitting: formManager.setSubmitting.bind(formManager),
        isEmailChecking: formManager.isEmailChecking.bind(formManager),
    };
};

// Form submission handler
export const handleFormSubmit = async (formManager, submitFunction, successCallback = null) => {
    // Validate form
    if (!formManager.validateAll()) {
        showValidationErrors(formManager.getErrors());
        return false;
    }

    // Set submitting state
    formManager.setSubmitting(true);

    try {
        // Submit form
        const result = await submitFunction(formManager.getData());
        
        if (result.success) {
            showActionSuccess(result.action || 'action_completed');
            if (successCallback) {
                successCallback(result);
            }
            return true;
        } else {
            showAPIErrors(result);
            return false;
        }
    } catch (error) {
        showErrorToast('An unexpected error occurred');
        return false;
    } finally {
        formManager.setSubmitting(false);
    }
};

// Field change handler
export const handleFieldChange = (formManager, field, value) => {
    formManager.updateField(field, value);
};

// Field blur handler
export const handleFieldBlur = (formManager, field, value) => {
    formManager.validateField(field, value);
};

// Email field change handler with availability check
export const handleEmailChange = (formManager, field, value, userId = null, callback = null) => {
    formManager.updateField(field, value);
    
    // Check email availability if email format is valid
    if (value && value.includes('@')) {
        formManager.validateEmailWithAvailability(value, userId, callback);
    }
};

// Phone field change handler with formatting
export const handlePhoneChange = (formManager, field, value, countryCode, formatPhoneNumber) => {
    const formattedValue = formatPhoneNumber ? formatPhoneNumber(value, countryCode) : value;
    formManager.updateField(field, formattedValue);
};

// Custom field change handler
export const handleCustomFieldChange = (formManager, fieldIndex, value) => {
    const fieldName = `custom_field_${fieldIndex}`;
    formManager.updateField(fieldName, value);
};

// Form validation helper
export const validateFormData = (data, requiredFields = []) => {
    const errors = {};
    
    // Check required fields
    requiredFields.forEach(field => {
        if (!data[field] || data[field].toString().trim() === '') {
            errors[field] = `${field.charAt(0).toUpperCase() + field.slice(1)} is required`;
        }
    });
    
    // Validate all fields
    const validationErrors = validateAllFields(data);
    Object.assign(errors, validationErrors);
    
    return errors;
};

// Form data transformer
export const transformFormData = (data, transformations = {}) => {
    const transformed = { ...data };
    
    Object.keys(transformations).forEach(field => {
        if (transformed[field] !== undefined) {
            transformed[field] = transformations[field](transformed[field]);
        }
    });
    
    return transformed;
};

// Form data cleaner
export const cleanFormData = (data, fieldsToClean = []) => {
    const cleaned = { ...data };
    
    fieldsToClean.forEach(field => {
        if (cleaned[field] && typeof cleaned[field] === 'string') {
            cleaned[field] = cleaned[field].trim();
        }
    });
    
    return cleaned;
};
