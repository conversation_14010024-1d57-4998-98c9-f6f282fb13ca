import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { FiChevronLeft, FiChevronRight } from 'react-icons/fi';
import CardPreview from './CardPreview';
import './CardCarousel.css';

const CardCarousel = ({ 
  cards = [], 
  onCardSelect, 
  selectedCard = null,
  itemsPerView = { desktop: 3, tablet: 2, mobile: 1 },
  className = ''
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState(0);
  const carouselRef = useRef(null);

  // Mobile detection
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      setIsMobile(width < 768);
      setIsTablet(width >= 768 && width < 1024);
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Calculate items per view based on screen size
  const getItemsPerView = () => {
    if (isMobile) return itemsPerView.mobile;
    if (isTablet) return itemsPerView.tablet;
    return itemsPerView.desktop;
  };

  const currentItemsPerView = getItemsPerView();
  const maxIndex = Math.max(0, cards.length - currentItemsPerView);

  // Navigation functions
  const goToNext = () => {
    setCurrentIndex(prev => Math.min(prev + 1, maxIndex));
  };

  const goToPrevious = () => {
    setCurrentIndex(prev => Math.max(prev - 1, 0));
  };

  const goToSlide = (index) => {
    setCurrentIndex(Math.min(Math.max(index, 0), maxIndex));
  };

  // Touch/drag handlers
  const handleDragStart = (e) => {
    setIsDragging(true);
    setDragStart(e.type === 'mousedown' ? e.clientX : e.touches[0].clientX);
  };

  const handleDragEnd = (e) => {
    if (!isDragging) return;
    
    const dragEnd = e.type === 'mouseup' ? e.clientX : e.changedTouches[0].clientX;
    const dragDistance = dragStart - dragEnd;
    const threshold = 50;

    if (Math.abs(dragDistance) > threshold) {
      if (dragDistance > 0) {
        goToNext();
      } else {
        goToPrevious();
      }
    }

    setIsDragging(false);
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Only handle keyboard navigation when carousel is focused
      if (document.activeElement?.closest('.card-carousel')) {
        if (e.key === 'ArrowLeft') {
          e.preventDefault();
          goToPrevious();
        } else if (e.key === 'ArrowRight') {
          e.preventDefault();
          goToNext();
        } else if (e.key === 'Home') {
          e.preventDefault();
          goToSlide(0);
        } else if (e.key === 'End') {
          e.preventDefault();
          goToSlide(maxIndex);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [maxIndex]);

  // Auto-adjust index when cards change
  useEffect(() => {
    if (currentIndex > maxIndex) {
      setCurrentIndex(maxIndex);
    }
  }, [cards.length, currentItemsPerView, maxIndex]);

  if (!cards || cards.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        <div className="text-center">
          <div className="text-4xl mb-4">📋</div>
          <p>No card types available</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`card-carousel ${className}`}
      role="region"
      aria-label="Card types carousel"
      tabIndex={0}
    >
      {/* Navigation arrows */}
      <div className="flex items-center justify-between mb-6">
        <motion.button
          onClick={goToPrevious}
          disabled={currentIndex === 0}
          className="carousel-nav-btn"
          whileHover={{
            scale: 1.05,
            transition: { duration: 0.2, ease: "easeOut" }
          }}
          whileTap={{
            scale: 0.95,
            transition: { duration: 0.1, ease: "easeOut" }
          }}
          aria-label="Previous cards"
          title="Previous cards"
          style={{
            padding: '12px',
            borderRadius: '50%',
            border: 'none',
            background: currentIndex === 0 ? '#f3f4f6' : '#00c3ac',
            color: currentIndex === 0 ? '#9ca3af' : 'white',
            cursor: currentIndex === 0 ? 'not-allowed' : 'pointer',
            boxShadow: currentIndex === 0 ? 'none' : '0 4px 12px rgba(0, 195, 172, 0.3)',
            transition: 'all 0.3s ease',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <FiChevronLeft size={20} />
        </motion.button>

        {/* Slide indicators */}
        <div className="flex items-center space-x-2" role="tablist" aria-label="Carousel navigation">
          {Array.from({ length: maxIndex + 1 }, (_, index) => (
            <motion.button
              key={index}
              onClick={() => goToSlide(index)}
              className="carousel-indicator"
              whileHover={{
                scale: 1.2,
                transition: { duration: 0.2, ease: "easeOut" }
              }}
              whileTap={{
                scale: 0.9,
                transition: { duration: 0.1, ease: "easeOut" }
              }}
              role="tab"
              aria-selected={index === currentIndex}
              aria-label={`Go to slide ${index + 1}`}
              title={`Slide ${index + 1}`}
              style={{
                width: '8px',
                height: '8px',
                borderRadius: '50%',
                border: 'none',
                background: index === currentIndex ? '#00c3ac' : '#d1d5db',
                cursor: 'pointer',
                transition: 'all 0.3s ease'
              }}
            />
          ))}
        </div>

        <motion.button
          onClick={goToNext}
          disabled={currentIndex === maxIndex}
          className="carousel-nav-btn"
          whileHover={{
            scale: 1.05,
            transition: { duration: 0.2, ease: "easeOut" }
          }}
          whileTap={{
            scale: 0.95,
            transition: { duration: 0.1, ease: "easeOut" }
          }}
          aria-label="Next cards"
          title="Next cards"
          style={{
            padding: '12px',
            borderRadius: '50%',
            border: 'none',
            background: currentIndex === maxIndex ? '#f3f4f6' : '#00c3ac',
            color: currentIndex === maxIndex ? '#9ca3af' : 'white',
            cursor: currentIndex === maxIndex ? 'not-allowed' : 'pointer',
            boxShadow: currentIndex === maxIndex ? 'none' : '0 4px 12px rgba(0, 195, 172, 0.3)',
            transition: 'all 0.3s ease',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <FiChevronRight size={20} />
        </motion.button>
      </div>

      {/* Carousel container */}
      <div
        ref={carouselRef}
        className="carousel-container"
        style={{
          overflow: 'hidden',
          borderRadius: '12px',
          position: 'relative',
          padding: '16px 0'
        }}
        onMouseDown={handleDragStart}
        onMouseUp={handleDragEnd}
        onTouchStart={handleDragStart}
        onTouchEnd={handleDragEnd}
      >
        <motion.div
          className="carousel-track"
          style={{
            display: 'flex',
            cursor: isDragging ? 'grabbing' : 'grab'
          }}
          animate={{
            x: `-${currentIndex * (100 / currentItemsPerView)}%`
          }}
          transition={{
            type: 'spring',
            stiffness: 300,
            damping: 30
          }}
        >
          {cards.map((card, index) => (
            <div
              key={card.id || index}
              className="carousel-item"
              style={{
                flex: `0 0 ${100 / currentItemsPerView}%`,
                maxWidth: `${100 / currentItemsPerView}%`
              }}
            >
              <CardPreview
                card={card}
                isSelected={selectedCard?.id === card.id}
                onClick={() => onCardSelect && onCardSelect(card)}
              />
            </div>
          ))}
        </motion.div>
      </div>

      {/* Mobile swipe hint */}
      {isMobile && cards.length > 1 && (
        <motion.div
          className="text-center mt-4 text-sm text-gray-500 swipe-hint"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1 }}
        >
          👈 Swipe left or right to navigate 👉
        </motion.div>
      )}

      {/* Card counter */}
      <div className="text-center mt-4 text-sm text-gray-600">
        <span className="font-medium">
          {currentIndex + 1}-{Math.min(currentIndex + currentItemsPerView, cards.length)}
        </span>
        {' '}of{' '}
        <span className="font-medium">{cards.length}</span>
        {' '}card types
      </div>
    </div>
  );
};

export default CardCarousel;
