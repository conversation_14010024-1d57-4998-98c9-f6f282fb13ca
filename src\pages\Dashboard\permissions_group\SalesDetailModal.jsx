import React from 'react';
import { Dialog } from 'primereact/dialog';
import { motion } from 'framer-motion';
import { FiPackage, FiUser, FiCalendar, FiDollarSign, FiCreditCard, FiX } from 'react-icons/fi';
import { FaGift, FaCreditCard as FaCreditCardAlt } from 'react-icons/fa';
import { BsBank2 } from 'react-icons/bs';

const SalesDetailModal = ({ isOpen, onClose, saleData }) => {
    if (!saleData) return null;

    // Format currency
    const formatCurrency = (value) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2
        }).format(value);
    };

    // Format date
    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    // Get status color
    const getStatusColor = (status) => {
        const statusValue = status || 'active';
        return statusValue === 'active'
            ? 'bg-green-100 text-green-800 border-green-200'
            : 'bg-red-100 text-red-800 border-red-200';
    };

    // Get payment method icon
    const getPaymentMethodIcon = (paymentMethod) => {
        switch (paymentMethod) {
            case 'gift':
                return <FaGift className="text-purple-500" size={16} />;
            case 'bank_transfer':
                return <BsBank2 className="text-blue-500" size={16} />;
            case 'regular':
            default:
                return <FaCreditCardAlt className="text-green-500" size={16} />;
        }
    };

    // Format price with gift handling
    const formatPriceWithGift = (sale) => {
        const paymentMethod = sale.payment_method || 'regular';
        const totalPrice = parseFloat(sale.total_price || 0);

        if (paymentMethod === 'gift' || totalPrice === 0) {
            return (
                <div className="flex items-center space-x-3">
                    <span className="text-2xl font-bold text-purple-600">GIFT</span>
                    {getPaymentMethodIcon(paymentMethod)}
                </div>
            );
        }

        return (
            <div className="flex items-center space-x-3">
                <span className="text-2xl font-bold text-[#00c3ac]">
                    {formatCurrency(totalPrice)}
                </span>
                {getPaymentMethodIcon(paymentMethod)}
            </div>
        );
    };

    const headerContent = (
        <div className="flex items-center justify-between">
            <div className="flex items-center">
                <div className="p-3 rounded-full bg-[#00c3ac]/10 text-[#00c3ac] mr-4">
                    <FiPackage size={24} />
                </div>
                <div>
                    <h2 className="text-xl font-bold text-gray-900">Package Details</h2>
                    <p className="text-sm text-gray-500">Complete sales information</p>
                </div>
            </div>
        </div>
    );

    return (
        <Dialog
            visible={isOpen}
            onHide={onClose}
            header={headerContent}
            modal
            className="w-full max-w-2xl mx-4"
            contentClassName="p-0"
            headerClassName="border-b border-gray-200 pb-4"
            dismissableMask
            closeOnEscape
        >
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="p-6"
            >
                {/* Package Information */}
                <div className="mb-6">
                    <div className="bg-gradient-to-r from-[#00c3ac]/5 to-[#02aa96]/5 rounded-lg p-4 border border-[#00c3ac]/20">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">{saleData.name || 'Unnamed Package'}</h3>
                        <div className="flex items-center justify-between">
                            {formatPriceWithGift(saleData)}
                            <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full border ${getStatusColor(saleData.status)}`}>
                                {saleData.status || 'active'}
                            </span>
                        </div>
                    </div>
                </div>

                {/* Details Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Customer Information */}
                    <div className="space-y-4">
                        <h4 className="text-md font-semibold text-gray-900 border-b border-gray-200 pb-2">Customer Information</h4>
                        
                        <div className="flex items-start space-x-3">
                            <div className="p-2 rounded-full bg-blue-500/10 text-blue-500">
                                <FiUser size={16} />
                            </div>
                            <div>
                                <p className="text-sm font-medium text-gray-500">Customer Name</p>
                                <p className="text-sm text-gray-900">{saleData.purchased_by_manager_name || 'Unknown Customer'}</p>
                            </div>
                        </div>

                        <div className="flex items-start space-x-3">
                            <div className="p-2 rounded-full bg-purple-500/10 text-purple-500">
                                <FiCreditCard size={16} />
                            </div>
                            <div>
                                <p className="text-sm font-medium text-gray-500">Card Limit</p>
                                <p className="text-sm text-gray-900">{saleData.card_limit || 0} cards</p>
                            </div>
                        </div>

                        <div className="flex items-start space-x-3">
                            <div className="p-2 rounded-full bg-indigo-500/10 text-indigo-500">
                                {getPaymentMethodIcon(saleData.payment_method || 'regular')}
                            </div>
                            <div>
                                <p className="text-sm font-medium text-gray-500">Payment Method</p>
                                <p className="text-sm text-gray-900 capitalize">
                                    {saleData.payment_method === 'gift' ? 'Gift (Free)' :
                                     saleData.payment_method === 'bank_transfer' ? 'Bank Transfer' :
                                     'Regular Payment'}
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Date Information */}
                    <div className="space-y-4">
                        <h4 className="text-md font-semibold text-gray-900 border-b border-gray-200 pb-2">Date Information</h4>
                        
                        <div className="flex items-start space-x-3">
                            <div className="p-2 rounded-full bg-green-500/10 text-green-500">
                                <FiCalendar size={16} />
                            </div>
                            <div>
                                <p className="text-sm font-medium text-gray-500">Purchase Date</p>
                                <p className="text-sm text-gray-900">{formatDate(saleData.purchased_at)}</p>
                            </div>
                        </div>

                        <div className="flex items-start space-x-3">
                            <div className="p-2 rounded-full bg-orange-500/10 text-orange-500">
                                <FiCalendar size={16} />
                            </div>
                            <div>
                                <p className="text-sm font-medium text-gray-500">Expiry Date</p>
                                <p className="text-sm text-gray-900">{formatDate(saleData.expiry_date)}</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Additional Information */}
                {(saleData.description || saleData.notes) && (
                    <div className="mt-6">
                        <h4 className="text-md font-semibold text-gray-900 border-b border-gray-200 pb-2 mb-4">Additional Information</h4>
                        <div className="bg-gray-50 rounded-lg p-4">
                            <p className="text-sm text-gray-700">
                                {saleData.description || saleData.notes || 'No additional information available.'}
                            </p>
                        </div>
                    </div>
                )}

                {/* Action Buttons */}
                <div className="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
                    <button
                        onClick={onClose}
                        className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200"
                    >
                        Close
                    </button>
                    <button
                        onClick={() => {
                            // Add any additional actions here (e.g., edit, export)
                            console.log('Additional action for sale:', saleData);
                        }}
                        className="px-4 py-2 text-sm font-medium text-white bg-[#00c3ac] border border-[#00c3ac] rounded-md hover:bg-[#02aa96] hover:border-[#02aa96] transition-colors duration-200"
                    >
                        View Details
                    </button>
                </div>
            </motion.div>
        </Dialog>
    );
};

export default SalesDetailModal;
