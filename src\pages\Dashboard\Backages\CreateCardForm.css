/* Modern Card Form Dialog Styles */
.modern-card-form-dialog .p-dialog {
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15) !important;
  border: none !important;
}

.modern-card-form-dialog .p-dialog-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
  border-bottom: 1px solid #e2e8f0 !important;
  padding: 20px 24px !important;
  border-radius: 16px 16px 0 0 !important;
}

.modern-card-form-dialog .p-dialog-header .p-dialog-title {
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  color: #1f2937 !important;
}

.modern-card-form-dialog .p-dialog-header-icon {
  color: #6b7280 !important;
  transition: all 0.2s ease !important;
}

.modern-card-form-dialog .p-dialog-header-icon:hover {
  color: #374151 !important;
  background: rgba(0, 0, 0, 0.05) !important;
  border-radius: 6px !important;
}

.modern-card-form-dialog .p-dialog-content {
  background: white !important;
  padding: 0 !important;
}

/* Form input styling */
.modern-card-form-dialog .p-inputtext {
  border: 1px solid #d1d5db !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
  font-size: 14px !important;
  transition: all 0.2s ease !important;
  background: white !important;
}

.modern-card-form-dialog .p-inputtext:focus {
  border-color: #00c3ac !important;
  box-shadow: 0 0 0 3px rgba(0, 195, 172, 0.1) !important;
  outline: none !important;
}

.modern-card-form-dialog .p-inputtext:hover:not(:focus) {
  border-color: #9ca3af !important;
}

/* Label styling */
.modern-card-form-dialog label {
  font-weight: 500 !important;
  color: #374151 !important;
  margin-bottom: 6px !important;
}

/* Section headers */
.modern-card-form-dialog h3 {
  color: #1f2937 !important;
  font-weight: 600 !important;
  margin-bottom: 16px !important;
}

/* Button styling */
.modern-card-form-dialog button {
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
  border: none !important;
  cursor: pointer !important;
}

.modern-card-form-dialog button:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modern-card-form-dialog .p-dialog {
    width: 95vw !important;
    margin: 10px !important;
    max-height: 90vh !important;
  }

  .modern-card-form-dialog .p-dialog-header {
    padding: 16px 20px !important;
  }

  .modern-card-form-dialog .p-dialog-content {
    padding: 0 !important;
  }

  .modern-card-form-dialog .grid {
    grid-template-columns: 1fr !important;
  }

  .modern-card-form-dialog .flex {
    flex-direction: column !important;
    gap: 12px !important;
  }

  .modern-card-form-dialog button {
    width: 100% !important;
    padding: 14px !important;
  }

  /* Mobile-specific form adjustments */
  .modern-card-form-dialog .space-y-6 > * + * {
    margin-top: 1rem !important;
  }

  .modern-card-form-dialog .space-y-4 > * + * {
    margin-top: 0.75rem !important;
  }

  .modern-card-form-dialog h3 {
    font-size: 1rem !important;
  }

  .modern-card-form-dialog label {
    font-size: 0.875rem !important;
  }
}

@media (max-width: 480px) {
  .modern-card-form-dialog .p-dialog {
    width: 98vw !important;
    margin: 5px !important;
  }

  .modern-card-form-dialog .p-dialog-header {
    padding: 12px 16px !important;
  }

  .modern-card-form-dialog .p-dialog-header h2 {
    font-size: 1.125rem !important;
  }

  .modern-card-form-dialog .p-inputtext {
    padding: 10px 14px !important;
    font-size: 14px !important;
  }

  .modern-card-form-dialog button {
    padding: 12px !important;
    font-size: 14px !important;
  }
}

/* Animation improvements */
.modern-card-form-dialog .p-dialog-enter {
  animation: dialog-enter 0.3s ease-out !important;
}

.modern-card-form-dialog .p-dialog-exit {
  animation: dialog-exit 0.2s ease-in !important;
}

@keyframes dialog-enter {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes dialog-exit {
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  to {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
}

/* Loading spinner styling */
.modern-card-form-dialog .animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Focus states for accessibility */
.modern-card-form-dialog button:focus,
.modern-card-form-dialog .p-inputtext:focus {
  outline: 2px solid #00c3ac !important;
  outline-offset: 2px !important;
}

/* Divider styling */
.modern-card-form-dialog .border-t {
  border-color: #e5e7eb !important;
}

/* Error state styling */
.modern-card-form-dialog .p-inputtext.p-invalid {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

/* Success state styling */
.modern-card-form-dialog .p-inputtext.p-valid {
  border-color: #10b981 !important;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
}
