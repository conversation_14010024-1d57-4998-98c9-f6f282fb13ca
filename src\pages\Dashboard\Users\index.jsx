import { useState, useEffect } from 'react'

import TypesTabMenu from './components/TypesTabMenu';
import MemberDataTable from './Members/MemberDataTable';
import GroupsDataTable from './Groups/GroupsDataTable';
import { useGetDesigns } from "@quires/template"
import Container from '@components/Container'
import { useParams } from 'react-router-dom';
import { MembersDataTableProvider } from '@contexts/MembersDataTableContext';
import { GroupsDataTableProvider } from '@contexts/GroupsDataTableContext';

function MembersIndex() {
    useGetDesigns();

    const { type } = useParams();
    const [activeTap, setActiveTap] = useState(type === "members" ? 0 : 1);

    // تنظيف البيانات عند تغيير التبويبات
    useEffect(() => {
        setActiveTap(type === "members" ? 0 : 1);
    }, [type]);

    return (
        <Container>
            <div className='w-full'>
                <TypesTabMenu activeTap={activeTap} setActiveTap={setActiveTap} />
            </div>
            <div className="w-full overflow-x-auto ">
                {
                    activeTap === 0 ?
                        <MembersDataTableProvider>
                            <MemberDataTable />
                        </MembersDataTableProvider>
                        :
                        <GroupsDataTableProvider>
                            <GroupsDataTable />
                        </GroupsDataTableProvider>
                }
            </div>
        </Container>
    )
}

export default MembersIndex