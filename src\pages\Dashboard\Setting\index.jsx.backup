import { useState, useRef, useEffect } from 'react';
import { InputText } from 'primereact/inputtext';
import { Password } from 'primereact/password';
import { But<PERSON> } from 'primereact/button';
import { Toast } from 'primereact/toast';
import { Image } from 'primereact/image';
import { QRCodeSVG } from 'qrcode.react';
import axiosInstance from "../../../config/Axios";
// import jsQR from 'jsqr'; // Removed

// API URL for image storage
const API_URL = import.meta.env.VITE_REACT_APP_API_URL || 'http://192.168.88.79:8000';

// Default user image
const DEFAULT_USER_IMAGE = 'https://storage.inknull.com/uploads/user-image-14-591-1751789627.png';

function SettingsIndex() {
    const toast = useRef(null);
    const fileInputRef = useRef(null);
    // const videoRef = useRef(null); // Removed
    // const canvasRef = useRef(null); // Removed
    const [isEditingProfile, setIsEditingProfile] = useState(false);
    const [isEditingPassword, setIsEditingPassword] = useState(false);
    const [originalName, setOriginalName] = useState('');
    const [originalEmail, setOriginalEmail] = useState('');
    const [loading, setLoading] = useState(false);
    const [imageLoading, setImageLoading] = useState(false);
    const [errors, setErrors] = useState({});
    const [userImage, setUserImage] = useState('');

    // Two-Factor Authentication states
    const [twoFactorStatus, setTwoFactorStatus] = useState({ enabled: false, verified_at: null });
    const [twoFactorLoading, setTwoFactorLoading] = useState(false);
    const [qrCodeData, setQrCodeData] = useState(null);

    const [verificationCode, setVerificationCode] = useState('');
    const [secretKey, setSecretKey] = useState('');
    const [showSetup, setShowSetup] = useState(false);
    const [showDisable, setShowDisable] = useState(false);
    
    // QR Code scan status states
    const [qrScanStatus, setQrScanStatus] = useState({
        qr_generated: false,
        qr_scanned: false,
        qr_scanned_at: null,
        loading: false
    });

    // WebAuthn states
    const [webAuthnStatus, setWebAuthnStatus] = useState({
        enabled: false,
        keys_count: 0,
        keys: []
    });
    const [webAuthnLoading, setWebAuthnLoading] = useState(false);


    // Form states
    const [name, setName] = useState('');
    const [email, setEmail] = useState('');
    const [currentPassword, setCurrentPassword] = useState('');
    const [newPassword, setNewPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');

    // Professional setup step
    const [setupStep, setSetupStep] = useState(1);
    // Remove qrScanned, isScanning, scanningError, videoRef, canvasRef
    // const [qrScanned, setQrScanned] = useState(false); // Removed
    // const [isScanning, setIsScanning] = useState(false); // Removed
    // const [scanningError, setScanningError] = useState(''); // Removed

    // Fetch user data on component mount
    useEffect(() => {
        fetchUserData();
        fetchTwoFactorStatus();
        fetchQrScanStatus();
        fetchWebAuthnStatus();
    }, []);





    // Remove all useEffect and functions related to camera/jsQR/scanQRCode
    // const startQRScanning = async () => { ... }; // Removed
    // const scanQRCode = () => { ... }; // Removed
    // const stopQRScanning = () => { ... }; // Removed

    const fetchUserData = async () => {
        try {
            const userId = localStorage.getItem('user_id');
            const response = await axiosInstance.get(`/users/${userId}`);
            const userData = response.data.data || response.data;

            setName(userData.name || '');
            setEmail(userData.email || '');
            setOriginalName(userData.name || '');
            setOriginalEmail(userData.email || '');
            
            if (userData.image) {
                setUserImage(userData.image);
                localStorage.setItem('user_image', userData.image);
            } else {
                setUserImage('');
                localStorage.removeItem('user_image');
            }
        } catch (error) {
            console.error('Error fetching user data:', error);
            showMessage('error', 'Error', 'Failed to load user data');
        }
    };

    const fetchTwoFactorStatus = async () => {
        try {
            const response = await axiosInstance.get('/two-factor/status');
            setTwoFactorStatus(response.data);
        } catch (error) {
            console.error('Error fetching two-factor status:', error);
        }
    };

    // Fetch QR code scan status
    const fetchQrScanStatus = async () => {
        try {
            setQrScanStatus(prev => ({ ...prev, loading: true }));
            const response = await axiosInstance.get('/two-factor/qr-status');
            setQrScanStatus({
                ...response.data,
                loading: false
            });
        } catch (error) {
            console.error('Error fetching QR scan status:', error);
            setQrScanStatus(prev => ({ ...prev, loading: false }));
        }
    };

    // Fetch WebAuthn status
    const fetchWebAuthnStatus = async () => {
        try {
            const response = await axiosInstance.get('/webauthn/status');
            console.log('WebAuthn Status:', response.data);
            setWebAuthnStatus(response.data);
        } catch (error) {
            console.error('Error fetching WebAuthn status:', error);
        }
    };

    // Enable WebAuthn
    const enableWebAuthn = async () => {
        try {
            setWebAuthnLoading(true);
            
            // Check WebAuthn support
            if (!window.PublicKeyCredential) {
                throw new Error('WebAuthn is not supported on this browser');
            }
            
            // Step 1: Begin registration
            const beginResponse = await axiosInstance.post('/webauthn/register/begin');
            console.log('WebAuthn Begin Response:', beginResponse.data);
            
            if (!beginResponse.data.success) {
                throw new Error(beginResponse.data.message);
            }
            
            const options = beginResponse.data.options;
            
            // Convert base64 strings to ArrayBuffers
            const challenge = Uint8Array.from(atob(options.challenge), c => c.charCodeAt(0));
            const userId = Uint8Array.from(atob(options.user.id), c => c.charCodeAt(0));
            
            // Step 2: Create credential
            const credential = await navigator.credentials.create({
                publicKey: {
                    challenge: challenge,
                    rp: options.rp,
                    user: {
                        id: userId,
                        name: options.user.name,
                        displayName: options.user.displayName
                    },
                    pubKeyCredParams: options.pubKeyCredParams,
                    authenticatorSelection: options.authenticatorSelection,
                    timeout: options.timeout,
                    attestation: options.attestation
                }
            });
            
            if (!credential) {
                throw new Error('Failed to create credential');
            }
            
            // Step 3: Complete registration
            const completeResponse = await axiosInstance.post('/webauthn/register/complete', {
                credential: {
                    id: credential.id,
                    rawId: btoa(String.fromCharCode(...new Uint8Array(credential.rawId))),
                    type: credential.type,
                    response: {
                        attestationObject: btoa(String.fromCharCode(...new Uint8Array(credential.response.attestationObject))),
                        clientDataJSON: btoa(String.fromCharCode(...new Uint8Array(credential.response.clientDataJSON)))
                    }
                }
            });
            
            console.log('WebAuthn Complete Response:', completeResponse.data);
            
            if (completeResponse.data.success) {
                showMessage('success', '🎉 WebAuthn Enabled!', `Successfully registered ${completeResponse.data.device_name}!`);
                await fetchWebAuthnStatus();
                await fetchTwoFactorStatus();
            } else {
                throw new Error(completeResponse.data.message);
            }
            
        } catch (error) {
            console.error('WebAuthn Error:', error);
            let message = 'Failed to enable WebAuthn';
            
            if (error.name === 'NotSupportedError') {
                message = 'WebAuthn is not supported on this device';
            } else if (error.name === 'NotAllowedError') {
                message = 'WebAuthn was cancelled or timed out';
            } else if (error.message) {
                message = error.message;
            }
            
            showMessage('error', 'WebAuthn Error', message);
        } finally {
            setWebAuthnLoading(false);
        }
    };

    // Disable WebAuthn
    const disableWebAuthn = async () => {
        try {
            setWebAuthnLoading(true);
            
            const response = await axiosInstance.post('/webauthn/disable');
            
            if (response.data.success) {
                showMessage('success', 'WebAuthn Disabled', 'WebAuthn has been disabled successfully');
                await fetchWebAuthnStatus();
                await fetchTwoFactorStatus();
            } else {
                throw new Error(response.data.message);
            }
            
        } catch (error) {
            console.error('Error disabling WebAuthn:', error);
            showMessage('error', 'Error', error.response?.data?.message || 'Failed to disable WebAuthn');
        } finally {
            setWebAuthnLoading(false);
        }
    };

    // Mark QR code as scanned (Auto Detection)
    const markQrCodeAsScanned = async () => {
        try {
            console.log('🎯 Auto Detection: Marking QR code as scanned...');
            setQrScanStatus(prev => ({ ...prev, loading: true }));
            
            const response = await axiosInstance.post('/two-factor/mark-qr-scanned');
            console.log('📡 API Response:', response.data);
            
            if (response.data.success) {
                console.log('✅ Successfully detected QR scan!');
                setQrScanStatus(prev => ({
                    ...prev,
                    qr_scanned: true,
                    qr_scanned_at: response.data.scanned_at,
                    loading: false
                }));
                stopAutoDetection();
                showMessage('success', '🎉 QR Code Detected!', 'Smart detection system automatically recognized your QR scan!');
            } else {
                console.log('❌ API call succeeded but response was not successful:', response.data);
                setQrScanStatus(prev => ({ ...prev, loading: false }));
            }
        } catch (error) {
            console.error('❌ Error marking QR as scanned:', error);
            console.error('Error details:', error.response?.data);
            setQrScanStatus(prev => ({ ...prev, loading: false }));
        }
    };

    // Auto Detection Functions
    const startAutoDetection = () => {
        if (autoDetection.isTracking) return;

        console.log('Starting QR code auto detection...');
        
        const startTime = Date.now();
        setAutoDetection(prev => ({
            ...prev,
            isTracking: true,
            startTime: startTime,
            lastVisibilityChange: startTime,
            focusCount: 0
        }));

        // Start Page Visibility tracking
        document.addEventListener('visibilitychange', handleVisibilityChange);
        
        // Start Focus/Blur tracking
        window.addEventListener('focus', handleWindowFocus);
        window.addEventListener('blur', handleWindowBlur);
        
        // Add keyboard event listener for Alt+Tab detection
        document.addEventListener('keydown', handleKeyDown);
        
        // Monitor for camera/media access (advanced detection)
        monitorMediaAccess();
        
        // Monitor user interaction patterns
        monitorUserInteraction();
        
        // Monitor battery usage (camera drains battery)
        monitorBatteryUsage();
        
        // Monitor device orientation and motion
        monitorDeviceMotion();
        
        // Monitor network activity (authenticator apps may sync)
        monitorNetworkActivity();

        // Start polling mechanism - check very frequently for instant response
        const pollingInterval = setInterval(checkAutoDetectionConditions, 500);
        setAutoDetection(prev => ({ ...prev, polling: pollingInterval }));

        // Quick fallback timer - if no activity detected after 15 seconds, assume scanned
        setTimeout(() => {
            if (autoDetection.isTracking && !qrScanStatus.qr_scanned) {
                console.log('🕒 Auto detection timeout (15s) - assuming QR code was scanned');
                markQrCodeAsScanned();
            }
        }, 15000);
    };

    const stopAutoDetection = () => {
        console.log('Stopping QR code auto detection...');
        
        document.removeEventListener('visibilitychange', handleVisibilityChange);
        window.removeEventListener('focus', handleWindowFocus);
        window.removeEventListener('blur', handleWindowBlur);
        document.removeEventListener('keydown', handleKeyDown);

        if (autoDetection.polling) {
            clearInterval(autoDetection.polling);
        }

        setAutoDetection({
            isTracking: false,
            startTime: null,
            lastVisibilityChange: null,
            focusCount: 0,
            polling: null
        });
    };

    const handleVisibilityChange = () => {
        const now = Date.now();
        
        console.log('Visibility changed. Document hidden:', document.hidden);
        console.log('Current autoDetection state:', autoDetection);
        console.log('QR scan status:', qrScanStatus);
        
        if (document.hidden) {
            // User left the page (possibly to open camera app)
            setAutoDetection(prev => {
                const newState = {
                    ...prev,
                    lastVisibilityChange: now
                };
                console.log('Updated autoDetection state (hidden):', newState);
                return newState;
            });
            console.log('Page became hidden - user possibly opening camera app');
        } else {
            // User returned to the page
            const timeAway = autoDetection.lastVisibilityChange ? now - autoDetection.lastVisibilityChange : 0;
            console.log(`Page became visible - user was away for ${timeAway}ms`);
            
            // Much more sensitive detection - even 500ms away is enough
            if (timeAway > 500 && timeAway < 60000 && autoDetection.isTracking && !qrScanStatus.qr_scanned) {
                console.log('🎯 Auto detection triggered - user returned after any time away');
                setTimeout(() => {
                    console.log('Checking if still need to mark as scanned...');
                    if (!qrScanStatus.qr_scanned) {
                        console.log('✅ Marking QR code as scanned!');
                        markQrCodeAsScanned();
                    } else {
                        console.log('QR already marked as scanned');
                    }
                }, 300); // Very quick response
            } else {
                console.log(`Not triggering auto detection. TimeAway: ${timeAway}, IsTracking: ${autoDetection.isTracking}, AlreadyScanned: ${qrScanStatus.qr_scanned}`);
            }
        }
    };

    const handleWindowFocus = () => {
        const now = Date.now();
        console.log('🔍 Window focused event');
        
        setAutoDetection(prev => {
            const newState = {
                ...prev,
                focusCount: prev.focusCount + 1,
                lastVisibilityChange: now
            };
            console.log('Focus count updated:', newState.focusCount);
            
            // Even single focus after initial setup indicates likely scan
            if (newState.focusCount >= 1 && prev.isTracking && !qrScanStatus.qr_scanned) {
                // Wait a bit to see if this is just navigation, then assume scan
                const timeFromStart = now - prev.startTime;
                if (timeFromStart > 2000) { // At least 2 seconds after QR shown
                    console.log('🎯 Auto detection triggered - focus event after QR display');
                    setTimeout(() => {
                        if (!qrScanStatus.qr_scanned) {
                            console.log('✅ Marking QR code as scanned due to focus!');
                            markQrCodeAsScanned();
                        }
                    }, 300);
                }
            }
            
            return newState;
        });
    };

    const handleWindowBlur = () => {
        console.log('🌀 Window blurred - user possibly opening camera app');
    };

    const handleKeyDown = (event) => {
        // Detect Alt+Tab or Cmd+Tab (app switching)
        if ((event.altKey && event.key === 'Tab') || (event.metaKey && event.key === 'Tab')) {
            console.log('⌨️ App switch detected - user possibly opening camera app');
            setAutoDetection(prev => ({
                ...prev,
                lastVisibilityChange: Date.now()
            }));
        }
    };

    // Advanced detection: Monitor for camera/media access
    const monitorMediaAccess = () => {
        console.log('📹 Starting media access monitoring...');
        
        // Check if navigator.mediaDevices is available
        if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
            // Monitor for device changes (camera access)
            navigator.mediaDevices.addEventListener('devicechange', () => {
                console.log('📱 Media device change detected - possible camera usage');
                if (autoDetection.isTracking && !qrScanStatus.qr_scanned) {
                    setTimeout(() => {
                        if (!qrScanStatus.qr_scanned) {
                            console.log('🎯 Auto detection triggered - media device activity');
                            markQrCodeAsScanned();
                        }
                    }, 1000);
                }
            });
        }

        // Monitor for getUserMedia calls (camera permissions)
        if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
            const originalGetUserMedia = navigator.mediaDevices.getUserMedia.bind(navigator.mediaDevices);
            navigator.mediaDevices.getUserMedia = function(...args) {
                console.log('📷 Camera access detected - getUserMedia called');
                if (autoDetection.isTracking && !qrScanStatus.qr_scanned) {
                    setTimeout(() => {
                        if (!qrScanStatus.qr_scanned) {
                            console.log('🎯 Auto detection triggered - camera access');
                            markQrCodeAsScanned();
                        }
                    }, 1500);
                }
                return originalGetUserMedia(...args);
            };
        }

        // Smart pattern detection: Monitor for common QR scanning patterns
        let mouseMovements = 0;
        let lastMouseMove = Date.now();

        const handleMouseMove = () => {
            const now = Date.now();
            if (now - lastMouseMove > 100) { // Throttle to avoid spam
                mouseMovements++;
                lastMouseMove = now;
                
                // If user starts moving mouse a lot after seeing QR, they might be positioning phone
                if (mouseMovements > 10 && autoDetection.isTracking && !qrScanStatus.qr_scanned) {
                    const timeSinceStart = now - autoDetection.startTime;
                    if (timeSinceStart > 3000 && timeSinceStart < 15000) {
                        console.log('🖱️ Mouse activity pattern suggests QR positioning');
                        setTimeout(() => {
                            if (!qrScanStatus.qr_scanned) {
                                console.log('🎯 Auto detection triggered - mouse positioning pattern');
                                markQrCodeAsScanned();
                            }
                        }, 2000);
                        document.removeEventListener('mousemove', handleMouseMove);
                    }
                }
            }
        };

        document.addEventListener('mousemove', handleMouseMove);

        // Cleanup function
        return () => {
            document.removeEventListener('mousemove', handleMouseMove);
        };
    };

    // Monitor user interaction patterns after QR display
    const monitorUserInteraction = () => {
        console.log('👆 Starting user interaction monitoring...');
        
        let interactionCount = 0;
        let hasInteracted = false;
        
        const handleUserInteraction = (eventType) => {
            const now = Date.now();
            const timeSinceStart = now - autoDetection.startTime;
            
            // Only count interactions after QR has been displayed for at least 2 seconds
            if (timeSinceStart > 2000 && autoDetection.isTracking && !qrScanStatus.qr_scanned) {
                interactionCount++;
                hasInteracted = true;
                
                console.log(`👆 User interaction detected: ${eventType} (count: ${interactionCount})`);
                
                // Any interaction after 3 seconds suggests user is back from camera app
                if (timeSinceStart > 3000) {
                    setTimeout(() => {
                        if (!qrScanStatus.qr_scanned && hasInteracted) {
                            console.log('🎯 Auto detection triggered - user interaction after QR display');
                            markQrCodeAsScanned();
                        }
                    }, 1000);
                }
            }
        };

        const events = ['click', 'scroll', 'touchstart', 'keypress'];
        events.forEach(event => {
            document.addEventListener(event, () => handleUserInteraction(event), { once: true });
        });

        // Monitor for specific mobile touch patterns
        let touchCount = 0;
        const handleTouch = () => {
            touchCount++;
            if (touchCount >= 2 && autoDetection.isTracking && !qrScanStatus.qr_scanned) {
                const timeSinceStart = Date.now() - autoDetection.startTime;
                if (timeSinceStart > 2000) {
                    console.log('📱 Mobile touch pattern detected - likely return from camera');
                    setTimeout(() => {
                        if (!qrScanStatus.qr_scanned) {
                            console.log('🎯 Auto detection triggered - mobile touch pattern');
                            markQrCodeAsScanned();
                        }
                    }, 800);
                }
            }
        };

        document.addEventListener('touchstart', handleTouch);
        document.addEventListener('touchend', handleTouch);
    };

    // Monitor battery usage - camera apps drain battery significantly
    const monitorBatteryUsage = () => {
        if ('getBattery' in navigator) {
            navigator.getBattery().then(battery => {
                console.log('🔋 Starting battery monitoring...');
                let initialLevel = battery.level;
                
                const checkBatteryDrain = () => {
                    if (autoDetection.isTracking && !qrScanStatus.qr_scanned) {
                        const currentLevel = battery.level;
                        const drain = initialLevel - currentLevel;
                        
                        // Even small battery drain (0.1%) can indicate camera usage
                        if (drain > 0.001) {
                            console.log(`🔋 Battery drain detected: ${(drain * 100).toFixed(2)}%`);
                            setTimeout(() => {
                                if (!qrScanStatus.qr_scanned) {
                                    console.log('🎯 Auto detection triggered - battery drain pattern');
                                    markQrCodeAsScanned();
                                }
                            }, 1000);
                        }
                    }
                };
                
                battery.addEventListener('levelchange', checkBatteryDrain);
                battery.addEventListener('chargingchange', () => {
                    if (!battery.charging && autoDetection.isTracking && !qrScanStatus.qr_scanned) {
                        console.log('🔌 Device unplugged - possible camera usage');
                    }
                });
            });
        }
    };

    // Monitor device orientation and motion for mobile devices
    const monitorDeviceMotion = () => {
        console.log('📱 Starting device motion monitoring...');
        
        // Monitor orientation changes (portrait/landscape switches common with camera)
        const handleOrientationChange = () => {
            if (autoDetection.isTracking && !qrScanStatus.qr_scanned) {
                console.log('🔄 Device orientation changed - possible camera app usage');
                setTimeout(() => {
                    if (!qrScanStatus.qr_scanned) {
                        console.log('🎯 Auto detection triggered - orientation change');
                        markQrCodeAsScanned();
                    }
                }, 2000);
            }
        };
        
        window.addEventListener('orientationchange', handleOrientationChange);
        window.addEventListener('resize', handleOrientationChange);
        
        // Monitor device motion (phone movement patterns during QR scanning)
        if ('DeviceMotionEvent' in window) {
            let motionCount = 0;
            const handleDeviceMotion = (event) => {
                if (autoDetection.isTracking && !qrScanStatus.qr_scanned) {
                    const { acceleration } = event;
                    if (acceleration && (Math.abs(acceleration.x) > 2 || Math.abs(acceleration.y) > 2)) {
                        motionCount++;
                        if (motionCount > 5) {
                            console.log('📱 Significant device movement detected - likely QR scanning');
                            setTimeout(() => {
                                if (!qrScanStatus.qr_scanned) {
                                    console.log('🎯 Auto detection triggered - device motion pattern');
                                    markQrCodeAsScanned();
                                }
                            }, 1500);
                            window.removeEventListener('devicemotion', handleDeviceMotion);
                        }
                    }
                }
            };
            
            window.addEventListener('devicemotion', handleDeviceMotion);
        }
    };

    // Monitor network activity - authenticator apps may sync or fetch time
    const monitorNetworkActivity = () => {
        console.log('🌐 Starting network activity monitoring...');
        
        // Monitor connection changes
        const handleConnectionChange = () => {
            if (autoDetection.isTracking && !qrScanStatus.qr_scanned) {
                console.log('📡 Network connection state changed - possible app activity');
                setTimeout(() => {
                    if (!qrScanStatus.qr_scanned) {
                        console.log('🎯 Auto detection triggered - network activity');
                        markQrCodeAsScanned();
                    }
                }, 3000);
            }
        };
        
        window.addEventListener('online', handleConnectionChange);
        window.addEventListener('offline', handleConnectionChange);
        
        // Monitor for fetch/xhr activity (apps making requests)
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            if (autoDetection.isTracking && !qrScanStatus.qr_scanned) {
                console.log('🌐 Network request detected - possible authenticator app activity');
                setTimeout(() => {
                    if (!qrScanStatus.qr_scanned) {
                        console.log('🎯 Auto detection triggered - fetch activity');
                        markQrCodeAsScanned();
                    }
                }, 2000);
            }
            return originalFetch.apply(this, args);
        };
    };

    const checkAutoDetectionConditions = async () => {
        if (!autoDetection.isTracking || qrScanStatus.qr_scanned) return;

        console.log('🔄 Running auto detection check...');
        const now = Date.now();
        const timeElapsed = now - autoDetection.startTime;

        console.log(`Time elapsed: ${timeElapsed}ms, IsTracking: ${autoDetection.isTracking}`);

        // Auto-detect after 5 seconds if user hasn't returned (very quick)
        if (timeElapsed > 5000) {
            console.log('🎯 Auto detection triggered - time elapsed (5 seconds)');
            markQrCodeAsScanned();
            return;
        }

        // Check server status periodically in case user scanned from another device
        try {
            const response = await axiosInstance.get('/two-factor/qr-status');
            console.log('Server QR status check:', response.data);
            
            if (response.data.qr_scanned && !qrScanStatus.qr_scanned) {
                console.log('🎯 Auto detection triggered - server detected scan');
                setQrScanStatus({
                    ...response.data,
                    loading: false
                });
                stopAutoDetection();
            }
        } catch (error) {
            console.error('Error checking QR status:', error);
        }
    };



    const showMessage = (severity, summary, detail) => {
        toast.current?.show({ severity, summary, detail, life: 3000 });
    };

    const handleImageUpload = async (event) => {
        const file = event.target.files[0];
        if (!file) return;

        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            showMessage('error', 'Invalid File Type', 'Please select a valid image file (JPEG, PNG, GIF)');
            return;
        }

        const maxSize = 5 * 1024 * 1024;
        if (file.size > maxSize) {
            showMessage('error', 'File Too Large', 'Image size should be less than 5MB');
            return;
        }

        setImageLoading(true);
        setErrors({});

        try {
            const userId = localStorage.getItem('user_id');
            const formData = new FormData();
            formData.append('image', file);

            const response = await axiosInstance.post(`/users/${userId}/upload-image`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            console.log('Image upload response:', response.data);

            const newImageUrl = response.data.data?.image || response.data.image;
            setUserImage(newImageUrl);
            localStorage.setItem('user_image', newImageUrl);
            
            showMessage('success', 'Image Updated', 'Profile image updated successfully');
            await fetchUserData();

        } catch (error) {
            console.error('Error uploading image:', error);
            console.log('Error response:', error.response?.data);

            if (error.response?.data?.details) {
                setErrors(error.response.data.details);
            } else if (error.response?.data?.errors) {
                setErrors(error.response.data.errors);
            }

            showMessage('error', 'Upload Failed', error.response?.data?.message || 'Failed to upload image');
        } finally {
            setImageLoading(false);
        }
    };

    const handleUpdateProfile = async () => {
        setLoading(true);
        setErrors({});

        try {
            const userId = localStorage.getItem('user_id');

            console.log('Sending profile update data:', {
                name: name,
                email: email
            });

            const response = await axiosInstance.put(`/users/${userId}`, {
                name: name,
                email: email
            });

            console.log('Profile update response:', response.data);

            showMessage('success', 'Profile Updated', 'Changes saved successfully');
            setIsEditingProfile(false);
            setOriginalName(name);
            setOriginalEmail(email);
        } catch (error) {
            console.error('Error updating profile:', error);
            console.log('Error response:', error.response?.data);

            if (error.response?.data?.details) {
                setErrors(error.response.data.details);
            } else if (error.response?.data?.errors) {
                setErrors(error.response.data.errors);
            }

            showMessage('error', 'Update Failed', error.response?.data?.message || 'Failed to update profile');
        } finally {
            setLoading(false);
        }
    };

    const handleChangePassword = async () => {
        if (newPassword !== confirmPassword) {
            showMessage('error', 'Error', 'Passwords do not match');
            setErrors({ confirmPassword: 'Passwords do not match' });
            return;
        }

        setLoading(true);
        setErrors({});

        try {
            const userId = localStorage.getItem('user_id');

            const passwordData = {
                id: userId,
                current_password: currentPassword,
                new_password: newPassword,
                new_password_confirmation: confirmPassword
            };

            const response = await axiosInstance.post(`/users/change-password`, passwordData);

            console.log('Password change response:', response.data);

            showMessage('success', 'Password Changed', 'Password updated successfully');
            setIsEditingPassword(false);
            setCurrentPassword('');
            setNewPassword('');
            setConfirmPassword('');
        } catch (error) {
            console.error('Error changing password:', error);
            console.log('Error response:', error.response?.data);

            if (error.response?.data?.details) {
                setErrors(error.response.data.details);
            } else if (error.response?.data?.errors) {
                setErrors(error.response.data.errors);
            }

            showMessage('error', 'Update Failed', error.response?.data?.message || 'Failed to update password');
        } finally {
            setLoading(false);
        }
    };

    const handleTwoFactorSetup = async () => {
        setTwoFactorLoading(true);
        try {
            const response = await axiosInstance.post('/two-factor/setup');
            console.log('QR Code Setup Response:', response.data);
            console.log('QR URL:', response.data.qr_url);
            console.log('Secret Key:', response.data.secret);
            setQrCodeData(response.data);
            setSecretKey(response.data.secret);
            setShowSetup(true);
            setSetupStep(1); // Start with step 1 (QR code display)
            // Refresh QR scan status after generating new QR code
            fetchQrScanStatus();
            // Reset auto detection state
            setAutoDetection({
                isTracking: false,
                startTime: null,
                lastVisibilityChange: null,
                focusCount: 0,
                polling: null
            });
        } catch (error) {
            console.error('Error setting up two-factor:', error);
            showMessage('error', 'Setup Failed', error.response?.data?.message || 'Failed to setup two-factor authentication');
        } finally {
            setTwoFactorLoading(false);
        }
    };

    const handleTwoFactorVerify = async () => {
        if (!verificationCode || verificationCode.length !== 6) {
            showMessage('error', 'Invalid Code', 'Please enter a valid 6-digit code');
            return;
        }

        setTwoFactorLoading(true);
        try {
            await axiosInstance.post('/two-factor/verify', {
                code: verificationCode
            });
            
            showMessage('success', 'Success', 'Two-factor authentication enabled successfully');
            setShowSetup(false);
            setVerificationCode('');
            setQrCodeData(null);
            setSecretKey('');
            // setQrScanned(false); // Removed
            // stopQRScanning(); // Removed
            stopAutoDetection(); // Stop auto detection after successful verification
            await fetchTwoFactorStatus();
            await fetchQrScanStatus(); // Refresh QR status after successful verification
            setSetupStep(3); // Automatically progress to step 3
        } catch (error) {
            console.error('Error verifying two-factor:', error);
            showMessage('error', 'Verification Failed', error.response?.data?.message || 'Invalid verification code');
        } finally {
            setTwoFactorLoading(false);
        }
    };

    const handleTwoFactorDisable = async () => {
        if (!verificationCode || verificationCode.length !== 6) {
            showMessage('error', 'Invalid Code', 'Please enter a valid 6-digit code');
            return;
        }

        setTwoFactorLoading(true);
        try {
            await axiosInstance.post('/two-factor/disable', {
                code: verificationCode
            });
            
            showMessage('success', 'Success', 'Two-factor authentication disabled successfully');
            setShowDisable(false);
            setVerificationCode('');
            await fetchTwoFactorStatus();
        } catch (error) {
            console.error('Error disabling two-factor:', error);
            showMessage('error', 'Disable Failed', error.response?.data?.message || 'Invalid verification code');
        } finally {
            setTwoFactorLoading(false);
        }
    };

    const enterEditProfileMode = () => {
        setOriginalName(name);
        setOriginalEmail(email);
        setIsEditingProfile(true);
    };

    const cancelEditProfile = () => {
        setName(originalName);
        setEmail(originalEmail);
        setIsEditingProfile(false);
    };

    const enterEditImageMode = () => {
        setTimeout(() => {
            fileInputRef.current?.click();
        }, 100);
    };

    const renderTwoFactorSection = () => (
        <div className="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 rounded-3xl p-8 border border-white/20 shadow-2xl backdrop-blur-xl relative">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-30">
                <div className="w-full h-full" style={{
                    backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
                }}></div>
            </div>
            
            {/* Animated Background Elements */}
            <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
                <div className="absolute -top-20 -right-20 w-40 h-40 bg-gradient-to-br from-blue-500/10 to-purple-600/10 rounded-full blur-2xl animate-pulse"></div>
                <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-gradient-to-tr from-purple-500/10 to-pink-600/10 rounded-full blur-2xl animate-pulse delay-1000"></div>
            </div>

            <div className="relative z-10">
                <div className="flex items-center gap-4 mb-8">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-2xl relative overflow-hidden">
                        <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>
                        <i className="pi pi-shield text-white text-2xl relative z-10"></i>
                        <div className="absolute inset-0 bg-gradient-to-br from-transparent to-black/20"></div>
                    </div>
                    <div>
                        <h3 className="text-2xl font-bold text-white mb-2">Two-Factor Authentication</h3>
                        <p className="text-gray-300">Secure your account with an additional layer of protection</p>
                    </div>
                </div>

                {!showSetup && !showDisable && (
                    <div className="grid lg:grid-cols-2 gap-8">
                        {/* Left Column - Status & Actions */}
                        <div className="space-y-6">
                            {/* Status Card */}
                            <div className={`rounded-2xl p-6 border ${
                                twoFactorStatus.enabled 
                                    ? 'bg-gradient-to-r from-emerald-500/20 to-blue-500/20 border-emerald-400/30' 
                                    : 'bg-gradient-to-r from-amber-500/20 to-orange-500/20 border-amber-400/30'
                            }`}>
                                <div className="flex items-center gap-4">
                                    <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                                        twoFactorStatus.enabled 
                                            ? 'bg-emerald-500/20' 
                                            : 'bg-amber-500/20'
                                    }`}>
                                        <i className={`text-xl ${
                                            twoFactorStatus.enabled 
                                                ? 'pi pi-check-circle text-emerald-400' 
                                                : 'pi pi-exclamation-triangle text-amber-400'
                                        }`}></i>
                                    </div>
                                    <div>
                                        <h4 className={`text-lg font-semibold mb-1 ${
                                            twoFactorStatus.enabled ? 'text-white' : 'text-white'
                                        }`}>
                                            {twoFactorStatus.enabled ? '2FA is Enabled' : '2FA is Disabled'}
                                        </h4>
                                        <p className={`text-sm ${
                                            twoFactorStatus.enabled ? 'text-emerald-300' : 'text-amber-300'
                                        }`}>
                                            {twoFactorStatus.enabled 
                                                ? 'Your account is protected with two-factor authentication'
                                                : 'Enable 2FA to add an extra layer of security to your account'
                                            }
                                        </p>
                                    </div>
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="space-y-4">
                                {!twoFactorStatus.enabled ? (
                                    <Button
                                        label="Enable Two-Factor Authentication"
                                        icon="pi pi-shield"
                                        className="w-full py-4 text-lg font-semibold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 hover:from-blue-700 hover:via-purple-700 hover:to-pink-700 hover:scale-105 active:scale-95 border-0 shadow-2xl rounded-2xl transition-all duration-300 text-white"
                                        onClick={handleTwoFactorSetup}
                                        loading={twoFactorLoading}
                                    />
                                ) : (
                                    <Button
                                        label="Disable Two-Factor Authentication"
                                        icon="pi pi-lock-open"
                                        severity="danger"
                                        className="w-full py-4 text-lg font-semibold bg-gradient-to-r from-red-600 via-orange-600 to-pink-600 hover:from-red-700 hover:via-orange-700 hover:to-pink-700 hover:scale-105 active:scale-95 border-0 shadow-2xl rounded-2xl transition-all duration-300 text-white"
                                        onClick={() => setShowDisable(true)}
                                        loading={twoFactorLoading}
                                    />
                                )}
                            </div>

                            {/* Security Features */}
                            <div className="bg-gradient-to-r from-emerald-500/20 to-blue-500/20 rounded-2xl p-6 border border-emerald-400/30">
                                <div className="flex items-center gap-4 mb-4">
                                    <i className="pi pi-shield text-emerald-400 text-2xl"></i>
                                    <h4 className="text-lg font-bold text-white">Security Features</h4>
                                </div>
                                <div className="grid grid-cols-2 gap-3 text-sm">
                                    <div className="flex items-center gap-2 text-emerald-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>30-second codes</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-emerald-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>3 attempt limit</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-emerald-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>Auto-lock protection</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-emerald-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>Secure encryption</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Right Column - Information */}
                        <div className="space-y-6">
                            {/* How it Works */}
                            <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
                                <div className="flex items-start gap-4 mb-6">
                                    <div className="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i className="pi pi-info-circle text-blue-400 text-xl"></i>
                                    </div>
                                    <div>
                                        <h4 className="text-xl font-bold text-white mb-2">How WebAuthn Works</h4>
                                        <p className="text-gray-300 text-sm leading-relaxed">
                                            WebAuthn uses your device's built-in biometric sensors (Face ID, Touch ID, Windows Hello) for secure, passwordless authentication.
                                        </p>
                                    </div>
                                </div>
                                
                                <div className="space-y-3">
                                    <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl">
                                        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-xs">1</div>
                                        <span className="text-white text-sm">Click "Enable WebAuthn" button below</span>
                                    </div>
                                    <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl">
                                        <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-xs">2</div>
                                        <span className="text-white text-sm">Use Face ID, Touch ID, or PIN when prompted</span>
                                    </div>
                                    <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl">
                                        <div className="w-6 h-6 bg-pink-500 rounded-full flex items-center justify-center text-white font-bold text-xs">3</div>
                                        <span className="text-white text-sm">Done! Use biometrics for future logins</span>
                                    </div>
                                </div>
                            </div>

                            {/* Supported Devices */}
                            <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
                                <div className="flex items-start gap-4 mb-4">
                                    <div className="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i className="pi pi-fingerprint text-purple-400 text-xl"></i>
                                    </div>
                                    <div>
                                        <h4 className="text-lg font-bold text-white mb-2">Supported Devices</h4>
                                        <p className="text-gray-300 text-sm">Works with built-in biometric sensors</p>
                                    </div>
                                </div>
                                
                                <div className="grid grid-cols-2 gap-3 text-sm">
                                    <div className="flex items-center gap-2 text-purple-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>iPhone (Face ID/Touch ID)</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-purple-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>Android (Fingerprint)</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-purple-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>Windows (Hello)</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-purple-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>Mac (Touch ID)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Professional Step-by-Step Setup */}
                {showSetup && (
                    <div className="space-y-8">
                        {/* Step Progress Indicator */}
                        <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
                            <div className="flex items-center justify-between mb-6">
                                <h3 className="text-xl font-bold text-white">Setup Progress</h3>
                                <div className="text-sm text-gray-300">
                                    Step {setupStep} of 3
                                </div>
                            </div>
                            
                            {/* Progress Bar */}
                            <div className="w-full bg-white/10 rounded-full h-3 mb-6">
                                <div 
                                    className="bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 h-3 rounded-full transition-all duration-500 ease-out"
                                    style={{ width: `${(setupStep / 3) * 100}%` }}
                                ></div>
                            </div>
                            
                            {/* Step Indicators */}
                            <div className="flex items-center justify-between">
                                <div className={`flex items-center gap-3 ${setupStep >= 1 ? 'text-blue-400' : 'text-gray-500'}`}>
                                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                                        setupStep > 1 || (setupStep === 1 && qrScanStatus.qr_scanned)
                                            ? 'bg-emerald-500 text-white' 
                                            : setupStep >= 1 && autoDetection.isTracking
                                                ? 'bg-blue-500 text-white animate-pulse'
                                            : setupStep >= 1 
                                                ? 'bg-blue-500 text-white' 
                                                : 'bg-white/10 text-gray-400'
                                    }`}>
                                        {setupStep > 1 || (setupStep === 1 && qrScanStatus.qr_scanned) ? <i className="pi pi-check text-xs"></i> : '1'}
                                    </div>
                                    <div className="flex flex-col">
                                        <span className="text-sm font-medium">QR Code Scan</span>
                                        {setupStep === 1 && (
                                            <span className={`text-xs ${
                                                qrScanStatus.qr_scanned 
                                                    ? 'text-emerald-400' 
                                                    : autoDetection.isTracking
                                                        ? 'text-blue-400'
                                                        : 'text-amber-400'
                                            }`}>
                                                                                                    {qrScanStatus.qr_scanned 
                                                        ? 'Auto-Detected ✓' 
                                                        : autoDetection.isTracking
                                                            ? 'AI Monitoring...'
                                                            : 'Ready...'}
                                                </span>
                                        )}
                                    </div>
                                </div>
                                
                                <div className={`flex-1 h-0.5 mx-4 ${
                                    setupStep >= 2 ? 'bg-blue-500' : 'bg-white/10'
                                }`}></div>
                                
                                <div className={`flex items-center gap-3 ${setupStep >= 2 ? 'text-purple-400' : 'text-gray-500'}`}>
                                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                                        setupStep >= 2 
                                            ? 'bg-purple-500 text-white' 
                                            : 'bg-white/10 text-gray-400'
                                    }`}>
                                        {setupStep > 2 ? <i className="pi pi-check text-xs"></i> : '2'}
                                    </div>
                                    <span className="text-sm font-medium">Verification</span>
                                </div>
                                
                                <div className={`flex-1 h-0.5 mx-4 ${
                                    setupStep >= 3 ? 'bg-purple-500' : 'bg-white/10'
                                }`}></div>
                                
                                <div className={`flex items-center gap-3 ${setupStep >= 3 ? 'text-pink-400' : 'text-gray-500'}`}>
                                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                                        setupStep >= 3 
                                            ? 'bg-pink-500 text-white' 
                                            : 'bg-white/10 text-gray-400'
                                    }`}>
                                        {setupStep > 3 ? <i className="pi pi-check text-xs"></i> : '3'}
                                    </div>
                                    <span className="text-sm font-medium">Complete</span>
                                </div>
                            </div>
                        </div>

                        {/* Step 1: QR Code Scan */}
                        {setupStep === 1 && (
                            <div className="space-y-6">
                        <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-400/30 rounded-2xl p-6">
                            <div className="flex items-start gap-4">
                                <div className="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                            <i className="pi pi-qrcode text-blue-400 text-xl"></i>
                                </div>
                                <div className="flex-1">
                                            <h3 className="text-xl font-semibold text-white mb-3">Step 1: Scan QR Code</h3>
                                    <div className="space-y-3">
                                        <div className="flex items-center gap-3 p-3 bg-white/10 rounded-xl">
                                            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-sm">1</div>
                                            <span className="text-white">Open your authenticator app (Google Authenticator, Authy, etc.)</span>
                                        </div>
                                        <div className="flex items-center gap-3 p-3 bg-white/10 rounded-xl">
                                            <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-sm">2</div>
                                            <span className="text-white">Scan the QR code below with your app</span>
                                        </div>
                                        <div className="flex items-center gap-3 p-3 bg-white/10 rounded-xl">
                                            <div className="w-8 h-8 bg-pink-500 rounded-full flex items-center justify-center text-white font-bold text-sm">3</div>
                                            <span className="text-white">
                                                Enter the 6-digit code below to verify successful scanning
                                            </span>
                                        </div>
                                        
                                        <div className="bg-gradient-to-r from-emerald-500/20 to-blue-500/20 border border-emerald-400/30 rounded-xl p-4 mt-4">
                                            <div className="flex items-center gap-2 mb-2">
                                                <i className="pi pi-qrcode text-emerald-400"></i>
                                                <span className="text-emerald-300 text-sm font-medium">Scan Verification Required</span>
                                            </div>
                                            <p className="text-emerald-200 text-sm leading-relaxed">
                                                After scanning the QR code with your authenticator app, please enter the 6-digit code below to verify that the setup was successful before enabling 2FA.
                                            </p>
                                        </div>
                                        
                                        {/* Smart Auto Detection Status */}
                                        <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-400/30 rounded-xl p-4 mt-4">
                                            <div className="flex items-center gap-2 mb-2">
                                                <i className="pi pi-eye text-blue-400 animate-pulse"></i>
                                                <span className="text-blue-300 text-sm font-medium">Ultra Smart Detection Active</span>
                                            </div>
                                            <p className="text-blue-200 text-sm leading-relaxed">
                                                Next-generation AI detection system monitors 7+ advanced indicators: page visibility, window focus, camera access, battery usage, device motion, network activity, and touch patterns. Simply scan the QR code with your authenticator app and the system will detect it instantly using military-grade algorithms!
                                            </p>
                                            <div className="mt-3 flex flex-wrap gap-2 justify-center">
                                                <div className="inline-flex items-center gap-1 bg-blue-600/20 px-2 py-1 rounded-full">
                                                    <div className="w-1.5 h-1.5 bg-blue-400 rounded-full animate-pulse"></div>
                                                    <span className="text-blue-300 text-xs">Page Monitor</span>
                                                </div>
                                                <div className="inline-flex items-center gap-1 bg-purple-600/20 px-2 py-1 rounded-full">
                                                    <div className="w-1.5 h-1.5 bg-purple-400 rounded-full animate-pulse"></div>
                                                    <span className="text-purple-300 text-xs">Focus Tracking</span>
                                                </div>
                                                <div className="inline-flex items-center gap-1 bg-pink-600/20 px-2 py-1 rounded-full">
                                                    <div className="w-1.5 h-1.5 bg-pink-400 rounded-full animate-pulse"></div>
                                                    <span className="text-pink-300 text-xs">Touch Pattern</span>
                                                </div>
                                                <div className="inline-flex items-center gap-1 bg-emerald-600/20 px-2 py-1 rounded-full">
                                                    <div className="w-1.5 h-1.5 bg-emerald-400 rounded-full animate-pulse"></div>
                                                    <span className="text-emerald-300 text-xs">Camera API</span>
                                                </div>
                                                <div className="inline-flex items-center gap-1 bg-yellow-600/20 px-2 py-1 rounded-full">
                                                    <div className="w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse"></div>
                                                    <span className="text-yellow-300 text-xs">Battery Monitor</span>
                                                </div>
                                                <div className="inline-flex items-center gap-1 bg-indigo-600/20 px-2 py-1 rounded-full">
                                                    <div className="w-1.5 h-1.5 bg-indigo-400 rounded-full animate-pulse"></div>
                                                    <span className="text-indigo-300 text-xs">Motion Sensor</span>
                                                </div>
                                                <div className="inline-flex items-center gap-1 bg-cyan-600/20 px-2 py-1 rounded-full">
                                                    <div className="w-1.5 h-1.5 bg-cyan-400 rounded-full animate-pulse"></div>
                                                    <span className="text-cyan-300 text-xs">Network Activity</span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        {/* QR Scan Status Indicator */}
                                        <div className={`flex items-center gap-3 p-3 rounded-xl mt-4 ${
                                            qrScanStatus.qr_scanned 
                                                ? 'bg-emerald-500/20 border border-emerald-400/30' 
                                                : autoDetection.isTracking
                                                    ? 'bg-blue-500/20 border border-blue-400/30'
                                                    : 'bg-amber-500/20 border border-amber-400/30'
                                        }`}>
                                            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                                                qrScanStatus.qr_scanned 
                                                    ? 'bg-emerald-500' 
                                                    : autoDetection.isTracking
                                                        ? 'bg-blue-500 animate-pulse'
                                                        : 'bg-amber-500'
                                            }`}>
                                                <i className={`text-white text-sm ${
                                                    qrScanStatus.qr_scanned 
                                                        ? 'pi pi-check' 
                                                        : autoDetection.isTracking
                                                            ? 'pi pi-eye'
                                                            : 'pi pi-clock'
                                                }`}></i>
                                            </div>
                                            <div className="flex flex-col">
                                                <span className={`text-sm font-medium ${
                                                    qrScanStatus.qr_scanned 
                                                        ? 'text-emerald-300' 
                                                        : autoDetection.isTracking
                                                            ? 'text-blue-300'
                                                            : 'text-amber-300'
                                                }`}>
                                                    {qrScanStatus.qr_scanned 
                                                        ? '🎉 QR code automatically detected and verified!' 
                                                        : autoDetection.isTracking
                                                            ? '🤖 Smart detection system is actively monitoring...'
                                                            : '⏳ Ready to scan QR code...'}
                                                </span>
                                                {autoDetection.isTracking && !qrScanStatus.qr_scanned && (
                                                    <span className="text-xs text-blue-400 mt-1">
                                                        ⚡ Just scan the QR code - no button clicks needed!
                                                    </span>
                                                )}
                                                {qrScanStatus.qr_scanned && (
                                                    <span className="text-xs text-emerald-400 mt-1">
                                                        ✅ Setup confirmed - ready to enable 2FA
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                        
                                        {/* Removed Camera scanning indicator */}
                                        
                                        {/* Removed Scanning error */}
                                        
                                        {/* Hidden video and canvas elements for QR scanning */}
                                        <div className="hidden">
                                            <video 
                                                // ref={videoRef} 
                                                autoPlay 
                                                playsInline 
                                                muted 
                                                style={{ width: '1px', height: '1px' }}
                                            />
                                            <canvas 
                                                // ref={canvasRef} 
                                                style={{ display: 'none' }}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* QR Code Section */}
                        {qrCodeData && (
                            <div className="flex justify-center">
                                <div className="bg-white/10 backdrop-blur-xl p-8 rounded-2xl border-2 border-white/20 shadow-2xl relative overflow-hidden">
                                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10"></div>
                                    <div className="relative">
                                        <div className="text-center mb-4">
                                            <h4 className="font-semibold text-white mb-1">QR Code</h4>
                                            <p className="text-xs text-gray-300">Scan with your authenticator app</p>
                                        </div>
                                        <div className="bg-white p-6 rounded-xl border shadow-lg">
                                            <QRCodeSVG 
                                                value={`otpauth://totp/InkNull:${email}?secret=${secretKey}&issuer=InkNull`}
                                                size={280}
                                                level="M"
                                                includeMargin={true}
                                            />
                                        </div>
                                    </div>
                                </div>
                                

                            </div>
                        )}

                        {/* Manual Entry Section */}
                        <div className="bg-gradient-to-r from-gray-500/20 to-blue-500/20 border border-gray-400/30 rounded-2xl p-6">
                            <div className="flex items-start gap-4">
                                <div className="w-12 h-12 bg-gray-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                    <i className="pi pi-key text-gray-400 text-xl"></i>
                                </div>
                                <div className="flex-1">
                                    <h3 className="text-lg font-semibold text-white mb-2">Manual Entry (Alternative)</h3>
                                    <p className="text-gray-300 mb-4">
                                        If QR code scanning doesn&apos;t work, you can manually enter the setup key in your authenticator app
                                    </p>
                                    
                                    <div className="bg-white/10 backdrop-blur-xl rounded-lg p-4 border border-white/20">
                                        <div className="flex items-center gap-3">
                                            <div className="flex-1">
                                                <label className="text-xs font-medium text-gray-300 mb-2 block">Setup Key:</label>
                                                <div className="font-mono text-sm bg-white/10 p-3 rounded-lg border border-white/20 break-all text-white">
                                                    {secretKey}
                                                </div>
                                            </div>
                                            <Button 
                                                icon="pi pi-copy" 
                                                className="p-button-sm p-button-outlined bg-white/10 border-white/20 text-white hover:bg-white/20"
                                                onClick={() => {
                                                    navigator.clipboard.writeText(secretKey);
                                                    showMessage('success', 'Copied!', 'Setup key copied to clipboard');
                                                }}
                                                tooltip="Copy to clipboard"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                                {/* Step 1 Action Buttons */}
                                <div className="flex gap-4 justify-end pt-4 border-t border-white/20">
                                    <Button 
                                        label="Cancel Setup" 
                                        severity="secondary" 
                                        className="px-6 py-3 bg-gradient-to-r from-gray-500/20 to-gray-600/20 border border-gray-400/30 text-white hover:from-gray-600/30 hover:to-gray-700/30 hover:scale-105 active:scale-95 transition-all duration-300 rounded-xl"
                                        onClick={() => {
                                            stopAutoDetection();
                                            setShowSetup(false);
                                            setQrCodeData(null);
                                            setSecretKey('');
                                            setVerificationCode('');
                                            setSetupStep(1);
                                            setQrScanStatus({
                                                qr_generated: false,
                                                qr_scanned: false,
                                                qr_scanned_at: null,
                                                loading: false
                                            });
                                            // setQrScanned(false); // Removed
                                            // stopQRScanning(); // Removed
                                        }}
                                    />
                                    <Button 
                                        label="Next Step" 
                                        icon="pi pi-arrow-right"
                                        className={`px-6 py-3 border-0 hover:scale-105 active:scale-95 transition-all duration-300 rounded-xl text-white font-semibold shadow-xl ${
                                            qrScanStatus.qr_scanned 
                                                ? 'bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 hover:from-blue-700 hover:via-purple-700 hover:to-pink-700' 
                                                : 'bg-gradient-to-r from-gray-500 to-gray-600 cursor-not-allowed opacity-50'
                                        }`}
                                        onClick={() => {
                                            if (qrScanStatus.qr_scanned) {
                                                setSetupStep(2);
                                                fetchQrScanStatus(); // Refresh status when moving to step 2
                                            }
                                        }}
                                        disabled={!qrScanStatus.qr_scanned}
                                        tooltip={!qrScanStatus.qr_scanned ? "Please scan the QR code first" : ""}
                                        tooltipOptions={{position: 'top'}}
                                    />
                                </div>
                            </div>
                        )}

                        {/* Step 2: Verification */}
                        {setupStep === 2 && (
                            <div className="space-y-6">
                                <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-400/30 rounded-2xl p-6">
                            <div className="flex items-start gap-4">
                                        <div className="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                            <i className="pi pi-check-circle text-purple-400 text-xl"></i>
                                </div>
                                <div className="flex-1">
                                            <h3 className="text-xl font-semibold text-white mb-3">Step 2: Verify Setup</h3>
                                            <p className="text-purple-300 mb-4">
                                                Enter the 6-digit code from your authenticator app to verify the setup is working correctly
                                    </p>
                                    
                                            <div className="bg-white/10 backdrop-blur-xl rounded-lg p-6 border border-white/20">
                                                <div className="space-y-4">
                                            <div>
                                                        <label className="text-sm font-medium text-white block mb-3">Verification Code:</label>
                                                <InputText
                                                    value={verificationCode}
                                                    onChange={(e) => setVerificationCode(e.target.value)}
                                                    placeholder="000000"
                                                    maxLength={6}
                                                            className="w-full text-center text-3xl font-mono tracking-widest border-2 border-purple-400/50 rounded-xl p-4 focus:border-purple-400 focus:ring-4 focus:ring-purple-400/20 bg-white/10 backdrop-blur-sm text-white placeholder-gray-400"
                                                    keyfilter="int"
                                                            autoFocus
                                                />
                                            </div>
                                                    <div className="flex items-center gap-2 text-sm text-purple-300">
                                                <i className="pi pi-clock"></i>
                                                <span>Code refreshes every 30 seconds</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                                {/* Step 2 Action Buttons */}
                        <div className="flex gap-4 justify-end pt-4 border-t border-white/20">
                            <Button 
                                        label="Previous Step" 
                                        icon="pi pi-arrow-left"
                                severity="secondary" 
                                className="px-6 py-3 bg-gradient-to-r from-gray-500/20 to-gray-600/20 border border-gray-400/30 text-white hover:from-gray-600/30 hover:to-gray-700/30 hover:scale-105 active:scale-95 transition-all duration-300 rounded-xl"
                                        onClick={() => setSetupStep(1)}
                                    />
                                    <Button 
                                        label="Verify & Continue" 
                                        icon="pi pi-check"
                                        className="px-6 py-3 bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 hover:from-purple-700 hover:via-pink-700 hover:to-red-700 border-0 hover:scale-105 active:scale-95 transition-all duration-300 rounded-xl text-white font-semibold shadow-xl"
                                        onClick={handleTwoFactorVerify}
                                        loading={twoFactorLoading}
                                        disabled={!verificationCode || verificationCode.length !== 6}
                                    />
                                </div>
                            </div>
                        )}

                        {/* Step 3: Completion */}
                        {setupStep === 3 && (
                            <div className="space-y-6">
                                <div className="bg-gradient-to-r from-emerald-500/20 to-green-500/20 border border-emerald-400/30 rounded-2xl p-6">
                                    <div className="flex items-start gap-4">
                                        <div className="w-12 h-12 bg-emerald-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                            <i className="pi pi-check-circle text-emerald-400 text-xl"></i>
                                        </div>
                                        <div className="flex-1">
                                            <h3 className="text-xl font-semibold text-white mb-3">Step 3: Setup Complete!</h3>
                                            <p className="text-emerald-300 mb-4">
                                                Congratulations! Your two-factor authentication has been successfully enabled. Your account is now protected with an additional layer of security.
                                            </p>
                                            
                                            <div className="bg-white/10 backdrop-blur-xl rounded-lg p-6 border border-white/20">
                                                <div className="space-y-4">
                                                    <div className="flex items-center gap-3 text-emerald-300">
                                                        <i className="pi pi-check-circle"></i>
                                                        <span>QR code scanned successfully</span>
                                                    </div>
                                                    <div className="flex items-center gap-3 text-emerald-300">
                                                        <i className="pi pi-check-circle"></i>
                                                        <span>Verification code confirmed</span>
                                                    </div>
                                                    <div className="flex items-center gap-3 text-emerald-300">
                                                        <i className="pi pi-check-circle"></i>
                                                        <span>2FA enabled for your account</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Step 3 Action Buttons */}
                                <div className="flex gap-4 justify-end pt-4 border-t border-white/20">
                                    <Button 
                                        label="Finish Setup" 
                                        icon="pi pi-check"
                                        className="px-6 py-3 bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 border-0 hover:scale-105 active:scale-95 transition-all duration-300 rounded-xl text-white font-semibold shadow-xl"
                                                                        onClick={() => {
                                    stopAutoDetection();
                                    setShowSetup(false);
                                    setQrCodeData(null);
                                    setSecretKey('');
                                    setVerificationCode('');
                                            setSetupStep(1);
                                            setQrScanStatus({
                                                qr_generated: false,
                                                qr_scanned: false,
                                                qr_scanned_at: null,
                                                loading: false
                                            });
                                            // setQrScanned(false); // Removed
                                            // stopQRScanning(); // Removed
                                        }}
                            />
                        </div>
                            </div>
                        )}
                    </div>
                )}

                {/* Disable Section */}
                {showDisable && (
                    <div className="space-y-6">
                        <div className="bg-gradient-to-r from-red-500/20 to-orange-500/20 border border-red-400/30 rounded-2xl p-6">
                            <div className="flex items-start gap-4">
                                <div className="w-12 h-12 bg-red-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                    <i className="pi pi-exclamation-triangle text-red-400 text-xl"></i>
                                </div>
                                <div className="flex-1">
                                    <h3 className="text-lg font-semibold text-white mb-2">Security Warning</h3>
                                    <p className="text-red-300 mb-4">
                                        Disabling two-factor authentication will significantly reduce the security of your account. 
                                        This action should only be performed if you&apos;re experiencing issues with your authenticator app.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div className="bg-gradient-to-r from-gray-500/20 to-blue-500/20 border border-gray-400/30 rounded-2xl p-6">
                            <div className="flex items-start gap-4">
                                <div className="w-12 h-12 bg-gray-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                    <i className="pi pi-key text-gray-400 text-xl"></i>
                                </div>
                                <div className="flex-1">
                                    <h3 className="text-lg font-semibold text-white mb-2">Verification Required</h3>
                                    <p className="text-gray-300 mb-4">
                                        To confirm this action, please enter the current 6-digit code from your authenticator app
                                    </p>
                                    
                                    <div className="bg-white/10 backdrop-blur-xl rounded-lg p-4 border border-white/20">
                                        <div className="space-y-3">
                                            <div>
                                                <label className="text-sm font-medium text-white block mb-2">Current Code:</label>
                                                <InputText
                                                    value={verificationCode}
                                                    onChange={(e) => setVerificationCode(e.target.value)}
                                                    placeholder="000000"
                                                    maxLength={6}
                                                    className="w-full text-center text-2xl font-mono tracking-widest border-2 border-red-400/50 rounded-lg p-4 focus:border-red-400 focus:ring-2 focus:ring-red-400/20 bg-white/10 backdrop-blur-sm text-white placeholder-gray-400"
                                                    keyfilter="int"
                                                />
                                            </div>
                                            <div className="flex items-center gap-2 text-sm text-gray-300">
                                                <i className="pi pi-clock"></i>
                                                <span>Code refreshes every 30 seconds</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="flex gap-4 justify-end pt-4 border-t border-white/20">
                            <Button 
                                label="Keep 2FA Enabled" 
                                severity="secondary" 
                                className="px-6 py-3 bg-gradient-to-r from-gray-500/20 to-gray-600/20 border border-gray-400/30 text-white hover:from-gray-600/30 hover:to-gray-700/30 hover:scale-105 active:scale-95 transition-all duration-300 rounded-xl"
                                onClick={() => {
                                    setShowDisable(false);
                                    setVerificationCode('');
                                }}
                            />
                            <Button 
                                label="Disable 2FA" 
                                severity="danger"
                                className="px-6 py-3 bg-gradient-to-r from-red-600 via-orange-600 to-pink-600 hover:from-red-700 hover:via-orange-700 hover:to-pink-700 border-0 hover:scale-105 active:scale-95 transition-all duration-300 rounded-xl text-white font-semibold shadow-xl"
                                onClick={handleTwoFactorDisable}
                                loading={twoFactorLoading}
                                disabled={!verificationCode || verificationCode.length !== 6}
                            />
                        </div>
                    </div>
                )}
            </div>
        </div>
    );

    return (
        <section className='w-full flex flex-col p-5 h-[95vh] overflow-y-auto bg-white' >
            <Toast ref={toast} />

            <div className="w-full">
                <div className="mb-8 text-left">
                    <h2 className="text-3xl font-bold text-gray-800 flex items-center justify-start gap-3">
                        <i className="pi pi-user bg-blue-100 rounded-full text-blue-600 text-3xl"></i>
                        Account Settings
                    </h2>
                    <p className="text-gray-500 mt-2">Manage your personal information and security preferences</p>
                </div>

                <div className="bg-white rounded-xl shadow-md p-8 mb-8 transition-all hover:shadow-lg border border-gray-100">
                    <div className="flex flex-col md:flex-row gap-8 mb-4">
                        <div className="flex flex-col items-center">
                            <div className="relative group">
                                <div className="w-[130px] h-[130px] rounded-full border-4 border-blue-50 shadow-lg overflow-hidden bg-gradient-to-br from-blue-50 to-purple-50 relative">
                                    <Image 
                                        src={userImage ? (userImage.startsWith('http') ? userImage : `${API_URL}/storage/${userImage}`) : DEFAULT_USER_IMAGE} 
                                        alt="profile"
                                        imageClassName="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110" 
                                        width="130" 
                                        height="130"
                                        preview={false}
                                        onError={(e) => {
                                            e.target.src = DEFAULT_USER_IMAGE;
                                        }}
                                    />
                                    
                                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                    
                                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                                        <div className="bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-lg">
                                            <i className="pi pi-camera text-blue-600 text-lg"></i>
                                        </div>
                                    </div>
                                </div>
                                
                                <Button 
                                    icon="pi pi-camera" 
                                    className="p-button-rounded p-button-primary absolute -bottom-2 -right-2 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 border-2 border-white"
                                    tooltip="Change Photo" 
                                    tooltipOptions={{position: 'bottom'}}
                                    onClick={enterEditImageMode}
                                />
                                
                                {imageLoading && (
                                    <div className="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center">
                                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                                    </div>
                                )}
                                
                                <input
                                    ref={fileInputRef}
                                    type="file"
                                    accept="image/*"
                                    onChange={handleImageUpload}
                                    className="hidden"
                                />
                            </div>
                        </div>
                        <div className="flex-1">
                            {!isEditingProfile ? (
                                <>
                                    <div className="flex justify-between items-center mb-6">
                                        <h3 className="text-xl font-semibold text-gray-800">Profile Information</h3>
                                        <Button icon="pi pi-pencil"
                                            className="p-button-rounded p-button-outlined p-button-primary"
                                            onClick={enterEditProfileMode} />
                                    </div>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="field bg-white p-4 rounded-lg">
                                            <label className="text-sm font-medium text-gray-500 block mb-1">Name</label>
                                            <p className="text-gray-800 font-medium">{name || 'No name provided'}</p>
                                        </div>
                                        <div className="field bg-white p-4 rounded-lg">
                                            <label className="text-sm font-medium text-gray-500 block mb-1">Email</label>
                                            <p className="text-gray-800 font-medium">{email || 'No email provided'}</p>
                                        </div>
                                    </div>
                                </>
                            ) : (
                                <>
                                    <div className="flex justify-between items-center mb-6">
                                        <h3 className="text-xl font-semibold text-gray-800">Edit Profile</h3>
                                        <Button icon="pi pi-times"
                                            className="p-button-rounded p-button-outlined p-button-danger"
                                            onClick={cancelEditProfile} />
                                    </div>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="field">
                                            <label className="text-sm font-medium text-gray-700 block mb-2">Name</label>
                                            <InputText
                                                value={name}
                                                onChange={(e) => setName(e.target.value)}
                                                className={`w-full p-inputtext-sm p-3 rounded-lg border ${errors.name ? 'border-red-500' : 'border-gray-300'}`}
                                            />
                                            {errors.name && <small className="p-error block mt-1">{errors.name}</small>}
                                        </div>
                                        <div className="field">
                                            <label className="text-sm font-medium text-gray-700 block mb-2">Email</label>
                                            <InputText
                                                type="email"
                                                value={email}
                                                onChange={(e) => setEmail(e.target.value)}
                                                className={`w-full p-inputtext-sm p-3 rounded-lg border ${errors.email ? 'border-red-500' : 'border-gray-300'}`}
                                            />
                                            {errors.email && <small className="p-error block mt-1">{errors.email}</small>}
                                        </div>
                                    </div>
                                    <div className="flex gap-3 justify-end mt-6">
                                        <Button label="Cancel" severity="secondary" className="p-button-sm"
                                            onClick={cancelEditProfile} />
                                        <Button label="Save Changes" className="p-button-sm main-btn"
                                            onClick={handleUpdateProfile}
                                            loading={loading} />
                                    </div>
                                </>
                            )}
                        </div>
                    </div>
                </div>

                <div className="w-full border-t border-gray-200 my-10 mx-0"></div>

                <div className="text-left mb-8">
                    <h2 className="text-3xl font-bold text-gray-800 flex items-center justify-start gap-3">
                        <i className="pi pi-lock bg-purple-100 rounded-full text-purple-600 text-3xl"></i>
                        Security Settings
                    </h2>
                    <p className="text-gray-500 mt-2">Protect your account with strong security measures</p>
                </div>

                {/* WebAuthn Section */}
                <div className="bg-gradient-to-br from-slate-900 via-indigo-900 to-slate-900 rounded-3xl p-8 border border-white/20 shadow-2xl backdrop-blur-xl relative mb-8">
                    {/* Background Pattern */}
                    <div className="absolute inset-0 opacity-30">
                        <div className="w-full h-full" style={{
                            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M30 30m-20 0a20 20 0 1 1 40 0a20 20 0 1 1 -40 0'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
                        }}></div>
                    </div>
                    
                    {/* Animated Background Elements */}
                    <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
                        <div className="absolute -top-20 -right-20 w-40 h-40 bg-gradient-to-br from-indigo-500/10 to-blue-600/10 rounded-full blur-2xl animate-pulse"></div>
                        <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-gradient-to-tr from-blue-500/10 to-cyan-600/10 rounded-full blur-2xl animate-pulse delay-1000"></div>
                    </div>

                    <div className="relative z-10">
                        <div className="flex items-center gap-4 mb-8">
                            <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 via-blue-500 to-cyan-500 rounded-full flex items-center justify-center shadow-2xl relative overflow-hidden">
                                <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>
                                <i className="pi pi-fingerprint text-white text-2xl relative z-10"></i>
                                <div className="absolute inset-0 bg-gradient-to-br from-transparent to-black/20"></div>
                            </div>
                            <div>
                                <h3 className="text-2xl font-bold text-white mb-2">WebAuthn / Passkeys</h3>
                                <p className="text-gray-300">Secure your account with biometric authentication - no passwords needed!</p>
                            </div>
                        </div>

                        <div className="grid lg:grid-cols-2 gap-8">
                            {/* Left Column - Status & Actions */}
                            <div className="space-y-6">
                                {/* Status Card */}
                                <div className={`rounded-2xl p-6 border ${
                                    webAuthnStatus.enabled 
                                        ? 'bg-gradient-to-r from-emerald-500/20 to-blue-500/20 border-emerald-400/30' 
                                        : 'bg-gradient-to-r from-amber-500/20 to-orange-500/20 border-amber-400/30'
                                }`}>
                                    <div className="flex items-center gap-4">
                                        <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                                            webAuthnStatus.enabled 
                                                ? 'bg-emerald-500/20' 
                                                : 'bg-amber-500/20'
                                        }`}>
                                            <i className={`text-xl ${
                                                webAuthnStatus.enabled 
                                                    ? 'pi pi-check-circle text-emerald-400' 
                                                    : 'pi pi-exclamation-triangle text-amber-400'
                                            }`}></i>
                                        </div>
                                        <div>
                                            <h4 className="text-lg font-semibold mb-1 text-white">
                                                {webAuthnStatus.enabled ? 'WebAuthn is Enabled' : 'WebAuthn is Disabled'}
                                            </h4>
                                            <p className={`text-sm ${
                                                webAuthnStatus.enabled ? 'text-emerald-300' : 'text-amber-300'
                                            }`}>
                                                {webAuthnStatus.enabled 
                                                    ? `Your account is protected with ${webAuthnStatus.keys_count} biometric key(s)`
                                                    : 'Enable WebAuthn for passwordless, secure authentication'
                                                }
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                {/* Action Buttons */}
                                <div className="space-y-4">
                                    {!webAuthnStatus.enabled ? (
                                        <Button
                                            label="Enable WebAuthn"
                                            icon="pi pi-fingerprint"
                                            className="w-full py-4 text-lg font-semibold bg-gradient-to-r from-indigo-600 via-blue-600 to-cyan-600 hover:from-indigo-700 hover:via-blue-700 hover:to-cyan-700 hover:scale-105 active:scale-95 border-0 shadow-2xl rounded-2xl transition-all duration-300 text-white"
                                            onClick={enableWebAuthn}
                                            loading={webAuthnLoading}
                                        />
                                    ) : (
                                        <Button
                                            label="Disable WebAuthn"
                                            icon="pi pi-lock-open"
                                            severity="danger"
                                            className="w-full py-4 text-lg font-semibold bg-gradient-to-r from-red-600 via-orange-600 to-pink-600 hover:from-red-700 hover:via-orange-700 hover:to-pink-700 hover:scale-105 active:scale-95 border-0 shadow-2xl rounded-2xl transition-all duration-300 text-white"
                                            onClick={disableWebAuthn}
                                            loading={webAuthnLoading}
                                        />
                                    )}
                                </div>

                                {/* Registered Keys */}
                                {webAuthnStatus.enabled && webAuthnStatus.keys.length > 0 && (
                                    <div className="bg-gradient-to-r from-blue-500/20 to-indigo-500/20 rounded-2xl p-6 border border-blue-400/30">
                                        <div className="flex items-center gap-4 mb-4">
                                            <i className="pi pi-mobile text-blue-400 text-2xl"></i>
                                            <h4 className="text-lg font-bold text-white">Registered Devices</h4>
                                        </div>
                                        <div className="space-y-3">
                                            {webAuthnStatus.keys.map((key, index) => (
                                                <div key={key.id} className="flex items-center justify-between bg-white/10 rounded-lg p-3">
                                                    <div className="flex items-center gap-3">
                                                        <i className="pi pi-mobile text-blue-300"></i>
                                                        <div>
                                                            <p className="text-white font-medium">{key.name}</p>
                                                            <p className="text-blue-300 text-sm">
                                                                Added {new Date(key.created_at).toLocaleDateString()}
                                                            </p>
                                                        </div>
                                                    </div>
                                                    {key.last_used_at && (
                                                        <span className="text-xs text-blue-400">
                                                            Last used: {new Date(key.last_used_at).toLocaleDateString()}
                                                        </span>
                                                    )}
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>

                            {/* Right Column - Information */}
                            <div className="space-y-6">
                                {/* How it Works */}
                                <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
                                    <div className="flex items-start gap-4 mb-6">
                                        <div className="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                            <i className="pi pi-info-circle text-blue-400 text-xl"></i>
                                        </div>
                                        <div>
                                            <h4 className="text-xl font-bold text-white mb-2">How WebAuthn Works</h4>
                                            <p className="text-gray-300 text-sm leading-relaxed">
                                                WebAuthn uses your device's built-in biometric sensors (Face ID, Touch ID, Windows Hello) for secure, passwordless authentication.
                                            </p>
                                        </div>
                                    </div>
                                    
                                    <div className="space-y-3">
                                        <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl">
                                            <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-xs">1</div>
                                            <span className="text-white text-sm">Click "Enable WebAuthn" button below</span>
                                        </div>
                                        <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl">
                                            <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-xs">2</div>
                                            <span className="text-white text-sm">Use Face ID, Touch ID, or PIN when prompted</span>
                                        </div>
                                        <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl">
                                            <div className="w-6 h-6 bg-pink-500 rounded-full flex items-center justify-center text-white font-bold text-xs">3</div>
                                            <span className="text-white text-sm">Done! Use biometrics for future logins</span>
                                        </div>
                                    </div>
                                </div>

                                {/* Supported Devices */}
                                <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
                                    <div className="flex items-start gap-4 mb-4">
                                        <div className="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                            <i className="pi pi-fingerprint text-purple-400 text-xl"></i>
                                        </div>
                                        <div>
                                            <h4 className="text-lg font-bold text-white mb-2">Supported Devices</h4>
                                            <p className="text-gray-300 text-sm">Works with built-in biometric sensors</p>
                                        </div>
                                    </div>
                                    
                                    <div className="grid grid-cols-2 gap-3 text-sm">
                                        <div className="flex items-center gap-2 text-purple-300">
                                            <i className="pi pi-check-circle"></i>
                                            <span>iPhone (Face ID/Touch ID)</span>
                                        </div>
                                        <div className="flex items-center gap-2 text-purple-300">
                                            <i className="pi pi-check-circle"></i>
                                            <span>Android (Fingerprint)</span>
                                        </div>
                                        <div className="flex items-center gap-2 text-purple-300">
                                            <i className="pi pi-check-circle"></i>
                                            <span>Windows (Hello)</span>
                                        </div>
                                        <div className="flex items-center gap-2 text-purple-300">
                                            <i className="pi pi-check-circle"></i>
                                            <span>Mac (Touch ID)</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {renderTwoFactorSection()}

                <div className="mt-8">
                    <div className="bg-white rounded-xl shadow-md p-8 transition-all hover:shadow-lg border border-gray-100">
                    {!isEditingPassword ? (
                        <div className="flex justify-between items-center">
                            <div className="text-left">
                                <h3 className="text-xl font-semibold text-gray-800 mb-2">Password</h3>
                                <p className="text-gray-500">Secure your account with a strong password</p>
                            </div>
                            <Button label="Change Password" icon="pi pi-lock"
                                className="p-button-outlined p-button-primary"
                                onClick={() => setIsEditingPassword(true)} />
                        </div>
                    ) : (
                        <>
                            <div className="flex justify-between items-center mb-6">
                                <h3 className="text-xl font-semibold text-gray-800">Change Password</h3>
                                <Button icon="pi pi-times"
                                    className="p-button-rounded p-button-outlined p-button-danger"
                                    onClick={() => setIsEditingPassword(false)} />
                            </div>
                            <div className="grid grid-cols-1 gap-6">
                                    <div className="field bg-white p-4 rounded-lg">
                                    <label className="text-sm font-medium text-gray-700 block mb-2">Current Password</label>
                                    <Password
                                        value={currentPassword}
                                        onChange={(e) => setCurrentPassword(e.target.value)}
                                        toggleMask
                                        className={`w-full ${errors.current_password ? 'p-invalid' : ''}`}
                                        inputClassName="w-full p-inputtext-sm p-3 rounded-lg border border-gray-300"
                                        feedback={false}
                                    />
                                    {errors.current_password && <small className="p-error block mt-1">{errors.current_password}</small>}
                                </div>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="field bg-white p-4 rounded-lg">
                                        <label className="text-sm font-medium text-gray-700 block mb-2">New Password</label>
                                        <Password
                                            value={newPassword}
                                            onChange={(e) => setNewPassword(e.target.value)}
                                            toggleMask
                                            className={`w-full ${errors.new_password ? 'p-invalid' : ''}`}
                                            inputClassName="w-full p-inputtext-sm p-3 rounded-lg border border-gray-300"
                                        />
                                        {errors.new_password && <small className="p-error block mt-1">{errors.new_password}</small>}
                                    </div>
                                        <div className="field bg-white p-4 rounded-lg">
                                        <label className="text-sm font-medium text-gray-700 block mb-2">Confirm Password</label>
                                        <Password
                                            value={confirmPassword}
                                            onChange={(e) => setConfirmPassword(e.target.value)}
                                            toggleMask
                                            className={`w-full ${errors.confirmPassword || (newPassword !== confirmPassword) ? 'p-invalid' : ''}`}
                                            inputClassName="w-full p-inputtext-sm p-3 rounded-lg border border-gray-300"
                                        />
                                        {errors.confirmPassword && <small className="p-error block mt-1">{errors.confirmPassword}</small>}
                                    </div>
                                </div>
                            </div>
                            <div className="flex gap-3 justify-end mt-6">
                                <Button label="Cancel" severity="secondary" className="p-button-sm"
                                    onClick={() => setIsEditingPassword(false)} />
                                <Button label="Update Password" className="p-button-sm main-btn"
                                    onClick={handleChangePassword}
                                    loading={loading} />
                            </div>
                        </>
                    )}
                </div>
            </div>
            </div>
        </section>
    );
}

export default SettingsIndex;
