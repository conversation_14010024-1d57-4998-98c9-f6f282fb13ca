import React, { useState } from 'react';
import { Dialog } from 'primereact/dialog';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Tag } from 'primereact/tag';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { Dropdown } from 'primereact/dropdown';
import { ConfirmDialog } from 'primereact/confirmdialog';
import { motion } from 'framer-motion';
import { FiUsers, FiInfo, FiPlus, FiUserPlus } from 'react-icons/fi';
import { FaBluetooth, FaCreditCard } from 'react-icons/fa';
import { LuNfc } from 'react-icons/lu';
import { useLayout } from '@contexts/LayoutContext';
import axiosInstance from '../../../../config/Axios';
import { mockEventCardTypes } from '@data/mockEventGroupsData';
import GroupDetailModal from './GroupDetailModal';
import AttendeesModal from './AttendeesModal';

const AssociatedGroupsModal = ({ visible, onHide, event, onUpdateEvent }) => {
    const { isMobile } = useLayout();

    // State for modals and data
    const [showCreateGroupModal, setShowCreateGroupModal] = useState(false);
    const [showCreateMemberModal, setShowCreateMemberModal] = useState(false);
    const [selectedGroupForMembers, setSelectedGroupForMembers] = useState(null);
    const [showGroupDetailModal, setShowGroupDetailModal] = useState(false);
    const [selectedGroupForDetails, setSelectedGroupForDetails] = useState(null);
    const [showAttendeesModal, setShowAttendeesModal] = useState(false);
    const [loading, setLoading] = useState(false);

    if (!event) return null;

    const groups = event.associatedGroups || [];

    // Handle opening group detail modal or attendees modal
    const handleGroupClick = (group) => {
        if (group.is_default_attendees) {
            // Open attendees modal for the default attendees group
            setShowAttendeesModal(true);
        } else {
            // Open regular group detail modal for other groups
            setSelectedGroupForDetails(group);
            setShowGroupDetailModal(true);
        }
    };

    // Handle creating new event group
    const handleCreateEventGroup = async (groupData) => {
        try {
            setLoading(true);

            const payload = {
                event_id: event.id,
                title: groupData.title,
                description: groupData.description,
                card_type_name: groupData.cardType,
                access_level: groupData.accessLevel,
                template_id: groupData.templateId,
                status: 'active',
                event_context: true
            };

            // Call event-specific API to create new group
            const response = await axiosInstance.post(`/events/${event.id}/groups/create`, payload);
            const newGroup = response.data.group || response.data;

            // Update the event with the new group
            const updatedEvent = {
                ...event,
                associatedGroups: [...groups, newGroup]
            };

            if (onUpdateEvent) {
                onUpdateEvent(updatedEvent);
            }

            setShowCreateGroupModal(false);
            return newGroup;
        } catch (error) {
            console.error('Error creating event group:', error);
            // For development, create a mock group
            const mockGroup = {
                id: `evt-grp-new-${Date.now()}`,
                title: groupData.title,
                description: groupData.description,
                card_type_name: groupData.cardType,
                access_level: groupData.accessLevel,
                template_id: groupData.templateId,
                status: 'active',
                memberCount: 0,
                event_context: true,
                created_at: new Date().toISOString()
            };

            const updatedEvent = {
                ...event,
                associatedGroups: [...groups, mockGroup]
            };

            if (onUpdateEvent) {
                onUpdateEvent(updatedEvent);
            }

            setShowCreateGroupModal(false);
            return mockGroup;
        } finally {
            setLoading(false);
        }
    };

    // Handle creating new event member and adding to group
    const handleCreateEventMember = async (memberData) => {
        try {
            setLoading(true);

            if (!selectedGroupForMembers) return;

            const payload = {
                event_id: event.id,
                group_id: selectedGroupForMembers.id,
                name: memberData.name,
                email: memberData.email || '',
                phone: memberData.phone || '',
                department: memberData.department,
                role: memberData.role,
                access_level: memberData.accessLevel,
                event_context: true
            };

            // Call event-specific API to create new member and add to group
            const response = await axiosInstance.post(`/event-groups/${selectedGroupForMembers.id}/members/create`, payload);
            const newMember = response.data.member || response.data;

            // Update the group's member count in the local state
            const updatedGroups = groups.map(group =>
                group.id === selectedGroupForMembers.id
                    ? { ...group, memberCount: group.memberCount + 1 }
                    : group
            );

            const updatedEvent = {
                ...event,
                associatedGroups: updatedGroups
            };

            if (onUpdateEvent) {
                onUpdateEvent(updatedEvent);
            }

            setShowCreateMemberModal(false);
            setSelectedGroupForMembers(null);
            return newMember;
        } catch (error) {
            console.error('Error creating event member:', error);
            // For development, still update locally even if API fails
            const updatedGroups = groups.map(group =>
                group.id === selectedGroupForMembers.id
                    ? { ...group, memberCount: group.memberCount + 1 }
                    : group
            );

            const updatedEvent = {
                ...event,
                associatedGroups: updatedGroups
            };

            if (onUpdateEvent) {
                onUpdateEvent(updatedEvent);
            }

            setShowCreateMemberModal(false);
            setSelectedGroupForMembers(null);
            return null;
        } finally {
            setLoading(false);
        }
    };

    // Template functions for DataTable columns
    const groupNameTemplate = (rowData) => {
        const isAttendeesGroup = rowData.is_default_attendees || rowData.title === "Attendees";

        return (
            <div
                className="flex items-center gap-3 cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors"
                onClick={() => handleGroupClick(rowData)}
            >
                <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center cursor-pointer transition-colors ${
                        isAttendeesGroup
                            ? 'bg-gradient-to-br from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700'
                            : 'bg-blue-500 hover:bg-blue-600'
                    }`}
                    onClick={(e) => {
                        e.stopPropagation();
                        handleGroupClick(rowData);
                    }}
                >
                    {isAttendeesGroup ? (
                        <FiUsers className="text-white" size={18} />
                    ) : (
                        <span className="text-white font-medium">
                            {rowData.title?.charAt(0)?.toUpperCase()}
                        </span>
                    )}
                </div>
                <div className="flex-1">
                    <div className="flex items-center gap-2">
                        <div className={`font-medium transition-colors ${
                            isAttendeesGroup
                                ? 'text-green-600 hover:text-green-800'
                                : 'text-blue-600 hover:text-blue-800'
                        }`}>
                            {rowData.title}
                        </div>
                        {isAttendeesGroup && (
                            <span
                                className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium"
                                style={{ backgroundColor: '#82f570', color: '#065f46' }}
                            >
                                <FiUsers size={12} />
                                Default
                            </span>
                        )}
                    </div>
                    <div className="text-sm text-gray-500">{rowData.description}</div>
                </div>
            </div>
        );
    };

    const cardTypeTemplate = (rowData) => (
        <Tag
            value={rowData.card_type_name}
            className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm"
        />
    );

    const statusTemplate = (rowData) => {
        const statusColors = {
            active: '#22C55E',
            inactive: '#6B7280',
            pending: '#F59E0B'
        };
        
        return (
            <Tag
                value={rowData.status}
                style={{ backgroundColor: statusColors[rowData.status] || '#6B7280' }}
                className="text-white px-3 py-1 rounded-full text-sm capitalize"
            />
        );
    };

    const memberCountTemplate = (rowData) => (
        <div className="flex items-center gap-2">
            <FiUsers size={14} className="text-gray-500" />
            <span className="text-sm font-medium">
                {rowData.memberCount} {rowData.memberCount === 1 ? 'member' : 'members'}
            </span>
        </div>
    );

    const actionsTemplate = (rowData) => (
        <div className="flex items-center gap-2">
            <Button
                icon={<FiUserPlus size={14} />}
                className="p-button-sm p-button-outlined"
                style={{
                    backgroundColor: 'white',
                    color: 'black',
                    border: '1px solid #d1d5db',
                    padding: '6px 12px',
                    borderRadius: '6px',
                    transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                    e.target.style.transform = 'translateY(-1px)';
                    e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
                }}
                onMouseLeave={(e) => {
                    e.target.style.transform = 'translateY(0)';
                    e.target.style.boxShadow = 'none';
                }}
                onClick={() => {
                    setSelectedGroupForMembers(rowData);
                    setShowCreateMemberModal(true);
                }}
                tooltip="Add Members"
                tooltipOptions={{ position: 'top' }}
            />
        </div>
    );

    // Mobile view for groups
    const MobileGroupsList = () => (
        <div className="space-y-4">
            {groups.map((group) => {
                const isAttendeesGroup = group.is_default_attendees || group.title === "Attendees";

                return (
                    <motion.div
                        key={group.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3 }}
                        className="border border-gray-200 rounded-lg p-4 bg-white"
                    >
                        <div className="flex items-start gap-3 mb-3">
                            <div
                                className={`w-12 h-12 rounded-full flex items-center justify-center cursor-pointer transition-colors ${
                                    isAttendeesGroup
                                        ? 'bg-gradient-to-br from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700'
                                        : 'bg-blue-500 hover:bg-blue-600'
                                }`}
                                onClick={() => handleGroupClick(group)}
                            >
                                {isAttendeesGroup ? (
                                    <FiUsers className="text-white" size={20} />
                                ) : (
                                    <span className="text-white font-bold text-lg">
                                        {group.title?.charAt(0)?.toUpperCase()}
                                    </span>
                                )}
                            </div>
                            <div className="flex-1">
                                <div className="flex items-start justify-between mb-1">
                                    <div className="flex items-center gap-2 flex-wrap">
                                        <h3
                                            className={`font-semibold text-lg cursor-pointer transition-colors ${
                                                isAttendeesGroup
                                                    ? 'text-green-600 hover:text-green-800'
                                                    : 'text-blue-600 hover:text-blue-800'
                                            }`}
                                            onClick={() => handleGroupClick(group)}
                                        >
                                            {group.title}
                                        </h3>
                                        {isAttendeesGroup && (
                                            <span
                                                className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium"
                                                style={{ backgroundColor: '#82f570', color: '#065f46' }}
                                            >
                                                <FiUsers size={10} />
                                                Default
                                            </span>
                                        )}
                                    </div>
                                <Button
                                    icon={<FiUserPlus size={16} />}
                                    className="p-button-sm p-button-outlined"
                                    style={{
                                        backgroundColor: 'white',
                                        color: 'black',
                                        border: '1px solid #d1d5db',
                                        padding: '8px 10px',
                                        borderRadius: '6px',
                                        minWidth: 'auto',
                                        minHeight: '44px', // Better touch target for mobile
                                        transition: 'all 0.2s ease'
                                    }}
                                    onClick={() => {
                                        setSelectedGroupForMembers(group);
                                        setShowCreateMemberModal(true);
                                    }}
                                    tooltip="Add Members"
                                    tooltipOptions={{ position: 'top' }}
                                />
                            </div>
                            <p className="text-sm text-gray-600 mb-2">{group.description}</p>
                            <div className="flex items-center gap-2 mb-2">
                                <Tag
                                    value={group.card_type_name}
                                    className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs"
                                />
                                <Tag
                                    value={group.status}
                                    style={{
                                        backgroundColor: group.status === 'active' ? '#22C55E' : '#6B7280'
                                    }}
                                    className="text-white px-2 py-1 rounded-full text-xs capitalize"
                                />
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-500">
                                <FiUsers size={14} />
                                <span>{group.memberCount} {group.memberCount === 1 ? 'member' : 'members'}</span>
                            </div>
                        </div>
                    </div>
                </motion.div>
                );
            })}
        </div>
    );

    return (
        <Dialog
            header={
                <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-2">
                        <FiUsers className="text-blue-600" size={20} />
                        <span>Associated Groups - {event.name}</span>
                    </div>
                    <Button
                        icon={<FiPlus size={16} />}
                        label={isMobile ? "" : "Add Group"}
                        className="p-button-sm"
                        style={{
                            backgroundColor: 'white',
                            color: 'black',
                            border: '1px solid #d1d5db',
                            padding: isMobile ? '8px' : '6px 12px',
                            borderRadius: '6px',
                            transition: 'all 0.2s ease',
                            minHeight: '44px', // Better touch target for mobile
                            minWidth: isMobile ? '44px' : 'auto'
                        }}
                        onMouseEnter={(e) => {
                            if (!isMobile) {
                                e.target.style.transform = 'translateY(-1px)';
                                e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
                            }
                        }}
                        onMouseLeave={(e) => {
                            if (!isMobile) {
                                e.target.style.transform = 'translateY(0)';
                                e.target.style.boxShadow = 'none';
                            }
                        }}
                        onClick={() => {
                            setShowCreateGroupModal(true);
                        }}
                        tooltip="Add a New Group to Event"
                        tooltipOptions={{ position: 'bottom' }}
                    />
                </div>
            }
            visible={visible}
            style={{
                width: isMobile ? '95vw' : '70vw',
                maxWidth: isMobile ? '95vw' : '1000px',
                height: isMobile ? '90vh' : 'auto',
                maxHeight: '90vh'
            }}
            breakpoints={{
                '960px': '95vw',
                '641px': '95vw'
            }}
            onHide={onHide}
            className="associated-groups-modal"
            contentStyle={{
                height: isMobile ? 'calc(90vh - 60px)' : 'auto',
                overflow: 'auto',
                padding: isMobile ? '15px' : '20px'
            }}
        >
            <div className="space-y-4">
                {/* Event Info */}
                <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="bg-blue-50 p-4 rounded-lg border border-blue-200"
                >
                    <div className="flex items-center gap-2 mb-2">
                        <FiInfo className="text-blue-600" size={16} />
                        <span className="font-medium text-blue-900">Event Information</span>
                    </div>
                    <div className="text-sm text-blue-800">
                        <p><strong>Event:</strong> {event.name}</p>
                        <p><strong>Location:</strong> {event.location}</p>
                        <p><strong>Date:</strong> {new Date(event.startDate).toLocaleDateString()} - {new Date(event.endDate).toLocaleDateString()}</p>
                        <p><strong>Total Groups:</strong> {groups.length}</p>
                    </div>
                </motion.div>

                {/* Groups Content */}
                {groups.length === 0 ? (
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.3, delay: 0.1 }}
                        className="text-center py-8"
                    >
                        <FiUsers size={48} className="text-gray-300 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-500 mb-2">No Associated Groups</h3>
                        <p className="text-gray-400">This event has no groups associated with it yet.</p>
                    </motion.div>
                ) : (
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: 0.2 }}
                    >
                        {isMobile ? (
                            <MobileGroupsList />
                        ) : (
                            <DataTable
                                value={groups}
                                className="border border-gray-200 rounded-lg"
                                emptyMessage="No groups found"
                                responsiveLayout="stack"
                                breakpoint="960px"
                            >
                                <Column
                                    body={groupNameTemplate}
                                    header="Group Name"
                                    style={{ minWidth: '250px' }}
                                />
                                <Column
                                    body={cardTypeTemplate}
                                    header="Card Type"
                                    style={{ minWidth: '150px' }}
                                />
                                <Column
                                    body={statusTemplate}
                                    header="Status"
                                    style={{ minWidth: '100px' }}
                                />
                                <Column
                                    body={memberCountTemplate}
                                    header="Members"
                                    style={{ minWidth: '120px' }}
                                />
                                <Column
                                    body={actionsTemplate}
                                    header="Actions"
                                    style={{ minWidth: '100px' }}
                                    exportable={false}
                                />
                            </DataTable>
                        )}
                    </motion.div>
                )}
            </div>

            {/* Create Group Modal */}
            <Dialog
                header="Create New Event Group"
                visible={showCreateGroupModal}
                style={{
                    width: isMobile ? '95vw' : '50vw',
                    maxWidth: isMobile ? '95vw' : '600px'
                }}
                breakpoints={{
                    '960px': '80vw',
                    '641px': '95vw'
                }}
                onHide={() => setShowCreateGroupModal(false)}
                className="create-group-modal"
                modal
            >
                <CreateGroupForm
                    eventCardTypes={mockEventCardTypes}
                    loading={loading}
                    onSubmit={handleCreateEventGroup}
                    onCancel={() => setShowCreateGroupModal(false)}
                    isMobile={isMobile}
                />
            </Dialog>

            {/* Create Member Modal */}
            <Dialog
                header={`Add Member to ${selectedGroupForMembers?.title || 'Group'}`}
                visible={showCreateMemberModal}
                style={{
                    width: isMobile ? '95vw' : '50vw',
                    maxWidth: isMobile ? '95vw' : '600px'
                }}
                breakpoints={{
                    '960px': '80vw',
                    '641px': '95vw'
                }}
                onHide={() => {
                    setShowCreateMemberModal(false);
                    setSelectedGroupForMembers(null);
                }}
                className="create-member-modal"
                modal
            >
                <CreateMemberForm
                    loading={loading}
                    onSubmit={handleCreateEventMember}
                    onCancel={() => {
                        setShowCreateMemberModal(false);
                        setSelectedGroupForMembers(null);
                    }}
                    isMobile={isMobile}
                />
            </Dialog>

            {/* Group Detail Modal */}
            <GroupDetailModal
                visible={showGroupDetailModal}
                onHide={() => {
                    setShowGroupDetailModal(false);
                    setSelectedGroupForDetails(null);
                }}
                group={selectedGroupForDetails}
                event={event}
                onUpdateGroup={(updatedGroup) => {
                    // Update the group in the event's associated groups
                    const updatedGroups = groups.map(group =>
                        group.id === updatedGroup.id ? updatedGroup : group
                    );
                    const updatedEvent = {
                        ...event,
                        associatedGroups: updatedGroups
                    };
                    if (onUpdateEvent) {
                        onUpdateEvent(updatedEvent);
                    }
                }}
                onDeleteGroup={(deletedGroupId) => {
                    // Remove the group from the event's associated groups
                    const updatedGroups = groups.filter(group => group.id !== deletedGroupId);
                    const updatedEvent = {
                        ...event,
                        associatedGroups: updatedGroups
                    };
                    if (onUpdateEvent) {
                        onUpdateEvent(updatedEvent);
                    }
                    setShowGroupDetailModal(false);
                    setSelectedGroupForDetails(null);
                }}
                isMobile={isMobile}
            />

            {/* Attendees Modal */}
            <AttendeesModal
                visible={showAttendeesModal}
                onHide={() => setShowAttendeesModal(false)}
                event={event}
                onUpdateEvent={onUpdateEvent}
            />

            {/* Confirmation Dialog */}
            <ConfirmDialog />
        </Dialog>
    );
};



// Hardcoded card types for group creation
const HARDCODED_CARD_TYPES = [
    {
        id: 'card-type-1',
        name: '600 x 400 BT 6Co',
        dimensions: '600 x 400',
        connection: 'BT', // You can return it to being bluetooth if you wish
        colors: '6Co',
        icon: <FaBluetooth size={20} />
    },
    {
        id: 'card-type-2',
        name: '416 x 240 NFC 4Co',
        dimensions: '416 x 240',
        connection: 'NFC',
        colors: '4Co',
        icon: <LuNfc size={20} />
    },
    {
        id: 'card-type-3',
        name: '300 x 400 NFC 4Co',
        dimensions: '300 x 400',
        connection: 'NFC',
        colors: '4Co',
        icon: <LuNfc size={20} />
    },
    {
        id: 'card-type-4',
        name: '300 x 400 NFC 3Co',
        dimensions: '300 x 400',
        connection: 'NFC',
        colors: '3Co',
        icon: <LuNfc size={20} />
    }
];

// Create Group Form Component
const CreateGroupForm = ({ eventCardTypes, loading, onSubmit, onCancel }) => {
    const [formData, setFormData] = useState({
        title: '',
        description: '',
        cardType: '',
        accessLevel: '',
        templateId: ''
    });
    const [errors, setErrors] = useState({});
    const [availableTemplates, setAvailableTemplates] = useState([]);
    const [selectedCardType, setSelectedCardType] = useState(null);

    const accessLevels = [
        { label: 'VIP', value: 'vip' },
        { label: 'Standard', value: 'standard' },
        { label: 'Staff', value: 'staff' },
        { label: 'Media', value: 'media' },
        { label: 'Volunteer', value: 'volunteer' },
        { label: 'Security', value: 'security' },
        { label: 'Facilitator', value: 'facilitator' },
        { label: 'Instructor', value: 'instructor' }
    ];

    // Mock templates - in real app, fetch from API based on card type
    const mockTemplates = [
        { id: 'template-1', name: 'Classic Design', cardType: 'VIP Access' },
        { id: 'template-2', name: 'Modern Design', cardType: 'VIP Access' },
        { id: 'template-3', name: 'Standard Blue', cardType: 'Standard Access' },
        { id: 'template-4', name: 'Standard Green', cardType: 'Standard Access' },
        { id: 'template-5', name: 'Media Badge', cardType: 'Media Pass' },
        { id: 'template-6', name: 'Staff Badge', cardType: 'Staff Access' }
    ];

    const handleInputChange = (field, value) => {
        setFormData(prev => ({ ...prev, [field]: value }));

        // Clear template selection when card type changes
        if (field === 'cardType') {
            setFormData(prev => ({ ...prev, templateId: '' }));
            // Filter templates based on selected card type
            const filteredTemplates = mockTemplates.filter(template => template.cardType === value);
            setAvailableTemplates(filteredTemplates);
        }
    };

    const handleCardTypeSelect = (cardType) => {
        setSelectedCardType(cardType);
        setFormData(prev => ({ ...prev, cardType: cardType.name, templateId: '' }));
        // Filter templates based on selected card type
        const filteredTemplates = mockTemplates.filter(template => template.cardType === cardType.name);
        setAvailableTemplates(filteredTemplates);
        // Clear card type error if it exists
        if (errors.cardType) {
            setErrors(prev => ({ ...prev, cardType: '' }));
        }
    };

    const validateForm = () => {
        const newErrors = {};

        if (!formData.title.trim()) {
            newErrors.title = 'Group name is required';
        }

        if (!formData.description.trim()) {
            newErrors.description = 'Description is required';
        }

        if (!formData.cardType) {
            newErrors.cardType = 'Card type is required';
        }

        if (!formData.accessLevel) {
            newErrors.accessLevel = 'Access level is required';
        }

        // if (!formData.templateId) {
        //     newErrors.templateId = 'Template selection is required';
        // }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = () => {
        if (validateForm()) {
            onSubmit(formData);
        }
    };

    return (
        <div className="space-y-4">
            <div className="text-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Create New Event Group</h3>
                <p className="text-sm text-gray-600">Create a new group specifically for this event</p>
            </div>

            <div className="grid grid-cols-1 gap-4">
                {/* Group Name */}
                <div className="field">
                    <label htmlFor="groupTitle" className="block text-sm font-medium text-gray-700 mb-1">
                        Group Name <span className="text-red-500">*</span>
                    </label>
                    <InputText
                        id="groupTitle"
                        value={formData.title}
                        onChange={(e) => handleInputChange('title', e.target.value)}
                        placeholder="Enter group name"
                        className={`w-full ${errors.title ? 'p-invalid' : ''}`}
                        disabled={loading}
                    />
                    {errors.title && <small className="p-error">{errors.title}</small>}
                </div>

                {/* Description */}
                <div className="field">
                    <label htmlFor="groupDescription" className="block text-sm font-medium text-gray-700 mb-1">
                        Description <span className="text-red-500">*</span>
                    </label>
                    <InputTextarea
                        id="groupDescription"
                        value={formData.description}
                        onChange={(e) => handleInputChange('description', e.target.value)}
                        placeholder="Enter group description"
                        rows={3}
                        className={`w-full ${errors.description ? 'p-invalid' : ''}`}
                        disabled={loading}
                    />
                    {errors.description && <small className="p-error">{errors.description}</small>}
                </div>

                {/* Card Type Selection */}
                <div className="field">
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                        Card Type <span className="text-red-500">*</span>
                    </label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {HARDCODED_CARD_TYPES.map((cardType) => (
                            <motion.div
                                key={cardType.id}
                                className={`
                                    relative p-4 border-2 rounded-lg cursor-pointer transition-all duration-200
                                    ${selectedCardType?.id === cardType.id
                                        ? 'border-blue-500 bg-blue-50 shadow-md'
                                        : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-sm'
                                    }
                                    ${errors.cardType ? 'border-red-300' : ''}
                                `}
                                onClick={() => handleCardTypeSelect(cardType)}
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                            >
                                <div className="flex items-center gap-3">
                                    <div className={`
                                        p-2 rounded-full
                                        ${selectedCardType?.id === cardType.id
                                            ? 'bg-blue-500 text-white'
                                            : 'bg-gray-100 text-gray-600'
                                        }
                                    `}>
                                        {cardType.icon}
                                    </div>
                                    <div className="flex-1">
                                        <h4 className="font-medium text-gray-900">{cardType.name}</h4>
                                        <div className="text-sm text-gray-500 mt-1">
                                            <span>{cardType.dimensions}</span>
                                            <span className="mx-2">•</span>
                                            <span>{cardType.connection}</span>
                                            <span className="mx-2">•</span>
                                            <span>{cardType.colors}</span>
                                        </div>
                                    </div>
                                </div>
                                {selectedCardType?.id === cardType.id && (
                                    <div className="absolute top-2 right-2">
                                        <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                                            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                            </svg>
                                        </div>
                                    </div>
                                )}
                            </motion.div>
                        ))}
                    </div>
                    {errors.cardType && <small className="p-error">{errors.cardType}</small>}
                </div>

                {/* Access Level */}
                <div className="field">
                    <label htmlFor="accessLevel" className="block text-sm font-medium text-gray-700 mb-1">
                        Access Level <span className="text-red-500">*</span>
                    </label>
                    <Dropdown
                        id="accessLevel"
                        value={formData.accessLevel}
                        options={accessLevels}
                        onChange={(e) => handleInputChange('accessLevel', e.value)}
                        optionLabel="label"
                        optionValue="value"
                        placeholder="Select access level"
                        className={`w-full ${errors.accessLevel ? 'p-invalid' : ''}`}
                        disabled={loading}
                    />
                    {errors.accessLevel && <small className="p-error">{errors.accessLevel}</small>}
                </div>

                {/* Template Selection - Only show if card type is selected */}
                {formData.cardType && (
                    <div className="field">
                        <label htmlFor="templateId" className="block text-sm font-medium text-gray-700 mb-1">
                            Design Template
                        </label>
                        <Dropdown
                            id="templateId"
                            value={formData.templateId}
                            options={availableTemplates}
                            onChange={(e) => handleInputChange('templateId', e.value)}
                            optionLabel="name"
                            optionValue="id"
                            placeholder="Select design template"
                            className={`w-full ${errors.templateId ? 'p-invalid' : ''}`}
                            disabled={loading || availableTemplates.length === 0}
                        />
                        {errors.templateId && <small className="p-error">{errors.templateId}</small>}
                        {availableTemplates.length === 0 && formData.cardType && (
                            <small className="text-gray-500 mt-1">
                                No templates available for selected card type.
                            </small>
                        )}
                    </div>
                )}
            </div>

            <div className="flex justify-end gap-2 pt-4">
                <Button
                    label="Cancel"
                    className="p-button-outlined"
                    style={{
                        backgroundColor: 'white',
                        color: 'black',
                        border: '1px solid #d1d5db',
                        padding: '10px 16px',
                        borderRadius: '6px',
                        minHeight: '44px'
                    }}
                    onClick={onCancel}
                    disabled={loading}
                />
                <Button
                    label="Create Group"
                    style={{
                        backgroundColor: '#00c3ac',
                        color: 'white',
                        border: '1px solid #00c3ac',
                        padding: '10px 16px',
                        borderRadius: '6px',
                        minHeight: '44px'
                    }}
                    onClick={handleSubmit}
                    disabled={loading}
                    loading={loading}
                />
            </div>
        </div>
    );
};



// Create Member Form Component
const CreateMemberForm = ({ loading, onSubmit, onCancel }) => {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        phone: '',
        department: '',
        role: '',
        accessLevel: ''
    });
    const [errors, setErrors] = useState({});

    const accessLevels = [
        { label: 'VIP', value: 'vip' },
        { label: 'Standard', value: 'standard' },
        { label: 'Staff', value: 'staff' },
        { label: 'Media', value: 'media' },
        { label: 'Volunteer', value: 'volunteer' },
        { label: 'Security', value: 'security' },
        { label: 'Facilitator', value: 'facilitator' },
        { label: 'Instructor', value: 'instructor' }
    ];

    const handleInputChange = (field, value) => {
        setFormData(prev => ({ ...prev, [field]: value }));
    };

    const validateForm = () => {
        const newErrors = {};

        if (!formData.name.trim()) {
            newErrors.name = 'Name is required';
        }

        // Email is optional - only validate format if provided
        if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
            newErrors.email = 'Email format is invalid';
        }

        if (!formData.department.trim()) {
            newErrors.department = 'Department is required';
        }

        if (!formData.role.trim()) {
            newErrors.role = 'Role is required';
        }

        if (!formData.accessLevel) {
            newErrors.accessLevel = 'Access level is required';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = () => {
        if (validateForm()) {
            onSubmit(formData);
        }
    };

    return (
        <div className="space-y-4">
            <div className="text-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Create New Event Member</h3>
                <p className="text-sm text-gray-600">Add a new member specifically for this event</p>
            </div>

            <div className="grid grid-cols-1 gap-4">
                {/* Name */}
                <div className="field">
                    <label htmlFor="memberName" className="block text-sm font-medium text-gray-700 mb-1">
                        Full Name <span className="text-red-500">*</span>
                    </label>
                    <InputText
                        id="memberName"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        placeholder="Enter full name"
                        className={`w-full ${errors.name ? 'p-invalid' : ''}`}
                        disabled={loading}
                    />
                    {errors.name && <small className="p-error">{errors.name}</small>}
                </div>

                {/* Email */}
                <div className="field">
                    <label htmlFor="memberEmail" className="block text-sm font-medium text-gray-700 mb-1">
                        Email Address
                    </label>
                    <InputText
                        id="memberEmail"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        placeholder="Enter email address (optional)"
                        type="email"
                        className={`w-full ${errors.email ? 'p-invalid' : ''}`}
                        disabled={loading}
                    />
                    {errors.email && <small className="p-error">{errors.email}</small>}
                </div>

                {/* Phone */}
                <div className="field">
                    <label htmlFor="memberPhone" className="block text-sm font-medium text-gray-700 mb-1">
                        Phone Number
                    </label>
                    <InputText
                        id="memberPhone"
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        placeholder="Enter phone number (optional)"
                        type="tel"
                        className="w-full"
                        disabled={loading}
                    />
                </div>

                {/* Department */}
                <div className="field">
                    <label htmlFor="memberDepartment" className="block text-sm font-medium text-gray-700 mb-1">
                        Department <span className="text-red-500">*</span>
                    </label>
                    <InputText
                        id="memberDepartment"
                        value={formData.department}
                        onChange={(e) => handleInputChange('department', e.target.value)}
                        placeholder="Enter department"
                        className={`w-full ${errors.department ? 'p-invalid' : ''}`}
                        disabled={loading}
                    />
                    {errors.department && <small className="p-error">{errors.department}</small>}
                </div>

                {/* Role */}
                <div className="field">
                    <label htmlFor="memberRole" className="block text-sm font-medium text-gray-700 mb-1">
                        Role <span className="text-red-500">*</span>
                    </label>
                    <InputText
                        id="memberRole"
                        value={formData.role}
                        onChange={(e) => handleInputChange('role', e.target.value)}
                        placeholder="Enter role/position"
                        className={`w-full ${errors.role ? 'p-invalid' : ''}`}
                        disabled={loading}
                    />
                    {errors.role && <small className="p-error">{errors.role}</small>}
                </div>

                {/* Access Level */}
                <div className="field">
                    <label htmlFor="memberAccessLevel" className="block text-sm font-medium text-gray-700 mb-1">
                        Access Level <span className="text-red-500">*</span>
                    </label>
                    <Dropdown
                        id="memberAccessLevel"
                        value={formData.accessLevel}
                        options={accessLevels}
                        onChange={(e) => handleInputChange('accessLevel', e.value)}
                        optionLabel="label"
                        optionValue="value"
                        placeholder="Select access level"
                        className={`w-full ${errors.accessLevel ? 'p-invalid' : ''}`}
                        disabled={loading}
                    />
                    {errors.accessLevel && <small className="p-error">{errors.accessLevel}</small>}
                </div>
            </div>

            <div className="flex justify-end gap-2 pt-4">
                <Button
                    label="Cancel"
                    className="p-button-outlined"
                    style={{
                        backgroundColor: 'white',
                        color: 'black',
                        border: '1px solid #d1d5db',
                        padding: '10px 16px',
                        borderRadius: '6px',
                        minHeight: '44px'
                    }}
                    onClick={onCancel}
                    disabled={loading}
                />
                <Button
                    label="Create Member"
                    style={{
                        backgroundColor: '#00c3ac',
                        color: 'white',
                        border: '1px solid #00c3ac',
                        padding: '10px 16px',
                        borderRadius: '6px',
                        minHeight: '44px'
                    }}
                    onClick={handleSubmit}
                    disabled={loading}
                    loading={loading}
                />
            </div>
        </div>
    );
};

export default AssociatedGroupsModal;
