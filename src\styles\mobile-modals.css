/* Mobile-friendly modal styles */

/* Card view dialog mobile styles */
.badge-view-dialog .p-dialog-content {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 20px !important;
  overflow: visible !important;
}

.mobile-card-dialog .p-dialog-content {
  padding: 10px !important;
  height: calc(90vh - 60px) !important;
  overflow: auto !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: flex-start !important;
}

.mobile-card-dialog .p-dialog-header {
  padding: 10px 15px !important;
  font-size: 16px !important;
}

.mobile-card-dialog .p-dialog {
  margin: 0 !important;
  border-radius: 8px !important;
}

/* Card carousel navigation styles */
.card-carousel-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 400px;
  margin: 0 auto 20px auto;
  padding: 0 10px;
}

.card-carousel-indicators {
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-carousel-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
}

.card-carousel-indicator.active {
  background-color: #3b82f6;
  transform: scale(1.2);
}

.card-carousel-indicator:not(.active) {
  background-color: #d1d5db;
}

.card-carousel-indicator:hover {
  transform: scale(1.1);
}

/* Ensure card content is properly sized on mobile */
@media (max-width: 768px) {
  .mobile-card-dialog .p-dialog-content {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
  }

  /* Card image responsive sizing */
  .mobile-card-dialog img {
    max-width: 80vw !important;
    max-height: 40vh !important;
    object-fit: contain;
  }

  /* Navigation controls mobile spacing */
  .mobile-card-dialog .space-x-4 {
    gap: 8px !important;
  }

  .mobile-card-dialog .space-x-2 {
    gap: 4px !important;
  }

  /* Hide desktop carousel nav on mobile */
  .card-carousel-nav {
    display: none !important;
  }
}

/* Form dialog mobile styles */
.p-dialog .p-dialog-content {
  max-height: calc(90vh - 60px);
  overflow-y: auto;
}

/* Mobile group dialog specific styles */
.mobile-group-dialog .p-dialog-content {
  padding: 15px !important;
  height: calc(90vh - 120px) !important;
  overflow-y: auto !important;
}

.mobile-group-dialog .p-dialog-header {
  padding: 12px 15px !important;
  font-size: 18px !important;
  font-weight: 600 !important;
}

.mobile-group-dialog .p-dialog-footer {
  padding: 15px !important;
  border-top: 1px solid #e5e7eb;
}

/* Mobile form field spacing */
.mobile-group-dialog form .space-y-4 > * + * {
  margin-top: 1.5rem !important;
}

.mobile-group-dialog .p-multiselect,
.mobile-group-dialog .p-dropdown {
  min-height: 48px !important;
}

.mobile-group-dialog .p-inputtext,
.mobile-group-dialog .p-inputtextarea {
  font-size: 16px !important;
  padding: 12px !important;
}

@media (max-width: 768px) {


  /* Form fields mobile layout */
  .p-dialog form .flex-wrap {
    flex-direction: column !important;
  }

  .p-dialog form .w-6\/12 {
    width: 100% !important;
  }

  /* Button layout mobile */
  .p-dialog form .flex.items-end {
    flex-direction: column !important;
    gap: 12px !important;
    padding: 15px !important;
  }
  
  .p-dialog form .flex.items-end .p-button {
    width: 100% !important;
    margin: 0 !important;
  }
}

/* Side menu collapsed styles */
.sidebar-collapsed {
  width: 80px !important;
}

.sidebar-collapsed .menu-item-title {
  display: none;
}

.sidebar-collapsed ul li {
  justify-content: center !important;
  margin-bottom: 16px !important;
}

.sidebar-collapsed ul li span {
  margin: 0 auto !important;
}

/* Burger menu button styles */
.burger-menu-btn {
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.burger-menu-btn:hover {
  background-color: #e5e7eb;
  transform: scale(1.05);
}

/* Active tab styles for collapsed sidebar */
.sidebar-collapsed .active_tab {
  background-color: rgba(59, 130, 246, 0.1);
  border-radius: 8px;
  padding: 8px !important;
}

/* Tooltip for collapsed menu items */
.sidebar-collapsed li {
  position: relative;
}

.sidebar-collapsed li:hover::after {
  content: attr(data-title);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background: #374151;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  margin-left: 8px;
}

.sidebar-collapsed li:hover::before {
  content: '';
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  border: 4px solid transparent;
  border-right-color: #374151;
  margin-left: 4px;
  z-index: 1000;
}

/* Mobile overlay improvements */
@media (max-width: 768px) {
  .mobile-overlay {
    backdrop-filter: blur(2px);
    background-color: rgba(0, 0, 0, 0.5);
  }

  /* Groups mobile list improvements */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Touch-friendly interactions */
  .active\:scale-\[0\.98\]:active {
    transform: scale(0.98);
  }

  /* Mobile action menu improvements */
  .mobile-action-menu {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }

  /* Improved touch targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Mobile search and filter improvements */
  .mobile-search-container input {
    font-size: 16px; /* Prevents zoom on iOS */
    -webkit-appearance: none;
    border-radius: 8px;
  }

  .mobile-search-container select {
    font-size: 16px; /* Prevents zoom on iOS */
    -webkit-appearance: none;
    border-radius: 8px;
  }
}

/* Responsive card sizing */
@media (max-width: 480px) {
  .mobile-card-dialog img {
    max-width: 90vw !important;
    max-height: 35vh !important;
  }

  .mobile-card-dialog .p-dialog-content {
    padding: 5px !important;
  }
}

/* Card centering and layout improvements */
.badge-view-dialog .p-dialog {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.badge-view-dialog .p-dialog-mask {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Card container centering */
.card-container-centered {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 400px;
}

/* 3D card perspective improvements */
.card-3d-container {
  perspective: 1000px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

/* Card flip animation improvements */
.card-flip-container {
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.6s ease-in-out;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Ensure proper card sizing */
.card-front, .card-back {
  backface-visibility: hidden;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.card-back {
  transform: rotateY(180deg);
}

/* Layout expansion styles */
.layout-main-content {
  transition: all 0.3s ease-in-out;
}

.layout-sidebar {
  transition: all 0.3s ease-in-out;
}

/* Ensure main content expands properly when sidebar is collapsed */
.layout-main-expanded {
  width: calc(100% - 80px) !important;
  flex: 1;
}

.layout-main-normal {
  width: 83.333333% !important; /* w-10/12 */
}

/* Sidebar width states */
.sidebar-expanded {
  width: 16.666667% !important; /* w-2/12 */
}

.sidebar-collapsed {
  width: 80px !important; /* w-20 */
}

/* Mobile layout adjustments */
@media (max-width: 768px) {
  .layout-main-content {
    width: 100% !important;
  }

  .layout-sidebar {
    width: 0 !important;
  }

  .layout-sidebar.mobile-open {
    width: 256px !important; /* w-64 */
  }
}
