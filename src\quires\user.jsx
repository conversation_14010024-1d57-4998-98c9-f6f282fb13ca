import { useMutation, useQuery } from 'react-query';
import axiosInstance from '../config/Axios';

import { useDataTableContext } from '@contexts/DataTableContext';
import { useGlobalContext } from '@contexts/GlobalContext';
import { usersTableConfig } from '@constants';
import { handleErrors } from '@utils/helper';

//--------------create user-------------- //
const createUser = async (payload) => {
    const { data } = await axiosInstance.post("/datatable/users/create", payload);

    return data.data;
    
}



export const useCreateUserMutation = () => {
    const { showToast } = useGlobalContext();
    const { setLazyParams, setReload } = useDataTableContext();
 
    return useMutation(createUser, {
        onSuccess: async () => {
            // Refresh data table
            setLazyParams(prev => ({ ...prev, ...usersTableConfig }));
            
            // Force data refresh
            setReload(true);
        },
        onError: (error) => {
            handleErrors(showToast, error);
        }
    })
}

//--------------update user-------------- //
const updateUser = async (payload) => {
    // Check if current user is admin or manager
    const currentUserType = localStorage.getItem('user_type');
    
    if (currentUserType === 'admin' || currentUserType === 'manager') {
        // Use admin API (no password verification required) for both admin and manager
        const { data } = await axiosInstance.put(`/datatable/users/${payload.id}/admin-update`, payload.data);
        return data.data;
    } else {
        // Use regular API (password verification required) for regular users
        const { data } = await axiosInstance.post(`/datatable/users/update/${payload.id}`, payload.data);
        return data.data;
    }
}

export const useUpdateUserMutation = () => {
    const { showToast } = useGlobalContext();
    const { setLazyParams, setReload } = useDataTableContext();
 
    return useMutation(updateUser, {
        onSuccess: async () => {
            // Refresh data table
            setLazyParams(prev => ({ ...prev, ...usersTableConfig }));
            
            // Force data refresh
            setReload(true);
        },
        onError: (error) => {
            handleErrors(showToast, error);
        }
    })
}

//--------------get user-------------- //
const getUser = async (id) => {
    const { data } = await axiosInstance.get(`/users/${id}`);

    return data.data;
}

export const useFetchUser = (id) => {
    const { showToast } = useGlobalContext();
    let { isLoading, data, error, isError } = useQuery('getUser', () => getUser(id));

    if (isError) {
        showToast("error", "Fetch Types ", error.response?.data?.message)
    }

    return { isLoading, data };
}

//--------------delete user-------------- //
const deleteUser = async (payload) => {
    const { data } = await axiosInstance.delete(`/users/${payload.id}`);

    return data;
}

export const useDeleteUserMutation = () => {
    const { showToast } = useGlobalContext();
    const { setLazyParams } = useDataTableContext();

    return useMutation(deleteUser, {
        onSuccess: async () => {
            setLazyParams(prev => ({ ...prev, ...usersTableConfig }))
        },
        onError: (error) => handleErrors(showToast, error)
    })
}