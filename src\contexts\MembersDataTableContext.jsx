import { useContext, createContext, useEffect, useState, useRef } from "react";
import PropTypes from "prop-types";
import { useGetDataTable } from "@quires";

const MembersDataTableContext = createContext({});

export const MembersDataTableProvider = (props) => {
    const getDataTable = useGetDataTable();

    let loadLazyTimeout = null;
    const didMountRef = useRef(null)

    const [reload, setReload] = useState(false);
    const [loading, setLoading] = useState(false);
    const [totalRecords, setTotalRecords] = useState(0);
    const [data, setData] = useState([]);
    const [rawData, setRawData] = useState([]);

    const [lazyParams, setLazyParams] = useState({
        url: "",
        first: 0,
        rows: 10,
        page: 0,
        sortField: 'id',
        sortOrder: 1,
        filters: {},
    });

    const onPage = (event) => {
        event = { ...lazyParams, ...event }
        setLazyParams(event);
    }

    const onSort = (event) => {
        event = { ...lazyParams, ...event }
        setLazyParams(event);
    }

    const onFilter = (event) => {
        event = { ...lazyParams, ...event }
        setLazyParams(event);
    }

    const dataHandler = (event) => {
        event = { ...lazyParams, ...event }
        setLazyParams(event);
    }
    

    const loadLazyData = () => {
        if (lazyParams.url) {
            setLoading(true);

            if (loadLazyTimeout) {
                clearTimeout(loadLazyTimeout);
            }

            // تقليل التأخير لتحسين سرعة الاستجابة
            loadLazyTimeout = setTimeout(async () => {
                await getDataTable.mutateAsync(lazyParams, {
                    onSuccess: async (data) => {
                        setTotalRecords(data.pagination.total);
                        setData(data.data);
                        setRawData(data.data); // تحديث rawData أيضاً
                        setLoading(false);
                    },
                    onError : () => {
                        setLoading(false);
                    }
                })
            }, 100); // تقليل التأخير من 1000ms إلى 100ms
        }
    }

    useEffect(() => {
        if (!didMountRef.current) {
            didMountRef.current = true;
        } else if (lazyParams.url) { // تحميل البيانات فقط إذا كان هناك URL
            loadLazyData();
        }
    }, [lazyParams])

    useEffect(() => {
        if (reload) {
            loadLazyData()
        }
        setReload(false)
    }, [reload])

    // تنظيف البيانات عند إلغاء تحميل المكون فقط
    useEffect(() => {
        return () => {
            console.log('🧹 Cleaning up MembersDataTableContext');
            setData([])
            setRawData([])
            setReload(false)
            setTotalRecords(0)
            setLoading(false)
            if (loadLazyTimeout) {
                clearTimeout(loadLazyTimeout);
            }
        }
    }, [])

    return (
        <MembersDataTableContext.Provider value={{
            totalRecords, setTotalRecords,
            lazyParams, setLazyParams,
            data, setData,
            rawData, setRawData,
            loading, setLoading,
            setReload,
            getDataTable,
            dataHandler,
            onPage,
            onSort,
            onFilter,
            toast: null // إضافة toast كـ null مؤقتاً
        }}>
            {props.children}
        </MembersDataTableContext.Provider>
    )
}

MembersDataTableProvider.propTypes = {
    children: PropTypes.node.isRequired,
};

export const useMembersDataTableContext = () => {
    return useContext(MembersDataTableContext);
} 