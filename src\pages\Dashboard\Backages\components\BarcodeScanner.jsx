import { useState, useEffect, useRef } from 'react';
import { Dialog } from 'primereact/dialog';
import { BrowserMultiFormatReader, BarcodeFormat } from '@zxing/library';
import { motion } from 'framer-motion';
import './BarcodeScanner.css';

const BarcodeScanner = ({ isOpen, onClose, onBarcodeScanned }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [scannedResult, setScannedResult] = useState('');
  const videoRef = useRef(null);
  const codeReaderRef = useRef(null);
  const streamRef = useRef(null);

  useEffect(() => {
    if (isOpen) {
      initializeScanner();
    } else {
      cleanup();
    }

    return () => cleanup();
  }, [isOpen]);

  const initializeScanner = async () => {
    setIsLoading(true);
    setError(null);
    setScannedResult('');

    try {
      // Initialize the code reader with Code 93 format only
      codeReaderRef.current = new BrowserMultiFormatReader();

      // Get video input devices
      const videoInputDevices = await codeReaderRef.current.listVideoInputDevices();

      if (videoInputDevices.length === 0) {
        throw new Error('No camera devices found');
      }

      // Use the first available camera
      const selectedDeviceId = videoInputDevices[0].deviceId;

      // Get user media
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          deviceId: selectedDeviceId,
          width: { ideal: 640 },
          height: { ideal: 480 }
        }
      });

      streamRef.current = stream;

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.play();
      }

      // Start decoding with Code 93 format only
      codeReaderRef.current.decodeFromVideoDevice(
        selectedDeviceId,
        videoRef.current,
        (result, error) => {
          if (result) {
            // Check if the result is Code 93 format
            if (result.getBarcodeFormat() === BarcodeFormat.CODE_93) {
              const scannedText = result.getText();
              setScannedResult(scannedText);
              onBarcodeScanned(scannedText);
              cleanup();
            }
            // Ignore other formats silently
          }

          if (error && error.name !== 'NotFoundException') {
            console.error('Barcode scanning error:', error);
          }
        }
      );

      setIsLoading(false);
    } catch (err) {
      console.error('Scanner initialization error:', err);
      
      // Handle specific errors
      if (err.name === 'NotFoundError') {
        // Silently ignore - no camera available
        onClose();
        return;
      } else if (err.name === 'NotAllowedError') {
        setError('Camera access denied. Please allow camera access and try again.');
      } else if (err.name === 'NotReadableError') {
        setError('Camera is already in use by another application.');
      } else {
        setError(err.message || 'Failed to initialize camera');
      }
      
      setIsLoading(false);
    }
  };

  const cleanup = () => {
    // Stop the code reader
    if (codeReaderRef.current) {
      codeReaderRef.current.reset();
      codeReaderRef.current = null;
    }

    // Stop the video stream
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    // Clear video element
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
  };

  const handleClose = () => {
    cleanup();
    onClose();
  };

  return (
    <Dialog
      header={
        <div className="flex items-center gap-3">
          <div className="w-1 h-8 bg-gradient-to-b from-[#00c3ac] to-[#02aa96] rounded-full"></div>
          <h2 className="text-xl font-semibold text-gray-900">Scan Barcode</h2>
        </div>
      }
      visible={isOpen}
      onHide={handleClose}
      style={{
        width: "90vw",
        maxWidth: "600px",
        borderRadius: "16px",
        overflow: "hidden"
      }}
      modal
      className="barcode-scanner-dialog"
      contentClassName="p-0"
    >
      <div className="p-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="space-y-4"
        >
          {/* Instructions */}
          <div className="text-center mb-4">
            <p className="text-gray-600 text-sm">
              Position a Code 93 barcode within the camera view to scan
            </p>
          </div>

          {/* Camera View */}
          <div className="relative bg-black rounded-lg overflow-hidden" style={{ aspectRatio: '4/3' }}>
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                <div className="text-center">
                  <div className="w-8 h-8 border-4 border-[#00c3ac] border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                  <p className="text-gray-600 text-sm">Initializing camera...</p>
                </div>
              </div>
            )}

            {error && (
              <div className="absolute inset-0 flex items-center justify-center bg-red-50">
                <div className="text-center p-4">
                  <div className="text-red-500 mb-2">
                    <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <p className="text-red-700 text-sm font-medium">{error}</p>
                </div>
              </div>
            )}

            <video
              ref={videoRef}
              className="w-full h-full object-cover"
              playsInline
              muted
            />

            {/* Scanning overlay */}
            {!isLoading && !error && (
              <div className="absolute inset-0 pointer-events-none">
                <div className="absolute inset-0 border-2 border-[#00c3ac] opacity-50"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-32 border-2 border-[#00c3ac] rounded-lg"></div>
              </div>
            )}
          </div>

          {/* Scanned Result */}
          {scannedResult && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <p className="text-green-800 text-sm font-medium">Scanned Result:</p>
              <p className="text-green-700 font-mono text-sm break-all">{scannedResult}</p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
            <motion.button
              type="button"
              onClick={handleClose}
              className="px-6 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-all duration-200"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Close
            </motion.button>
            
            {error && (
              <motion.button
                type="button"
                onClick={initializeScanner}
                className="px-6 py-3 bg-[#00c3ac] hover:bg-[#02aa96] text-white rounded-lg font-medium transition-all duration-200"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                Retry
              </motion.button>
            )}
          </div>
        </motion.div>
      </div>
    </Dialog>
  );
};

export default BarcodeScanner;
